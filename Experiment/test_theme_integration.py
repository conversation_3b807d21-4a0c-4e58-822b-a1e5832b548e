#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主題系統與 Playwright PDF 整合測試

這個腳本測試新的 Playwright PDF 系統與現有主題系統的兼容性，
確保所有主題的 HTML 輸出都能正確轉換為 PDF。

版本: 1.0.0
作者: 教育材料轉換系統開發團隊
日期: 2024-12-02
"""

import sys
import os
from pathlib import Path

# 添加父目錄到 Python 路徑，以便導入主系統模組
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from main_converter import convert_string
    from playwright_pdf_converter import PlaywrightPDFConverter, PDFOptions
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    print("請確保在正確的目錄中運行此腳本")
    sys.exit(1)

def create_comprehensive_test_content():
    """創建全面的測試內容"""
    return """# Playwright PDF 與主題系統整合測試

## 📋 測試概述

這個文檔用於測試 Playwright PDF 轉換系統與主題化 HTML 的兼容性。

## 🎨 主題化元素測試

### 標題層級測試
#### 四級標題
##### 五級標題
###### 六級標題

### 列表測試

#### 無序列表
- 第一項：基本功能
  - 子項目：數學公式支援
    - 三級項目：LaTeX 渲染
      - 四級項目：MathJax 整合
        - 五級項目：完整支援

#### 有序列表
1. 安裝 Playwright
2. 配置轉換器
3. 測試 PDF 生成
4. 驗證主題兼容性

### 數學公式測試

#### 內聯公式
愛因斯坦質能方程：$E = mc^2$

#### 區塊公式
$$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$

#### 複雜公式
$$\\frac{\\partial^2 u}{\\partial t^2} = c^2 \\nabla^2 u$$

### 填空線測試

請填寫以下答案：

1. Python 的創始人是 ____________________
2. Playwright 是由 ____________________ 開發的
3. PDF 的全稱是 ____________________

### 表格測試

| 主題名稱 | 主色調 | 列表圖標 | 適用場景 |
|----------|--------|----------|----------|
| Default | 藍色 | 🔵 | 正式文檔 |
| Nature | 綠色 | 🌿 | 環境科學 |
| Tech | 紫色 | ⚡ | 程式設計 |
| Space | 深藍 | 🚀 | 天文物理 |
| Sport | 紅色 | ⚽ | 體育健康 |

### 圖片測試

![測試圖片](https://via.placeholder.com/400x200/2563eb/ffffff?text=Playwright+PDF+Test)

### 引用測試

> 這是一個引用區塊，用於測試主題化的引用樣式。
> 
> Playwright 提供了更好的性能和穩定性。

### 代碼測試

```python
# Playwright PDF 轉換示例
from playwright_pdf_converter import PlaywrightPDFConverter

converter = PlaywrightPDFConverter()
result = converter.convert_html_to_pdf(html_content, "output.pdf")
```

### 水平線測試

---

## 🧪 測試結論

如果您能在 PDF 中看到所有上述元素都正確顯示，說明 Playwright PDF 系統與主題系統完美兼容！

### 檢查清單

- [ ] 標題樣式正確
- [ ] 列表圖標顯示
- [ ] 數學公式渲染
- [ ] 填空線樣式
- [ ] 表格格式
- [ ] 圖片顯示
- [ ] 引用樣式
- [ ] 代碼高亮
- [ ] 主題色彩
"""

def test_theme_pdf_conversion(theme_name: str):
    """測試特定主題的 PDF 轉換"""
    print(f"🎨 測試 {theme_name} 主題...")
    
    try:
        # 使用主題系統生成 HTML
        result = convert_string(
            create_comprehensive_test_content(),
            theme=theme_name,
            pdf_mode=True  # 啟用 PDF 優化模式
        )
        
        if not result.success:
            print(f"❌ {theme_name} 主題 HTML 生成失敗: {result.errors}")
            return False
        
        # 使用 Playwright 轉換為 PDF
        converter = PlaywrightPDFConverter()
        
        # 設置 PDF 選項
        options = PDFOptions(
            format="A4",
            margin_top="1.5cm",
            margin_right="1.5cm",
            margin_bottom="2cm",
            margin_left="1.5cm",
            print_background=False,  # PDF 優化：不印背景
            wait_for_mathjax=True,
            mathjax_timeout=60000,
            wait_for_images=True,
            image_timeout=30000,
            navigation_timeout=120000
        )
        
        # 創建 test_output 目錄
        output_dir = Path(__file__).parent / "test_output"
        output_dir.mkdir(exist_ok=True)

        # 生成 PDF
        output_path = output_dir / f"theme_test_{theme_name}.pdf"
        pdf_result = converter.convert_html_to_pdf(
            result.html_content,
            str(output_path),
            options
        )
        
        if output_path.exists():
            print(f"✅ {theme_name} 主題 PDF 生成成功: {pdf_result}")
            return True
        else:
            print(f"❌ {theme_name} 主題 PDF 檔案未生成")
            return False
            
    except Exception as e:
        print(f"❌ {theme_name} 主題測試失敗: {e}")
        return False

def test_all_themes():
    """測試所有主題"""
    themes = ["default", "nature", "tech", "space", "sport"]
    success_count = 0
    
    print("🎯 開始主題系統與 Playwright PDF 整合測試")
    print("=" * 60)
    
    for theme in themes:
        if test_theme_pdf_conversion(theme):
            success_count += 1
        print()  # 空行分隔
    
    print("=" * 60)
    print(f"📊 測試結果: {success_count}/{len(themes)} 個主題通過")
    
    if success_count == len(themes):
        print("🎉 所有主題測試通過！Playwright PDF 系統與主題系統完美兼容。")
        return True
    else:
        print(f"⚠️ 有 {len(themes) - success_count} 個主題測試失敗。")
        return False

def compare_with_original_system():
    """與原系統進行對比測試（如果可用）"""
    print("🔄 進行系統對比測試...")
    
    # 這裡可以添加與原 Puppeteer 系統的對比邏輯
    # 由於我們已經移除了原系統，這裡主要是驗證功能完整性
    
    test_content = """# 對比測試

## 功能驗證

### 數學公式
$f(x) = \\sum_{n=0}^{\\infty} \\frac{f^{(n)}(a)}{n!}(x-a)^n$

### 填空線
答案：____________________

### 表格
| 項目 | 原系統 | 新系統 |
|------|--------|--------|
| 技術棧 | Puppeteer | Playwright |
| 啟動速度 | 慢 | 快 |
| 穩定性 | 良好 | 優秀 |
| 維護性 | 複雜 | 簡單 |
"""
    
    try:
        result = convert_string(test_content, theme="default", pdf_mode=True)
        if result.success:
            converter = PlaywrightPDFConverter()
            output_dir = Path(__file__).parent / "test_output"
            output_dir.mkdir(exist_ok=True)
            output_path = output_dir / "comparison_test.pdf"
            converter.convert_html_to_pdf(result.html_content, str(output_path))
            
            if output_path.exists():
                print("✅ 對比測試通過：新系統功能完整")
                return True
        
        print("❌ 對比測試失敗")
        return False
        
    except Exception as e:
        print(f"❌ 對比測試出錯: {e}")
        return False

def main():
    """主程式"""
    print("🚀 主題系統與 Playwright PDF 整合測試")
    print("版本: 1.0.0")
    print("日期: 2024-12-02")
    print()
    
    success_tests = 0
    total_tests = 2
    
    # 測試所有主題
    if test_all_themes():
        success_tests += 1
    
    print()
    
    # 對比測試
    if compare_with_original_system():
        success_tests += 1
    
    print("\n" + "=" * 60)
    print(f"🏁 整合測試完成: {success_tests}/{total_tests} 通過")
    
    if success_tests == total_tests:
        print("🎉 Playwright PDF 系統已準備好替代原系統！")
        print("\n📁 生成的測試檔案：")
        test_output_dir = Path(__file__).parent / "test_output"
        if test_output_dir.exists():
            test_files = list(test_output_dir.glob("*.pdf"))
            for file in test_files:
                print(f"   📄 test_output/{file.name}")
        else:
            print("   📂 test_output 目錄未找到")
        return True
    else:
        print("⚠️ 部分測試失敗，請檢查上述錯誤信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
