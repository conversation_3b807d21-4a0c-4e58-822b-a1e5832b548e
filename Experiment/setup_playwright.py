#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright PDF 系統安裝和測試腳本

這個腳本用於：
1. 安裝 Playwright 依賴
2. 下載 Chromium 瀏覽器
3. 測試 PDF 轉換功能
4. 驗證與主題系統的兼容性

版本: 1.0.0
作者: 教育材料轉換系統開發團隊
日期: 2024-12-02
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path

def run_command(command, cwd=None, timeout=300):
    """執行命令並返回結果"""
    try:
        print(f"🔄 執行命令: {' '.join(command)}")
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        if result.returncode == 0:
            print(f"✅ 命令執行成功")
            if result.stdout.strip():
                print(f"📤 輸出: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ 命令執行失敗 (返回碼: {result.returncode})")
            if result.stderr.strip():
                print(f"📤 錯誤: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ 命令執行超時 ({timeout}秒)")
        return False
    except Exception as e:
        print(f"❌ 命令執行出錯: {e}")
        return False

def check_nodejs():
    """檢查 Node.js 環境"""
    print("🔍 檢查 Node.js 環境...")
    
    if not run_command(["node", "--version"]):
        print("❌ Node.js 未安裝或不可用")
        print("請先安裝 Node.js: https://nodejs.org/")
        return False
    
    if not run_command(["npm", "--version"]):
        print("❌ npm 未安裝或不可用")
        return False
    
    print("✅ Node.js 環境檢查通過")
    return True

def install_playwright():
    """安裝 Playwright 依賴"""
    print("📦 安裝 Playwright 依賴...")
    
    current_dir = Path(__file__).parent
    
    # 安裝 npm 依賴
    if not run_command(["npm", "install"], cwd=current_dir, timeout=600):
        print("❌ npm 依賴安裝失敗")
        return False
    
    # 安裝 Playwright 瀏覽器
    print("🌐 下載 Chromium 瀏覽器...")
    if not run_command(["npx", "playwright", "install", "chromium"], cwd=current_dir, timeout=600):
        print("❌ Chromium 瀏覽器下載失敗")
        return False
    
    print("✅ Playwright 安裝完成")
    return True

def create_test_html():
    """創建測試 HTML 檔案"""
    test_html = """<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright PDF 轉換測試</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #2563eb;
            border-left: 4px solid #f59e0b;
            padding-left: 15px;
        }
        
        .fill-blank {
            border-bottom: 2px solid #2563eb;
            display: inline-block;
            min-width: 120px;
            margin: 0 8px;
            padding: 2px 0;
        }
        
        .math-block {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e5e7eb;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #2563eb;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Playwright PDF 轉換測試</h1>
    
    <h2>基本功能測試</h2>
    <p>這是一個測試文檔，用於驗證 Playwright PDF 轉換功能是否正常運作。</p>
    
    <h2>數學公式測試</h2>
    <p>內聯數學公式：$E = mc^2$</p>
    
    <div class="math-block">
        <p>區塊數學公式：</p>
        $$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$
    </div>
    
    <h2>填空線測試</h2>
    <p>請填寫答案：Python 的創始人是 <span class="fill-blank">&nbsp;</span>。</p>
    
    <h2>表格測試</h2>
    <table>
        <thead>
            <tr>
                <th>項目</th>
                <th>描述</th>
                <th>狀態</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Playwright 安裝</td>
                <td>安裝 Playwright 和 Chromium</td>
                <td>✅ 完成</td>
            </tr>
            <tr>
                <td>PDF 轉換</td>
                <td>HTML 到 PDF 轉換測試</td>
                <td>🔄 測試中</td>
            </tr>
            <tr>
                <td>主題兼容</td>
                <td>與主題系統的兼容性</td>
                <td>⏳ 待測試</td>
            </tr>
        </tbody>
    </table>
    
    <h2>總結</h2>
    <p>如果您能看到這個 PDF 檔案，說明 Playwright PDF 轉換系統已經成功運作！</p>
</body>
</html>"""
    
    test_file = Path(__file__).parent / "test.html"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_html)
    
    return str(test_file)

def test_pdf_conversion():
    """測試 PDF 轉換功能"""
    print("🧪 測試 PDF 轉換功能...")
    
    current_dir = Path(__file__).parent
    
    # 創建測試 HTML
    test_html_path = create_test_html()
    test_pdf_path = current_dir / "test_output.pdf"
    
    # 測試基本轉換
    test_options = {
        "format": "A4",
        "printBackground": False,
        "waitForMathJax": True,
        "mathJaxTimeout": 60000,
        "margin": {
            "top": "1.5cm",
            "right": "1.5cm",
            "bottom": "2cm",
            "left": "1.5cm"
        }
    }
    
    command = [
        "node",
        "playwright_script.js",
        test_html_path,
        str(test_pdf_path),
        str(test_options).replace("'", '"')
    ]
    
    if run_command(command, cwd=current_dir, timeout=120):
        if test_pdf_path.exists():
            print(f"✅ PDF 轉換測試成功！檔案已生成: {test_pdf_path}")
            return True
        else:
            print("❌ PDF 檔案未生成")
            return False
    else:
        print("❌ PDF 轉換測試失敗")
        return False

def test_python_integration():
    """測試 Python 整合"""
    print("🐍 測試 Python 整合...")
    
    try:
        # 導入轉換器
        from playwright_pdf_converter import PlaywrightPDFConverter, PDFOptions
        
        # 創建簡單的測試 HTML
        test_html = """
        <!DOCTYPE html>
        <html>
        <head><title>Python 整合測試</title></head>
        <body>
            <h1>Python 整合測試</h1>
            <p>這是通過 Python API 生成的 PDF。</p>
        </body>
        </html>
        """
        
        # 創建轉換器
        converter = PlaywrightPDFConverter()
        
        # 轉換為 PDF
        output_path = Path(__file__).parent / "python_test.pdf"
        result = converter.convert_html_to_pdf(test_html, str(output_path))
        
        if output_path.exists():
            print(f"✅ Python 整合測試成功！檔案已生成: {result}")
            return True
        else:
            print("❌ Python 整合測試失敗：PDF 檔案未生成")
            return False
            
    except Exception as e:
        print(f"❌ Python 整合測試失敗: {e}")
        return False

def main():
    """主程式"""
    print("🚀 Playwright PDF 系統安裝和測試")
    print("=" * 50)
    
    success_count = 0
    total_tests = 4
    
    # 1. 檢查 Node.js 環境
    if check_nodejs():
        success_count += 1
    
    # 2. 安裝 Playwright
    if install_playwright():
        success_count += 1
    
    # 3. 測試 PDF 轉換
    if test_pdf_conversion():
        success_count += 1
    
    # 4. 測試 Python 整合
    if test_python_integration():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {success_count}/{total_tests} 通過")
    
    if success_count == total_tests:
        print("🎉 所有測試通過！Playwright PDF 系統已準備就緒。")
        return True
    else:
        print(f"⚠️ 有 {total_tests - success_count} 個測試失敗，請檢查上述錯誤信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
