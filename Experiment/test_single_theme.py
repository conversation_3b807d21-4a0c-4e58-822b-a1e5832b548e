#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
單一主題測試腳本 - 快速驗證修復效果

測試 Nature 主題的所有修復：
1. 檢查清單正確顯示
2. 主題色彩保留
3. 圖片正確顯示
4. Full size PDF 佈局
"""

import sys
import os
from pathlib import Path

# 添加父目錄到 Python 路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from main_converter import convert_string
    from playwright_pdf_converter import PlaywrightPDFConverter, PDFOptions
except ImportError as e:
    print(f"❌ 導入失敗: {e}")
    sys.exit(1)

def create_test_content():
    """創建測試內容"""
    return """# 🌿 Nature 主題修復測試

## 📋 檢查清單測試

### 修復前的問題
- [ ] 檢查清單顯示為 🌿 [] 而不是複選框
- [x] 主題色彩在 PDF 中丟失
- [ ] 圖片無法正確顯示
- [ ] PDF 不是 full size

### 修復後的預期
- [x] 檢查清單顯示為正確的複選框 ☐ ☑
- [x] 保留 Nature 主題的綠色系色彩
- [x] 圖片正確載入和顯示
- [x] PDF 達到 full size 效果

## 🌿 主題列表測試

### 普通列表（應該有 🌿 圖標）
- 第一項：基本功能測試
- 第二項：主題色彩驗證
- 第三項：圖標顯示確認

### 多層級列表
- 主列表項目 🌿
  - 子列表項目 🌱
    - 三級列表項目 🍃
      - 四級列表項目 🌾
        - 五級列表項目 🌸

## 🖼️ 圖片測試

![Nature 主題測試圖片](https://via.placeholder.com/400x200/059669/ffffff?text=Nature+Theme+Fixed)

## 🎨 主題色彩測試

### 標題應該是綠色
### 這個二級標題應該有綠色和橙色的組合

> 這是一個引用區塊，應該有綠色的左邊線和淺綠色背景

## 📊 測試結果驗證

如果修復成功，PDF 中應該看到：

1. ✅ **檢查清單**: 顯示為 ☐ 和 ☑ 而不是 🌿 []
2. ✅ **主題色彩**: 綠色標題、橙色強調色、淺綠背景
3. ✅ **圖片顯示**: 清晰的測試圖片
4. ✅ **Full Size**: PDF 頁面充分利用 A4 空間
5. ✅ **列表圖標**: 普通列表仍然顯示 🌿 圖標

---

**測試完成時間**: 2024-12-02  
**修復版本**: Playwright PDF v1.0.0
"""

def main():
    """主測試函數"""
    print("🧪 Nature 主題修復測試")
    print("=" * 50)
    
    try:
        # 生成主題化 HTML
        print("🔄 生成 Nature 主題 HTML...")
        result = convert_string(
            create_test_content(),
            theme="nature",
            pdf_mode=True
        )
        
        if not result.success:
            print(f"❌ HTML 生成失敗: {result.errors}")
            return False
        
        print("✅ HTML 生成成功")
        
        # 創建輸出目錄
        output_dir = Path(__file__).parent / "test_output"
        output_dir.mkdir(exist_ok=True)
        
        # 配置 PDF 選項（使用修復後的設置）
        options = PDFOptions(
            format="A4",
            margin_top="0.5cm",      # 減少邊距以達到 full size
            margin_right="0.5cm",
            margin_bottom="0.5cm", 
            margin_left="0.5cm",
            print_background=True,   # 保留主題色彩
            wait_for_mathjax=True,
            mathjax_timeout=60000,
            wait_for_images=True,
            image_timeout=30000,
            navigation_timeout=120000
        )
        
        # 轉換為 PDF
        print("🔄 轉換為 PDF...")
        converter = PlaywrightPDFConverter()
        
        output_path = output_dir / "nature_theme_fixed.pdf"
        pdf_result = converter.convert_html_to_pdf(
            result.html_content,
            str(output_path),
            options
        )
        
        if output_path.exists():
            print(f"✅ PDF 生成成功: {pdf_result}")
            print(f"📄 檔案大小: {output_path.stat().st_size / 1024:.1f} KB")
            
            # 驗證修復效果
            print("\n🔍 修復驗證:")
            print("1. ☐ 檢查清單語法 - 請在 PDF 中確認")
            print("2. 🎨 主題色彩 - 請確認綠色系色彩")
            print("3. 🖼️ 圖片顯示 - 請確認圖片正確載入")
            print("4. 📏 Full Size - 請確認頁面佈局充分利用空間")
            
            return True
        else:
            print("❌ PDF 檔案未生成")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 測試完成！請檢查生成的 PDF 檔案驗證修復效果。")
    else:
        print("\n💥 測試失敗！")
    sys.exit(0 if success else 1)
