#!/usr/bin/env node

/**
 * Playwright PDF 轉換腳本
 * 使用 Playwright 將 HTML 轉換為 PDF，支援 MathJax 數學公式和主題化內容
 * 
 * 版本: 1.0.0
 * 作者: 教育材料轉換系統開發團隊
 * 日期: 2024-12-02
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// Logo 處理函數已移除，改為後處理策略

// 解析命令行參數
const args = process.argv.slice(2);
if (args.length < 3) {
    console.error('使用方法: node playwright_script.js <html_file> <pdf_file> <options_json>');
    process.exit(1);
}

const [htmlFile, pdfFile, optionsJson] = args;

// 解析選項
let options;
try {
    options = JSON.parse(optionsJson);
} catch (error) {
    console.error('選項 JSON 解析失敗:', error.message);
    process.exit(1);
}

/**
 * 等待 MathJax 渲染完成
 */
async function waitForMathJax(page, timeout = 60000) {
    try {
        // 等待 MathJax 載入
        await page.waitForFunction(
            () => window.MathJax && window.MathJax.startup && window.MathJax.startup.document,
            { timeout: timeout }
        );
        
        // 等待所有數學公式渲染完成
        await page.waitForFunction(
            () => {
                if (!window.MathJax || !window.MathJax.startup) return false;
                
                // 檢查是否有待處理的渲染任務
                const state = window.MathJax.startup.document.state();
                return state >= 10; // STATE.COMPILED = 10
            },
            { timeout: timeout }
        );
        
        // 額外等待確保渲染完成
        await page.waitForTimeout(1000);
        
        console.log('✅ MathJax 渲染完成');
        return true;
    } catch (error) {
        console.warn('⚠️ MathJax 等待超時或失敗:', error.message);
        return false;
    }
}

/**
 * 等待圖片載入完成
 */
async function waitForImages(page, timeout = 30000) {
    try {
        // 等待所有圖片載入完成
        await page.waitForFunction(
            () => {
                const images = Array.from(document.images);
                console.log(`檢查 ${images.length} 張圖片...`);

                // 檢查每張圖片的載入狀態
                const results = images.map(img => {
                    const isComplete = img.complete && img.naturalWidth > 0;
                    if (!isComplete) {
                        console.log(`圖片未載入: ${img.src}`);
                    }
                    return isComplete;
                });

                return results.every(result => result);
            },
            { timeout: timeout }
        );

        // 額外等待確保圖片渲染完成
        await page.waitForTimeout(2000);

        console.log('✅ 圖片載入完成');
        return true;
    } catch (error) {
        console.warn('⚠️ 圖片載入等待超時:', error.message);

        // 即使超時也檢查一下圖片狀態
        const imageStatus = await page.evaluate(() => {
            const images = Array.from(document.images);
            return images.map(img => ({
                src: img.src,
                complete: img.complete,
                naturalWidth: img.naturalWidth,
                naturalHeight: img.naturalHeight
            }));
        });

        console.log('📊 圖片狀態:', imageStatus);
        return false;
    }
}

/**
 * 等待頁面內容完全載入
 */
async function waitForPageReady(page, options) {
    console.log('🔄 等待頁面內容載入...');
    
    // 等待網路空閒
    await page.waitForLoadState('networkidle');
    
    // 等待 DOM 內容載入
    await page.waitForLoadState('domcontentloaded');
    
    // 等待圖片載入（如果啟用）
    if (options.waitForImages) {
        await waitForImages(page, options.imageTimeout);
    }
    
    // 等待 MathJax 渲染（如果啟用）
    if (options.waitForMathJax) {
        await waitForMathJax(page, options.mathJaxTimeout);
    }
    
    // 額外等待確保所有內容穩定
    await page.waitForTimeout(2000);
    
    console.log('✅ 頁面內容載入完成');
}

/**
 * 主要轉換函數
 */
async function convertToPDF() {
    let browser;
    
    try {
        console.log('🚀 啟動 Playwright 瀏覽器...');
        
        // 啟動瀏覽器
        browser = await chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--no-first-run',
                '--disable-default-apps',
                '--disable-extensions'
            ]
        });
        
        // 創建頁面
        const page = await browser.newPage();
        
        // 設置超時
        page.setDefaultTimeout(options.navigationTimeout || 120000);
        
        // 設置視窗大小（影響 PDF 渲染）
        await page.setViewportSize({ width: 1200, height: 800 });
        
        console.log(`📄 載入 HTML 檔案: ${htmlFile}`);
        
        // 方案D：設置媒體類型為 screen 模式（保持完整樣式）
        await page.emulateMedia({ media: 'screen' });

        // 載入 HTML 檔案
        const htmlPath = path.resolve(htmlFile);
        await page.goto(`file://${htmlPath}`, {
            waitUntil: 'networkidle',
            timeout: options.navigationTimeout || 120000
        });

        // 方案D：注入關鍵 CSS - 修復標題文字消失問題（移除 Logo 相關 CSS）
        await page.addStyleTag({
            content: `
                /* 強制保留所有顏色和樣式 */
                html, body {
                    -webkit-print-color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    color-adjust: exact !important;
                }

                /* 修復標題文字消失問題 - 覆蓋有問題的 CSS */
                .theme-h1 {
                    -webkit-text-fill-color: var(--theme-primary) !important;
                    -webkit-background-clip: initial !important;
                    background-clip: initial !important;
                    background: transparent !important;
                }

                @media print {
                    * {
                        -webkit-print-color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        color-adjust: exact !important;
                    }

                    .theme-h1 {
                        -webkit-text-fill-color: var(--theme-primary) !important;
                        -webkit-background-clip: initial !important;
                        background-clip: initial !important;
                        background: transparent !important;
                    }
                }
            `
        });

        // 等待頁面內容完全載入
        await waitForPageReady(page, options);
        
        console.log(`📑 生成 PDF: ${pdfFile}`);

        // 方案D：回歸 Full Size PDF + 底部預留空間策略
        console.log('🎯 方案D：Full Size PDF 輸出 + 底部預留 Logo 空間');

        // 準備 PDF 選項 - 回歸 Full Size 配置，僅保留底部邊距
        const pdfOptions = {
            path: pdfFile,
            format: options.format || 'A4',
            printBackground: options.printBackground !== false,
            landscape: options.landscape || false,
            scale: options.scale || 1.0,
            preferCSSPageSize: options.preferCSSPageSize !== false,

            // 移除頁首頁尾功能，回歸純淨 PDF
            displayHeaderFooter: false,

            // 最小邊距配置，僅底部預留 Logo 空間
            margin: options.margin || {
                top: '0.5cm',     // 最小頂部邊距
                right: '0.5cm',   // 最小右側邊距
                bottom: '3cm',    // 底部預留 3cm 給後續 Logo 處理
                left: '0.5cm'     // 最小左側邊距
            }
        };

        // 如果指定了自定義尺寸
        if (options.width && options.height) {
            pdfOptions.width = options.width;
            pdfOptions.height = options.height;
            delete pdfOptions.format;
        }

        console.log('📄 PDF 選項:', JSON.stringify(pdfOptions, null, 2));

        // 生成 PDF
        await page.pdf(pdfOptions);
        
        console.log('✅ PDF 轉換成功完成');
        
    } catch (error) {
        console.error('❌ PDF 轉換失敗:', error.message);
        process.exit(1);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

/**
 * 檢查檔案是否存在
 */
function checkFileExists(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`❌ 檔案不存在: ${filePath}`);
        process.exit(1);
    }
}

/**
 * 主程式入口
 */
async function main() {
    console.log('🎯 Playwright PDF 轉換器啟動');
    console.log(`📥 輸入檔案: ${htmlFile}`);
    console.log(`📤 輸出檔案: ${pdfFile}`);
    console.log(`⚙️ 轉換選項:`, JSON.stringify(options, null, 2));
    
    // 檢查輸入檔案
    checkFileExists(htmlFile);
    
    // 確保輸出目錄存在
    const outputDir = path.dirname(pdfFile);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 執行轉換
    await convertToPDF();
}

// 執行主程式
main().catch(error => {
    console.error('❌ 程式執行失敗:', error);
    process.exit(1);
});
