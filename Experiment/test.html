<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Playwright PDF 轉換測試</title>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\(', '\)']],
                displayMath: [['$$', '$$'], ['\[', '\]']]
            },
            svg: {
                fontCache: 'global'
            }
        };
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #2563eb;
            border-left: 4px solid #f59e0b;
            padding-left: 15px;
        }
        
        .fill-blank {
            border-bottom: 2px solid #2563eb;
            display: inline-block;
            min-width: 120px;
            margin: 0 8px;
            padding: 2px 0;
        }
        
        .math-block {
            background-color: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e5e7eb;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #2563eb;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Playwright PDF 轉換測試</h1>
    
    <h2>基本功能測試</h2>
    <p>這是一個測試文檔，用於驗證 Playwright PDF 轉換功能是否正常運作。</p>
    
    <h2>數學公式測試</h2>
    <p>內聯數學公式：$E = mc^2$</p>
    
    <div class="math-block">
        <p>區塊數學公式：</p>
        $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
    </div>
    
    <h2>填空線測試</h2>
    <p>請填寫答案：Python 的創始人是 <span class="fill-blank">&nbsp;</span>。</p>
    
    <h2>表格測試</h2>
    <table>
        <thead>
            <tr>
                <th>項目</th>
                <th>描述</th>
                <th>狀態</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Playwright 安裝</td>
                <td>安裝 Playwright 和 Chromium</td>
                <td>✅ 完成</td>
            </tr>
            <tr>
                <td>PDF 轉換</td>
                <td>HTML 到 PDF 轉換測試</td>
                <td>🔄 測試中</td>
            </tr>
            <tr>
                <td>主題兼容</td>
                <td>與主題系統的兼容性</td>
                <td>⏳ 待測試</td>
            </tr>
        </tbody>
    </table>
    
    <h2>總結</h2>
    <p>如果您能看到這個 PDF 檔案，說明 Playwright PDF 轉換系統已經成功運作！</p>
</body>
</html>