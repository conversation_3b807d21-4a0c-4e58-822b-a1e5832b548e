{"name": "education-material-pdf-generator-playwright", "version": "1.0.0", "description": "PDF generator using <PERSON>wright for educational materials with MathJax support and theme compatibility", "main": "playwright_script.js", "scripts": {"install-deps": "npm install", "install-browsers": "npx playwright install chromium", "setup": "npm install && npx playwright install chromium", "test": "node playwright_script.js test.html test.pdf '{\"format\":\"A4\",\"printBackground\":false}'", "test-with-math": "node playwright_script.js test_math.html test_math.pdf '{\"format\":\"A4\",\"waitForMathJax\":true,\"printBackground\":false}'"}, "dependencies": {"playwright": "^1.40.0"}, "engines": {"node": ">=16.0.0"}, "keywords": ["pdf", "playwright", "mathjax", "education", "html-to-pdf", "themes", "markdown"], "author": "Education Material Converter Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/education-converter/playwright-pdf"}, "bugs": {"url": "https://github.com/education-converter/playwright-pdf/issues"}, "homepage": "https://github.com/education-converter/playwright-pdf#readme"}