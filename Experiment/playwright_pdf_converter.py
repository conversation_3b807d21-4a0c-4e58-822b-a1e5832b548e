#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Playwright PDF 轉換器 - 教育材料轉換系統

基於 Playwright 的現代 HTML to PDF 轉換解決方案，
替代原有的 Puppeteer 系統，提供更好的性能和穩定性。

版本: 1.0.0
作者: 教育材料轉換系統開發團隊
日期: 2024-12-02

主要特性：
- 基於 Playwright 的高性能 PDF 生成
- 完美支援主題化 HTML 輸出
- 優化的數學公式和圖片渲染
- 與現有系統無縫整合
- 簡化的依賴管理
"""

import os
import sys
import json
import tempfile
import subprocess
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFConversionError(Exception):
    """PDF 轉換錯誤"""
    pass


@dataclass
class PDFOptions:
    """PDF 轉換選項配置"""
    
    # 頁面設置
    format: str = "A4"                          # 頁面格式
    width: Optional[str] = None                 # 自定義寬度
    height: Optional[str] = None                # 自定義高度
    
    # 邊距設置（與原系統保持一致）
    margin_top: str = "1.5cm"                   # 上邊距
    margin_right: str = "1.5cm"                 # 右邊距
    margin_bottom: str = "2cm"                  # 下邊距
    margin_left: str = "1.5cm"                  # 左邊距
    
    # 渲染設置
    print_background: bool = True               # 是否列印背景（改為 True 以保留主題色彩）
    landscape: bool = False                     # 是否橫向
    scale: float = 1.0                          # 縮放比例
    
    # 等待設置
    wait_for_mathjax: bool = True               # 等待 MathJax 渲染
    mathjax_timeout: int = 60000                # MathJax 超時（毫秒）
    wait_for_images: bool = True                # 等待圖片載入
    image_timeout: int = 30000                  # 圖片超時（毫秒）
    navigation_timeout: int = 120000            # 導航超時（毫秒）
    
    # 品質設置
    prefer_css_page_size: bool = True           # 優先使用 CSS 頁面尺寸
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "format": self.format,
            "width": self.width,
            "height": self.height,
            "margin": {
                "top": self.margin_top,
                "right": self.margin_right,
                "bottom": self.margin_bottom,
                "left": self.margin_left
            },
            "printBackground": self.print_background,
            "landscape": self.landscape,
            "scale": self.scale,
            "waitForMathJax": self.wait_for_mathjax,
            "mathJaxTimeout": self.mathjax_timeout,
            "waitForImages": self.wait_for_images,
            "imageTimeout": self.image_timeout,
            "navigationTimeout": self.navigation_timeout,
            "preferCSSPageSize": self.prefer_css_page_size
        }


class PlaywrightPDFConverter:
    """
    基於 Playwright 的 PDF 轉換器
    
    特點：
    - 現代化的瀏覽器自動化工具
    - 更快的啟動速度和更穩定的性能
    - 完美支援 MathJax 數學公式
    - 高品質 PDF 輸出
    - 支援現代 CSS 特性
    - 與主題系統完美整合
    """
    
    def __init__(self, node_script_path: Optional[str] = None):
        """
        初始化 PDF 轉換器
        
        Args:
            node_script_path: Node.js 腳本路徑，如果為 None 則使用默認路徑
        """
        self.node_script_path = node_script_path or self._get_default_script_path()
        self.temp_dir = Path(tempfile.gettempdir()) / "playwright_pdf_converter"
        self.temp_dir.mkdir(exist_ok=True)
        
        # 檢查環境
        self._check_environment()
        
        logger.info("PlaywrightPDFConverter 初始化完成")
    
    def _get_default_script_path(self) -> str:
        """獲取默認的 Node.js 腳本路徑"""
        current_dir = Path(__file__).parent
        return str(current_dir / "playwright_script.js")
    
    def _check_environment(self):
        """檢查 Node.js 和 Playwright 環境"""
        try:
            # 檢查 Node.js
            result = subprocess.run(
                ["node", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            if result.returncode != 0:
                raise PDFConversionError("Node.js 未安裝或不可用")
            
            logger.info(f"Node.js 版本: {result.stdout.strip()}")
            
            # 檢查腳本檔案
            if not Path(self.node_script_path).exists():
                raise PDFConversionError(f"Playwright 腳本檔案不存在: {self.node_script_path}")
            
            logger.info("環境檢查通過")
            
        except subprocess.TimeoutExpired:
            raise PDFConversionError("Node.js 檢查超時")
        except FileNotFoundError:
            raise PDFConversionError("Node.js 未安裝")
    
    def convert_html_to_pdf(
        self,
        html_content: str,
        output_path: Optional[str] = None,
        options: Optional[PDFOptions] = None
    ) -> Union[str, bytes]:
        """
        將 HTML 內容轉換為 PDF
        
        Args:
            html_content: HTML 內容字符串
            output_path: 輸出 PDF 檔案路徑，如果為 None 則返回字節數據
            options: PDF 轉換選項
            
        Returns:
            如果指定了 output_path 則返回檔案路徑，否則返回 PDF 字節數據
        """
        try:
            # 設置默認選項
            if options is None:
                options = PDFOptions()
            
            # 創建臨時 HTML 檔案
            temp_html_path = self._create_temp_html_file(html_content)
            
            # 創建臨時輸出路徑
            if output_path:
                pdf_output_path = output_path
            else:
                pdf_output_path = str(self.temp_dir / f"output_{os.getpid()}.pdf")
            
            # 調用 Playwright 腳本進行轉換
            self._call_playwright_script(temp_html_path, pdf_output_path, options)
            
            # 處理結果
            if output_path:
                return output_path
            else:
                # 讀取 PDF 檔案並返回字節數據
                with open(pdf_output_path, 'rb') as f:
                    pdf_bytes = f.read()
                
                # 清理臨時檔案
                os.unlink(pdf_output_path)
                return pdf_bytes
                
        except Exception as e:
            logger.error(f"PDF 轉換失敗: {e}")
            raise PDFConversionError(f"PDF 轉換失敗: {e}")
        
        finally:
            # 清理臨時 HTML 檔案
            if 'temp_html_path' in locals():
                try:
                    os.unlink(temp_html_path)
                except:
                    pass
    
    def _create_temp_html_file(self, html_content: str) -> str:
        """創建臨時 HTML 檔案"""
        temp_html_path = str(self.temp_dir / f"input_{os.getpid()}.html")
        
        with open(temp_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return temp_html_path
    
    def _call_playwright_script(
        self, 
        html_path: str, 
        pdf_path: str, 
        options: PDFOptions
    ):
        """調用 Playwright Node.js 腳本"""
        
        # 準備腳本參數
        script_args = [
            "node",
            self.node_script_path,
            html_path,
            pdf_path,
            json.dumps(options.to_dict())
        ]
        
        try:
            result = subprocess.run(
                script_args,
                capture_output=True,
                text=True,
                timeout=180,  # 3分鐘超時
                cwd=Path(self.node_script_path).parent
            )
            
            if result.returncode != 0:
                error_msg = f"Playwright 腳本執行失敗:\n{result.stderr}"
                logger.error(error_msg)
                raise subprocess.CalledProcessError(result.returncode, script_args, result.stderr)
            
            logger.info("Playwright PDF 轉換成功")
            
        except subprocess.TimeoutExpired:
            raise PDFConversionError("PDF 轉換超時（180秒）")
        except subprocess.CalledProcessError as e:
            raise PDFConversionError(f"Playwright 腳本執行失敗: {e}")


# 便利函數
def convert_html_to_pdf(
    html_content: str,
    output_path: Optional[str] = None,
    options: Optional[PDFOptions] = None
) -> Union[str, bytes]:
    """
    便利函數：將 HTML 轉換為 PDF（使用 PDF 優化設定）
    
    Args:
        html_content: HTML 內容
        output_path: 輸出檔案路徑
        options: 轉換選項
        
    Returns:
        PDF 檔案路徑或字節數據
    """
    converter = PlaywrightPDFConverter()
    
    # 如果沒有提供選項，使用 PDF 優化預設值
    if options is None:
        options = PDFOptions(
            format="A4",
            margin_top="1.5cm",
            margin_right="1.5cm",
            margin_bottom="2cm",
            margin_left="1.5cm",
            print_background=True,   # 改為 True 以保留主題色彩
            wait_for_mathjax=True,
            mathjax_timeout=60000,
            navigation_timeout=120000
        )
    
    return converter.convert_html_to_pdf(
        html_content=html_content,
        output_path=output_path,
        options=options
    )


if __name__ == "__main__":
    # 簡單的測試
    test_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>測試</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #2563eb; }
        </style>
    </head>
    <body>
        <h1>Playwright PDF 轉換測試</h1>
        <p>這是一個測試文檔，用於驗證 Playwright PDF 轉換功能。</p>
    </body>
    </html>
    """
    
    try:
        result = convert_html_to_pdf(test_html, "test_output.pdf")
        print(f"✅ 測試成功，PDF 已生成: {result}")
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
