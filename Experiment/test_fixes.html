<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f0fdf4;
            color: #1f2937;
        }
        
        .container {
            max-width: 100%;
            background: #fefffe;
            padding: 20px;
            margin: 0;
        }
        
        h1 {
            color: #059669;
            border-bottom: 3px solid #059669;
            padding-bottom: 15px;
        }
        
        h2 {
            color: #059669;
            border-left: 6px solid #d97706;
            padding-left: 20px;
            background: linear-gradient(90deg, #d1fae5 0%, transparent 100%);
            padding-top: 10px;
            padding-bottom: 10px;
        }
        
        /* 檢查清單樣式 */
        .task-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .task-list-item {
            margin: 8px 0;
            padding-left: 0;
            position: relative;
        }
        
        .task-list-checkbox {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: #059669;
        }
        
        /* 主題列表樣式 */
        .theme-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .theme-list li {
            margin: 8px 0;
            padding-left: 35px;
            position: relative;
        }
        
        .theme-list li::before {
            content: "🌿 ";
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.2em;
        }
        
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 修復測試文檔</h1>
        
        <h2>📋 檢查清單測試</h2>
        <ul class="task-list">
            <li class="task-list-item">
                <input type="checkbox" class="task-list-checkbox" disabled> 標題樣式正確
            </li>
            <li class="task-list-item">
                <input type="checkbox" class="task-list-checkbox" disabled checked> 列表圖標顯示
            </li>
            <li class="task-list-item">
                <input type="checkbox" class="task-list-checkbox" disabled> 數學公式渲染
            </li>
            <li class="task-list-item">
                <input type="checkbox" class="task-list-checkbox" disabled> 主題色彩顯示
            </li>
        </ul>
        
        <h2>🌿 主題列表測試</h2>
        <ul class="theme-list">
            <li>第一項：基本功能</li>
            <li>第二項：主題色彩</li>
            <li>第三項：圖標顯示</li>
        </ul>
        
        <h2>🖼️ 圖片測試</h2>
        <img src="https://via.placeholder.com/400x200/059669/ffffff?text=Nature+Theme+Test" alt="測試圖片">
        
        <h2>✅ 預期結果</h2>
        <p>如果修復成功，您應該能看到：</p>
        <ul class="theme-list">
            <li>正確的檢查清單複選框（不是 🌿 圖標）</li>
            <li>綠色的主題色彩（Nature 主題）</li>
            <li>正確顯示的圖片</li>
            <li>Full size 的 PDF 頁面佈局</li>
        </ul>
    </div>
</body>
</html>
