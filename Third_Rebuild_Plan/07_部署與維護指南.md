# 部署與維護指南

## 🚀 部署架構概覽

### 部署目標
- 簡化部署流程 (從 30 分鐘縮短到 5 分鐘)
- 減少依賴複雜度 (統一 Python 環境)
- 提高系統穩定性 (99.9% 可用性)
- 降低維護成本 (自動化運維)

### 技術優勢
- **官方 Docker 映像**: 使用 Microsoft Playwright 官方映像
- **統一技術棧**: 純 Python 後端環境
- **自動依賴管理**: Playwright 自動管理瀏覽器
- **容器化部署**: 一致的運行環境

## 🐳 Docker 部署配置

### 基礎 Dockerfile 設計

#### 後端 Dockerfile
```dockerfile
# 使用官方 Playwright Python 映像
FROM mcr.microsoft.com/playwright/python:v1.40.0-jammy

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴 (如需要)
RUN apt-get update && apt-get install -y \
    fonts-noto-cjk \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴文件
COPY requirements.txt .

# 安裝 Python 依賴
RUN pip install --no-cache-dir -r requirements.txt

# 安裝 Playwright 瀏覽器
RUN playwright install chromium

# 複製應用代碼
COPY . .

# 設置環境變數
ENV PYTHONPATH=/app
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright

# 暴露端口
EXPOSE 8000

# 啟動命令
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### 前端 Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 複製 package 文件
COPY package*.json ./

# 安裝依賴
RUN npm ci --only=production

# 複製源代碼
COPY . .

# 構建應用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 啟動命令
CMD ["npm", "start"]
```

### Docker Compose 配置

#### 開發環境 (docker-compose.dev.yml)
```yaml
version: '3.8'

services:
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/__pycache__
    environment:
      - DEBUG=true
      - LOG_LEVEL=debug
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
```

#### 生產環境 (docker-compose.prod.yml)
```yaml
version: '3.8'

services:
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - DEBUG=false
      - LOG_LEVEL=info
      - MAX_WORKERS=4
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://api.yourdomain.com
    restart: always

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    restart: always
```

## ⚙️ 環境配置

### 環境變數設計

#### 後端環境變數
```bash
# 應用配置
DEBUG=false
LOG_LEVEL=info
SECRET_KEY=your-secret-key

# 服務配置
HOST=0.0.0.0
PORT=8000
MAX_WORKERS=4

# Playwright 配置
PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
PLAYWRIGHT_TIMEOUT=30000

# 文件配置
UPLOAD_MAX_SIZE=10485760  # 10MB
TEMP_DIR=/tmp/conversions
CLEANUP_INTERVAL=3600     # 1 hour

# 性能配置
MAX_CONCURRENT_CONVERSIONS=5
CACHE_TTL=3600
```

#### 前端環境變數
```bash
# API 配置
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_MAX_FILE_SIZE=10485760

# 功能開關
NEXT_PUBLIC_ENABLE_BATCH_UPLOAD=true
NEXT_PUBLIC_ENABLE_THEME_PREVIEW=true

# 分析配置
NEXT_PUBLIC_GA_ID=your-ga-id
```

### 配置文件管理

#### 應用配置 (config.py)
```python
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    # 基礎配置
    debug: bool = False
    log_level: str = "info"
    secret_key: str
    
    # 服務配置
    host: str = "0.0.0.0"
    port: int = 8000
    max_workers: int = 4
    
    # Playwright 配置
    playwright_timeout: int = 30000
    max_concurrent_conversions: int = 5
    
    # 文件配置
    upload_max_size: int = 10 * 1024 * 1024  # 10MB
    temp_dir: str = "/tmp/conversions"
    cleanup_interval: int = 3600
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## 🔧 系統監控

### 健康檢查端點

#### 基礎健康檢查
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow(),
        "version": "1.0.0",
        "services": {
            "playwright": await check_playwright_status(),
            "disk_space": check_disk_space(),
            "memory": check_memory_usage()
        }
    }
```

#### 詳細狀態檢查
```python
@app.get("/status")
async def detailed_status():
    return {
        "system": {
            "uptime": get_uptime(),
            "cpu_usage": get_cpu_usage(),
            "memory_usage": get_memory_usage(),
            "disk_usage": get_disk_usage()
        },
        "application": {
            "active_conversions": get_active_conversions(),
            "total_conversions": get_total_conversions(),
            "error_rate": get_error_rate(),
            "average_response_time": get_avg_response_time()
        }
    }
```

### 日誌管理

#### 日誌配置
```python
import logging
from logging.handlers import RotatingFileHandler

# 配置日誌格式
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler(
            'app.log', 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        ),
        logging.StreamHandler()
    ]
)
```

#### 結構化日誌
```python
import structlog

logger = structlog.get_logger()

# 使用示例
logger.info(
    "conversion_started",
    user_id="user123",
    file_name="lesson1.md",
    output_formats=["html", "pdf"],
    theme="nature"
)
```

### 性能監控

#### 指標收集
- 請求響應時間
- 轉換成功率
- 錯誤發生率
- 系統資源使用率
- 併發用戶數

#### 監控工具整合
- **Prometheus**: 指標收集
- **Grafana**: 可視化儀表板
- **AlertManager**: 告警管理

## 🔄 維護操作

### 日常維護任務

#### 自動化清理
```bash
#!/bin/bash
# cleanup.sh - 定期清理腳本

# 清理臨時文件
find /tmp/conversions -type f -mtime +1 -delete

# 清理舊日誌
find /var/log/app -name "*.log.*" -mtime +7 -delete

# 清理 Docker 未使用的映像
docker image prune -f

# 重啟服務 (如需要)
if [ "$1" = "restart" ]; then
    docker-compose restart backend
fi
```

#### 備份策略
```bash
#!/bin/bash
# backup.sh - 備份腳本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/$DATE"

# 創建備份目錄
mkdir -p $BACKUP_DIR

# 備份配置文件
cp -r /app/config $BACKUP_DIR/

# 備份用戶上傳的文件
cp -r /app/uploads $BACKUP_DIR/

# 備份日誌
cp -r /var/log/app $BACKUP_DIR/

# 壓縮備份
tar -czf "/backups/backup_$DATE.tar.gz" $BACKUP_DIR
rm -rf $BACKUP_DIR

# 清理舊備份 (保留 30 天)
find /backups -name "backup_*.tar.gz" -mtime +30 -delete
```

### 更新部署流程

#### 零停機更新
```bash
#!/bin/bash
# deploy.sh - 部署腳本

# 拉取最新代碼
git pull origin main

# 構建新映像
docker-compose build backend

# 滾動更新
docker-compose up -d --no-deps backend

# 等待服務就緒
sleep 30

# 健康檢查
curl -f http://localhost:8000/health || exit 1

echo "部署完成"
```

#### 回滾機制
```bash
#!/bin/bash
# rollback.sh - 回滾腳本

PREVIOUS_VERSION=$1

if [ -z "$PREVIOUS_VERSION" ]; then
    echo "請指定要回滾的版本"
    exit 1
fi

# 切換到指定版本
git checkout $PREVIOUS_VERSION

# 重新構建和部署
docker-compose build backend
docker-compose up -d --no-deps backend

echo "已回滾到版本: $PREVIOUS_VERSION"
```

## 📊 性能優化

### 系統調優

#### Docker 資源限制
```yaml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

#### Playwright 優化
```python
# 瀏覽器實例池
class BrowserPool:
    def __init__(self, max_browsers=3):
        self.max_browsers = max_browsers
        self.browsers = []
        self.semaphore = asyncio.Semaphore(max_browsers)
    
    async def get_browser(self):
        async with self.semaphore:
            # 重用或創建瀏覽器實例
            pass
```

### 緩存策略

#### Redis 緩存配置
```yaml
services:
  redis:
    image: redis:alpine
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
```

#### 應用層緩存
```python
from functools import lru_cache

@lru_cache(maxsize=100)
def get_theme_config(theme_name: str):
    # 緩存主題配置
    pass

@lru_cache(maxsize=50)
def process_image(image_path: str):
    # 緩存圖片處理結果
    pass
```

這個部署和維護指南確保了系統的穩定運行和高效維護，大幅簡化了運維複雜度。
