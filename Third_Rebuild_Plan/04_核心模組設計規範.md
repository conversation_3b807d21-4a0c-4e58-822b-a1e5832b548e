# 核心模組設計規範

## 📋 模組架構概覽

### 核心模組層次結構
```
core/
├── document_processor.py      # 主處理器
├── markdown_parser.py         # Markdown 解析
├── ast_enhancer.py           # AST 增強
├── html_renderer.py          # HTML 渲染
├── pdf_renderer.py           # PDF 渲染
├── theme_engine.py           # 主題引擎
├── playwright_manager.py     # Playwright 管理
└── utils/
    ├── image_processor.py    # 圖片處理
    ├── math_processor.py     # 數學公式處理
    └── error_handler.py      # 錯誤處理
```

## 🔧 核心模組詳細設計

### 1. DocumentProcessor (主處理器)

#### 職責
- 統一的文檔處理入口點
- 協調各個處理階段
- 管理處理流程和錯誤處理

#### 主要方法
- `process_document(markdown_content, options)` - 主處理方法
- `process_async(markdown_content, output_formats)` - 異步並行處理
- `validate_input(content)` - 輸入驗證
- `cleanup_resources()` - 資源清理

#### 輸入參數
- `markdown_content`: 原始 Markdown 文本
- `output_formats`: 輸出格式列表 ['html', 'pdf']
- `theme_name`: 主題名稱
- `options`: 處理選項字典

#### 輸出結果
- `ProcessResult` 對象包含:
  - `html_content`: HTML 輸出 (如果請求)
  - `pdf_bytes`: PDF 二進制數據 (如果請求)
  - `metadata`: 處理元數據
  - `errors`: 錯誤信息列表

### 2. MarkdownParser (Markdown 解析器)

#### 職責
- 解析標準 Markdown 語法
- 識別數學公式語法
- 提取圖片引用
- 識別教育特殊元素

#### 主要方法
- `parse(markdown_text)` - 主解析方法
- `extract_math_formulas(content)` - 提取數學公式
- `extract_images(content)` - 提取圖片引用
- `identify_special_elements(content)` - 識別特殊元素

#### 特殊語法處理
- **數學公式**: `$inline$` 和 `$$display$$`
- **填空線**: `____` (4個或以上下劃線)
- **練習區塊**: 自動識別練習相關內容
- **圖片語法**: `![alt](path|width=400|center)`

#### 輸出格式
- 標準的 markdown-it-py Token 列表
- 附加元數據標記特殊元素

### 3. ASTEnhancer (AST 增強器)

#### 職責
- 為解析後的 Token 添加渲染信息
- 標記主題相關屬性
- 預處理圖片路徑
- 標記 PDF 特殊處理需求

#### 主要方法
- `enhance_tokens(tokens, output_format, theme)` - 主增強方法
- `add_theme_markers(tokens, theme)` - 添加主題標記
- `process_image_paths(tokens)` - 處理圖片路徑
- `mark_pdf_elements(tokens)` - 標記 PDF 特殊元素

#### 增強邏輯
- **圖片節點**: 添加尺寸、對齊信息
- **數學公式**: 標記渲染模式
- **主題元素**: 添加 CSS 類別
- **PDF 優化**: 標記分頁、字體等

### 4. HTMLRenderer (HTML 渲染器)

#### 職責
- 將增強後的 AST 轉換為 HTML
- 應用主題樣式
- 嵌入 MathJax 配置
- 生成響應式佈局

#### 主要方法
- `render(enhanced_tokens, theme)` - 主渲染方法
- `apply_theme_styles(html, theme)` - 應用主題
- `embed_mathjax_config(html)` - 嵌入 MathJax
- `optimize_for_web(html)` - 網頁優化

#### 輸出特性
- 語義化 HTML 結構
- 響應式 CSS 樣式
- MathJax 3.x 配置
- 主題相關的 CSS 類別

### 5. PDFRenderer (PDF 渲染器)

#### 職責
- 生成 PDF 優化的 HTML
- 處理圖片 Base64 轉換
- 配置 PDF 特殊樣式
- 調用 Playwright 生成 PDF

#### 主要方法
- `render_pdf(enhanced_tokens, theme)` - 主渲染方法
- `convert_images_to_base64(html)` - 圖片轉換
- `apply_pdf_styles(html, theme)` - PDF 樣式
- `generate_pdf_with_playwright(html)` - PDF 生成

#### PDF 優化
- A4 頁面格式
- 邊距: 上右左 1.5cm, 下 2cm
- 圖片 Base64 嵌入
- 分頁控制
- 字體嵌入

### 6. ThemeEngine (主題引擎)

#### 職責
- 管理主題配置
- 生成主題相關 CSS
- 創建 SVG 圖標
- 支援主題切換

#### 主要方法
- `load_theme(theme_name)` - 載入主題
- `generate_css(theme_config)` - 生成 CSS
- `create_svg_icons(theme_config)` - 創建 SVG
- `get_theme_list()` - 獲取主題列表

#### 主題配置結構
```python
theme_config = {
    'name': 'Nature',
    'colors': {
        'primary': '#2E7D32',
        'secondary': '#4CAF50',
        'accent': '#8BC34A'
    },
    'fonts': {
        'heading': 'Roboto',
        'body': 'Open Sans'
    },
    'icons': {
        'shape': 'circle',  # circle, hexagon, star, square, badge
        'style': 'filled'
    }
}
```

### 7. PlaywrightManager (Playwright 管理器)

#### 職責
- 管理瀏覽器實例
- 配置 PDF 生成參數
- 等待 MathJax 渲染
- 處理資源清理

#### 主要方法
- `generate_pdf(html_content, options)` - PDF 生成
- `wait_for_mathjax_complete(page)` - 等待 MathJax
- `configure_page(page, options)` - 頁面配置
- `cleanup_browser(browser)` - 瀏覽器清理

#### MathJax 等待策略
```python
async def wait_for_mathjax_complete(page):
    # 等待 MathJax 對象載入
    await page.wait_for_function("typeof MathJax !== 'undefined'")
    
    # 等待所有公式渲染完成
    await page.wait_for_function("""
        () => MathJax.startup && MathJax.startup.document.state() >= 10
    """)
    
    # 額外等待確保完全渲染
    await page.wait_for_timeout(1000)
```

## 🔄 模組間通信協議

### 數據流轉格式

#### 1. 處理選項 (ProcessOptions)
```python
class ProcessOptions:
    output_formats: List[str]  # ['html', 'pdf']
    theme_name: str           # 主題名稱
    image_base_path: str      # 圖片基礎路徑
    pdf_options: dict         # PDF 特殊選項
    custom_css: str           # 自定義 CSS
```

#### 2. 處理結果 (ProcessResult)
```python
class ProcessResult:
    success: bool
    html_content: Optional[str]
    pdf_bytes: Optional[bytes]
    metadata: dict
    errors: List[str]
    warnings: List[str]
    processing_time: float
```

#### 3. 錯誤信息 (ErrorInfo)
```python
class ErrorInfo:
    module: str              # 發生錯誤的模組
    error_type: str          # 錯誤類型
    message: str             # 錯誤信息
    details: dict            # 詳細信息
    recoverable: bool        # 是否可恢復
```

### 接口規範

#### 統一錯誤處理
- 所有模組使用統一的異常類型
- 錯誤信息包含模組名稱和詳細描述
- 支援錯誤恢復和優雅降級

#### 日誌記錄
- 統一的日誌格式和級別
- 關鍵處理步驟的日誌記錄
- 性能指標的記錄

#### 配置管理
- 統一的配置文件格式
- 環境變數支援
- 運行時配置更新

這個設計規範確保了各模組的職責清晰、接口統一，便於開發、測試和維護。
