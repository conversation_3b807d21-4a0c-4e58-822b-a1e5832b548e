# 測試策略與品質保證

## 🎯 測試目標

### 品質標準
- **功能正確性**: 100% 核心功能正常運作
- **PDF 轉換成功率**: 100%
- **數學公式渲染成功率**: ≥90%
- **測試覆蓋率**: ≥85%
- **性能標準**: 單文檔處理 <10 秒

### 測試範圍
- 單元測試 (Unit Testing)
- 整合測試 (Integration Testing)
- 端到端測試 (E2E Testing)
- 性能測試 (Performance Testing)
- 安全測試 (Security Testing)

## 🧪 測試層級設計

### 1. 單元測試 (Unit Tests)

#### 測試範圍
- 各個核心模組的獨立功能
- 工具函數和輔助方法
- 錯誤處理邏輯
- 邊界條件測試

#### 測試模組列表

**MarkdownParser 測試**
- 標準 Markdown 語法解析
- 數學公式識別 (inline 和 display)
- 圖片路徑提取
- 特殊教育語法識別
- 錯誤輸入處理

**ASTEnhancer 測試**
- Token 增強邏輯
- 主題標記添加
- 圖片路徑處理
- PDF 特殊標記

**HTMLRenderer 測試**
- HTML 輸出生成
- 主題樣式應用
- MathJax 配置嵌入
- 響應式佈局生成

**PDFRenderer 測試**
- PDF 優化 HTML 生成
- 圖片 Base64 轉換
- PDF 樣式應用
- Playwright 調用邏輯

**ThemeEngine 測試**
- 主題配置載入
- CSS 生成
- SVG 圖標創建
- 主題切換邏輯

#### 測試數據準備
- 標準 Markdown 測試文件
- 複雜數學公式測試案例
- 各種圖片格式測試
- 邊界條件測試數據
- 錯誤輸入測試案例

### 2. 整合測試 (Integration Tests)

#### 測試場景

**模組間協作測試**
- DocumentProcessor 與各渲染器的協作
- 主題引擎與渲染器的整合
- 錯誤處理機制的端到端測試

**外部依賴整合測試**
- Playwright 瀏覽器啟動和 PDF 生成
- MathJax 載入和渲染
- 文件系統操作
- 圖片處理管道

**API 整合測試**
- 文檔轉換 API 完整流程
- 文件上傳和下載流程
- 主題管理 API
- 錯誤響應格式

#### 測試環境
- 模擬生產環境配置
- Docker 容器環境測試
- 不同操作系統兼容性
- 資源限制環境測試

### 3. 端到端測試 (E2E Tests)

#### 用戶場景測試

**基礎轉換流程**
- 上傳 Markdown 文件
- 選擇輸出格式和主題
- 執行轉換
- 下載結果文件
- 驗證輸出品質

**複雜文檔處理**
- 包含複雜數學公式的文檔
- 大量圖片的文檔
- 多頁長文檔
- 特殊教育元素文檔

**批量處理測試**
- 多文檔同時轉換
- 不同主題批量應用
- 錯誤恢復機制
- 進度追蹤功能

#### 瀏覽器兼容性測試
- Chrome/Chromium
- Firefox
- Safari
- Edge
- 移動端瀏覽器

### 4. 性能測試 (Performance Tests)

#### 性能指標

**響應時間測試**
- 單文檔轉換時間
- 批量轉換時間
- API 響應時間
- 文件上傳下載時間

**負載測試**
- 併發用戶處理能力
- 高頻請求處理
- 長時間運行穩定性
- 記憶體使用監控

**壓力測試**
- 系統極限負載
- 資源耗盡情況處理
- 錯誤恢復能力
- 服務降級機制

#### 性能基準
- 單文檔 (1-5頁): <5 秒
- 複雜文檔 (10-20頁): <10 秒
- 批量處理 (5個文檔): <30 秒
- 併發處理: 支援 10 個同時請求

### 5. 安全測試 (Security Tests)

#### 安全檢查項目

**輸入驗證**
- 惡意 Markdown 內容
- 超大文件上傳
- 特殊字符處理
- 路徑遍歷攻擊

**文件安全**
- 上傳文件類型限制
- 文件大小限制
- 臨時文件清理
- 敏感信息洩露

**API 安全**
- 請求頻率限制
- 參數驗證
- 錯誤信息洩露
- CORS 配置

## 📋 測試用例設計

### 核心功能測試用例

#### 1. Markdown 解析測試
```
測試案例: 基礎 Markdown 語法
輸入: "# 標題\n\n這是**粗體**文字"
預期: 正確解析為標題和粗體文字
```

#### 2. 數學公式測試
```
測試案例: 複雜數學公式
輸入: "$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$"
預期: 正確渲染為數學公式，PDF 中不截斷
```

#### 3. 圖片處理測試
```
測試案例: 相對路徑圖片
輸入: "![測試圖片](./images/test.png)"
預期: HTML 中正確顯示，PDF 中 Base64 嵌入
```

#### 4. 主題應用測試
```
測試案例: 主題切換
輸入: 相同內容 + 不同主題
預期: 生成不同樣式的 HTML 和 PDF
```

### 邊界條件測試用例

#### 1. 空內容處理
```
測試案例: 空 Markdown 文件
輸入: ""
預期: 生成空白但格式正確的 HTML/PDF
```

#### 2. 超大文件處理
```
測試案例: 大型文檔
輸入: 100 頁的 Markdown 文檔
預期: 成功處理或優雅失敗
```

#### 3. 錯誤公式處理
```
測試案例: 語法錯誤的數學公式
輸入: "$$\invalid{formula$$"
預期: 顯示錯誤信息，不影響其他內容
```

## 🔧 測試工具和框架

### Python 測試框架
- **pytest**: 主要測試框架
- **pytest-asyncio**: 異步測試支援
- **pytest-cov**: 測試覆蓋率統計
- **pytest-mock**: Mock 對象支援

### 測試輔助工具
- **factory_boy**: 測試數據生成
- **faker**: 假數據生成
- **requests-mock**: HTTP 請求模擬
- **pytest-benchmark**: 性能測試

### 前端測試工具
- **Jest**: JavaScript 測試框架
- **React Testing Library**: React 組件測試
- **Cypress**: E2E 測試框架
- **Playwright Test**: 瀏覽器自動化測試

## 📊 測試執行策略

### 測試環境配置

#### 開發環境測試
- 本地開發時的快速測試
- 單元測試和基礎整合測試
- 即時反饋和調試

#### CI/CD 環境測試
- 代碼提交時的自動測試
- 完整的測試套件執行
- 測試報告生成

#### 預生產環境測試
- 完整的端到端測試
- 性能和負載測試
- 安全測試驗證

### 測試數據管理

#### 測試文件庫
- 標準 Markdown 測試文件
- 複雜數學公式文檔
- 各種圖片格式文件
- 錯誤案例文件

#### 測試結果基準
- 標準輸出 HTML 文件
- 標準輸出 PDF 文件
- 性能基準數據
- 錯誤處理預期結果

### 持續品質監控

#### 自動化測試
- 每次代碼提交觸發測試
- 定期的完整測試執行
- 性能回歸測試

#### 品質指標追蹤
- 測試通過率趨勢
- 代碼覆蓋率變化
- 性能指標監控
- 錯誤率統計

#### 測試報告
- 詳細的測試結果報告
- 失敗案例分析
- 性能測試報告
- 品質趨勢分析

這個測試策略確保了系統的高品質和穩定性，為用戶提供可靠的服務。
