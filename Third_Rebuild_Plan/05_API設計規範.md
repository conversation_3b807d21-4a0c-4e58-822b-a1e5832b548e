# API 設計規範

## 🌐 API 架構概覽

### RESTful API 設計原則
- 使用標準 HTTP 方法 (GET, POST, PUT, DELETE)
- 統一的響應格式
- 清晰的錯誤處理
- 適當的狀態碼使用

### 基礎 URL 結構
```
Base URL: http://localhost:8000/api/v1
```

## 📋 API 端點設計

### 1. 文檔轉換 API

#### POST /convert
**功能**: 轉換 Markdown 文檔為 HTML/PDF

**請求格式**:
```json
{
  "content": "# 標題\n這是內容...",
  "options": {
    "output_formats": ["html", "pdf"],
    "theme": "nature",
    "image_base_path": "/uploads/images/",
    "pdf_options": {
      "format": "A4",
      "margin": {
        "top": "1.5cm",
        "right": "1.5cm",
        "left": "1.5cm",
        "bottom": "2cm"
      }
    }
  }
}
```

**響應格式**:
```json
{
  "success": true,
  "data": {
    "html_content": "<html>...</html>",
    "pdf_url": "/api/v1/download/abc123.pdf",
    "metadata": {
      "processing_time": 2.5,
      "word_count": 1250,
      "math_formulas": 15,
      "images": 3
    }
  },
  "errors": [],
  "warnings": []
}
```

#### POST /convert/file
**功能**: 上傳文件進行轉換

**請求格式**: multipart/form-data
- `file`: Markdown 文件
- `options`: JSON 格式的轉換選項

**響應格式**: 同 `/convert`

#### POST /convert/batch
**功能**: 批量轉換多個文檔

**請求格式**:
```json
{
  "documents": [
    {
      "name": "lesson1.md",
      "content": "# 課程一...",
      "options": {...}
    },
    {
      "name": "lesson2.md", 
      "content": "# 課程二...",
      "options": {...}
    }
  ],
  "global_options": {
    "theme": "nature",
    "output_formats": ["html", "pdf"]
  }
}
```

**響應格式**:
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "name": "lesson1.md",
        "success": true,
        "html_content": "...",
        "pdf_url": "..."
      },
      {
        "name": "lesson2.md", 
        "success": false,
        "error": "數學公式渲染失敗"
      }
    ],
    "summary": {
      "total": 2,
      "success": 1,
      "failed": 1
    }
  }
}
```

### 2. 主題管理 API

#### GET /themes
**功能**: 獲取可用主題列表

**響應格式**:
```json
{
  "success": true,
  "data": {
    "themes": [
      {
        "name": "default",
        "display_name": "預設主題",
        "description": "簡潔的預設樣式",
        "preview_url": "/api/v1/themes/default/preview"
      },
      {
        "name": "nature",
        "display_name": "自然主題", 
        "description": "綠色自然風格",
        "preview_url": "/api/v1/themes/nature/preview"
      }
    ]
  }
}
```

#### GET /themes/{theme_name}
**功能**: 獲取特定主題詳細信息

**響應格式**:
```json
{
  "success": true,
  "data": {
    "name": "nature",
    "display_name": "自然主題",
    "description": "綠色自然風格主題",
    "config": {
      "colors": {
        "primary": "#2E7D32",
        "secondary": "#4CAF50"
      },
      "fonts": {
        "heading": "Roboto",
        "body": "Open Sans"
      }
    },
    "css_url": "/api/v1/themes/nature/css",
    "preview_url": "/api/v1/themes/nature/preview"
  }
}
```

#### GET /themes/{theme_name}/preview
**功能**: 獲取主題預覽 HTML

**響應**: HTML 內容 (Content-Type: text/html)

### 3. 文件管理 API

#### POST /upload
**功能**: 上傳圖片或其他資源文件

**請求格式**: multipart/form-data
- `files`: 文件列表
- `path`: 目標路徑 (可選)

**響應格式**:
```json
{
  "success": true,
  "data": {
    "uploaded_files": [
      {
        "original_name": "image1.png",
        "stored_name": "abc123.png",
        "url": "/api/v1/files/abc123.png",
        "size": 102400,
        "type": "image/png"
      }
    ]
  }
}
```

#### GET /files/{file_id}
**功能**: 下載文件

**響應**: 文件內容 (適當的 Content-Type)

#### DELETE /files/{file_id}
**功能**: 刪除文件

**響應格式**:
```json
{
  "success": true,
  "message": "文件已刪除"
}
```

### 4. 下載管理 API

#### GET /download/{download_id}
**功能**: 下載生成的 PDF 文件

**響應**: PDF 文件 (Content-Type: application/pdf)

#### GET /download/{download_id}/zip
**功能**: 下載打包的 HTML + PDF 文件

**響應**: ZIP 文件 (Content-Type: application/zip)

### 5. 系統狀態 API

#### GET /health
**功能**: 系統健康檢查

**響應格式**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 3600,
    "services": {
      "playwright": "running",
      "mathjax": "available"
    }
  }
}
```

#### GET /stats
**功能**: 系統統計信息

**響應格式**:
```json
{
  "success": true,
  "data": {
    "total_conversions": 1250,
    "success_rate": 98.5,
    "average_processing_time": 3.2,
    "active_sessions": 5
  }
}
```

## 🔒 認證和安全

### API 金鑰認證 (可選)
```http
Authorization: Bearer your-api-key-here
```

### 請求限制
- 文件大小限制: 10MB
- 請求頻率限制: 100 requests/minute
- 併發處理限制: 5 concurrent requests

### 安全標頭
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

## 📝 錯誤處理

### 標準錯誤響應格式
```json
{
  "success": false,
  "error": {
    "code": "CONVERSION_FAILED",
    "message": "文檔轉換失敗",
    "details": {
      "module": "pdf_renderer",
      "reason": "MathJax 渲染超時"
    }
  },
  "request_id": "req_abc123"
}
```

### 錯誤代碼定義
- `INVALID_INPUT` (400): 輸入參數無效
- `FILE_TOO_LARGE` (413): 文件過大
- `CONVERSION_FAILED` (500): 轉換處理失敗
- `THEME_NOT_FOUND` (404): 主題不存在
- `RATE_LIMIT_EXCEEDED` (429): 請求頻率超限
- `SERVICE_UNAVAILABLE` (503): 服務暫時不可用

### HTTP 狀態碼使用
- `200 OK`: 請求成功
- `201 Created`: 資源創建成功
- `400 Bad Request`: 請求參數錯誤
- `404 Not Found`: 資源不存在
- `413 Payload Too Large`: 文件過大
- `429 Too Many Requests`: 請求頻率超限
- `500 Internal Server Error`: 服務器內部錯誤
- `503 Service Unavailable`: 服務暫時不可用

## 📊 響應時間和性能

### 性能目標
- 單文檔轉換: < 10 秒
- 批量轉換: < 30 秒 (5個文檔)
- 主題列表: < 1 秒
- 文件上傳: < 5 秒

### 緩存策略
- 主題配置: 1 小時
- 生成的 PDF: 24 小時
- 靜態資源: 7 天

### 監控指標
- 請求響應時間
- 轉換成功率
- 錯誤發生率
- 系統資源使用率

這個 API 設計確保了系統的易用性、可擴展性和穩定性，為前端應用和第三方整合提供了完整的接口支援。
