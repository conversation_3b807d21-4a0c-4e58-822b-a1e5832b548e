# 技術架構設計

## 🏗️ 整體架構

### 系統架構圖
```
用戶介面 (Next.js Frontend)
    ↓ HTTP API
後端服務 (FastAPI)
    ↓
核心處理引擎 (Python)
    ↓
    ├─── HTML 渲染器 → HTML 輸出
    └─── PDF 渲染器 → Playwright → PDF 輸出
```

### 架構原則
1. **單一職責** - 每個模組負責特定功能
2. **鬆散耦合** - 模組間通過接口通信
3. **易於測試** - 每個組件可獨立測試
4. **可擴展性** - 支援新功能和新格式

## 🛠️ 技術棧選擇

### 後端技術棧
- **Web 框架**: FastAPI 0.104+
  - 原因: 高性能、自動 API 文檔、異步支援
- **ASGI 服務器**: Uvicorn 0.24+
  - 原因: 高性能異步服務器
- **PDF 生成**: Playwright 1.40+
  - 原因: 純 Python、簡化部署、官方 Docker 支援

### 核心處理庫
- **Markdown 解析**: markdown-it-py 3.0+
  - 原因: 功能完整、擴展性好、性能優秀
- **HTML 處理**: BeautifulSoup4 4.12+
  - 原因: 易用、穩定、廣泛使用
- **圖片處理**: Pillow 10.1+
  - 原因: 功能全面、格式支援廣泛

### 前端技術棧
- **框架**: Next.js 15.3+
  - 原因: React 生態、SSR 支援、開發體驗好
- **UI 庫**: React 19+
  - 原因: 最新特性、性能優化
- **樣式**: Tailwind CSS 4+
  - 原因: 快速開發、一致性好
- **類型安全**: TypeScript 5+
  - 原因: 代碼品質、開發效率

## 🔧 核心模組設計

### 1. 文檔處理器 (DocumentProcessor) - 增強版
**職責**: 統一的文檔處理入口和流式處理協調
**功能**:
- 接收 Markdown 輸入 (支援大文件流式讀取)
- 協調各個處理階段
- 管理輸出格式選擇
- 處理錯誤和異常
- 大文件分塊處理策略
- 記憶體使用監控和控制

### 2. Markdown 解析器 (MarkdownParser)
**職責**: 將 Markdown 轉換為結構化數據
**功能**:
- 解析標準 Markdown 語法
- 識別數學公式
- 處理圖片引用
- 識別教育特殊元素

### 3. AST 處理模組群 (重構為細粒度模組)

#### 3.1 主題標記器 (ThemeMarker)
**職責**: 專門處理主題相關標記
**功能**:
- 添加主題 CSS 類別
- 注入主題配置信息
- 處理主題特定元素

#### 3.2 媒體處理器 (MediaProcessor)
**職責**: 專門處理圖片和媒體文件
**功能**:
- 圖片路徑解析和驗證
- 圖片格式轉換準備
- 媒體文件大小檢查

#### 3.3 數學公式處理器 (MathProcessor)
**職責**: 專門處理數學公式
**功能**:
- 數學公式語法驗證
- 公式渲染模式標記
- 公式複雜度評估

#### 3.4 PDF 優化器 (PDFOptimizer)
**職責**: 專門處理 PDF 特殊需求
**功能**:
- PDF 分頁標記
- 字體嵌入準備
- 佈局優化標記

### 4. HTML 渲染器 (HTMLRenderer)
**職責**: 生成網頁版輸出
**功能**:
- 將 AST 轉換為 HTML
- 應用主題樣式
- 嵌入 MathJax 配置
- 優化響應式佈局

### 5. PDF 渲染器 (PDFRenderer)
**職責**: 生成 PDF 版輸出
**功能**:
- 生成 PDF 優化的 HTML
- 處理圖片 Base64 轉換
- 配置 Playwright 參數
- 等待 MathJax 渲染完成

### 6. 主題引擎 (ThemeEngine)
**職責**: 管理視覺主題
**功能**:
- 載入主題配置
- 生成 CSS 樣式
- 管理 SVG 圖標
- 支援主題切換

### 7. Playwright 資源管理系統 (重構為精細化管理)

#### 7.1 瀏覽器池管理器 (BrowserPoolManager)
**職責**: 精細化瀏覽器實例管理
**功能**:
- 瀏覽器實例池 (最大 3 個實例)
- 實例生命週期管理
- 資源使用監控
- 自動清理和回收

#### 7.2 PDF 生成協調器 (PDFCoordinator)
**職責**: 協調 PDF 生成流程
**功能**:
- 生成任務排隊管理
- 併發控制 (最大 5 個同時任務)
- 進度追蹤和狀態管理
- 超時和錯誤處理

#### 7.3 記憶體監控器 (MemoryMonitor)
**職責**: 監控和管理記憶體使用
**功能**:
- 實時記憶體使用監控
- 記憶體洩漏檢測
- 自動垃圾回收觸發
- 資源使用告警

## 🔄 數據流設計

### 處理流程
1. **輸入階段**
   - 接收 Markdown 文件
   - 驗證文件格式和大小
   - 提取元數據

2. **解析階段**
   - Markdown 語法解析
   - 生成抽象語法樹
   - 識別特殊元素

3. **增強階段**
   - 添加渲染標記
   - 圖片路徑處理
   - 主題信息注入

4. **渲染階段**
   - 並行生成 HTML 和 PDF
   - 應用主題樣式
   - 優化輸出格式

5. **輸出階段**
   - 文件打包
   - 下載鏈接生成
   - 臨時文件清理

### 錯誤處理策略
- **優雅降級**: 部分功能失敗不影響整體
- **詳細日誌**: 記錄每個處理階段的狀態
- **用戶友好**: 提供清晰的錯誤信息
- **自動恢復**: 支援重試和備用方案

## 📦 部署架構

### Docker 容器設計
- **應用容器**: 基於 Playwright 官方映像
- **數據卷**: 臨時文件和緩存存儲
- **網絡**: 前後端服務通信

### 環境配置
- **開發環境**: 本地 Docker Compose
- **測試環境**: 單容器部署
- **生產環境**: 容器編排部署

### 擴展性考慮
- **水平擴展**: 支援多實例部署
- **負載均衡**: API 層面的負載分散
- **緩存策略**: 減少重複處理
- **監控告警**: 系統健康狀態監控

這個架構設計確保了系統的可維護性、可擴展性和高性能，為長期發展提供了堅實的技術基礎。
