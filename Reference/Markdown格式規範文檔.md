# Markdown格式規範文檔

## 文檔概述

本文檔定義了教育材料Markdown格式的完整規範，包含所有支援的Markdown元素、語法要求、教育材料特定格式，以及格式化最佳實踐。此規範旨在確保內容的一致性、可讀性和正確轉換。

## 1. 基礎Markdown語法規範

### 1.1 標題格式

#### 1.1.1 標題層級

支援六個層級的標題，使用井號（#）標記：

```
# 一級標題 - 課程主標題
## 二級標題 - 章節標題  
### 三級標題 - 小節標題
#### 四級標題 - 子主題
##### 五級標題 - 細分主題
###### 六級標題 - 最小主題
```

#### 1.1.2 標題格式要求

**語法規則**：
- 井號後必須有一個空格
- 標題前後建議空一行
- 避免在標題中使用特殊符號
- 標題應簡潔明確，避免過長

**層級使用建議**：
- H1：課程或文檔主標題（每個文檔只使用一次）
- H2：主要章節標題
- H3：小節標題（最常用的分節標題）
- H4-H6：細分主題（謹慎使用，避免層級過深）

### 1.2 段落與換行

#### 1.2.1 段落分隔

**標準段落**：
- 段落間使用空行分隔
- 段落內的文字自動換行
- 避免段落過長，建議控制在150字以內

**強制換行**：
- 使用反斜線（\）實現強制換行
- 避免使用兩個空格的換行方式（兼容性問題）
- 在列表項目內使用反斜線換行

#### 1.2.2 文字格式化

**粗體文字**：
```
**重要內容** 或 __重要內容__
```

**斜體文字**：
```
*強調內容* 或 _強調內容_
```

**粗斜體**：
```
***重要強調*** 或 ___重要強調___
```

**刪除線**：
```
~~已刪除內容~~
```

### 1.3 列表格式

#### 1.3.1 無序列表

**基本語法**：
```
- 項目一
- 項目二
- 項目三
```

**嵌套列表**：
```
- 主項目一
  - 子項目1.1
  - 子項目1.2
- 主項目二
  - 子項目2.1
    - 子子項目2.1.1
```

**格式要求**：
- 使用連字號（-）作為標記符號
- 標記符號後必須有一個空格
- 嵌套使用兩個空格縮排
- 避免混用不同的標記符號

#### 1.3.2 有序列表

**基本語法**：
```
1. 第一項
2. 第二項
3. 第三項
```

**嵌套有序列表**：
```
1. 主要步驟一
   1. 子步驟1.1
   2. 子步驟1.2
2. 主要步驟二
   1. 子步驟2.1
   2. 子步驟2.2
```

**編號規則**：
- 使用阿拉伯數字加點號
- 編號可以不連續，系統會自動修正
- 嵌套列表使用三個空格縮排
- 支援SVG主題化數字顯示

#### 1.3.3 任務列表

**語法格式**：
```
- [ ] 未完成任務
- [x] 已完成任務
- [ ] 另一個未完成任務
```

**使用場景**：
- 學習檢查清單
- 作業完成狀態
- 實驗步驟確認
- 複習要點檢查

## 2. 教育材料特定格式

### 2.1 填空線格式

#### 2.1.1 填空線語法

**短填空線**（4-9個下劃線）：
```
請填寫答案：____
```

**中等填空線**（10-19個下劃線）：
```
請寫出完整答案：______________
```

**長填空線**（20個以上下劃線）：
```
請詳細說明：________________________
```

#### 2.1.2 填空線使用規則

**與水平線區分**：
- 三個下劃線（___）為標準水平線
- 四個以上下劃線為填空線
- 填空線會轉換為可填寫的輸入框樣式

**使用建議**：
- 根據預期答案長度選擇適當的填空線長度
- 在填空線前提供清楚的問題或提示
- 避免在同一行使用多個填空線

### 2.2 練習區塊格式

#### 2.2.1 練習標題標記

**練習區塊識別**：
```
### 練習一：基礎概念
### 練習 2：計算題
### Exercise 3: 應用題
```

**格式要求**：
- 使用三級標題（###）標記練習
- 標題中包含「練習」或「Exercise」關鍵字
- 建議使用編號便於引用

#### 2.2.2 練習內容組織

**題目結構**：
```
### 練習一：選擇題

1. 下列哪個選項是正確的？
   A. 選項A
   B. 選項B  
   C. 選項C
   D. 選項D

2. 請選擇最佳答案：
   A. 答案A
   B. 答案B
```

**答題空間**：
- 選擇題後提供答案填寫區域
- 計算題提供足夠的計算空間
- 問答題提供適當長度的答題區域

### 2.3 表格格式

#### 2.3.1 基本表格語法

**標準表格**：
```
| 標題1 | 標題2 | 標題3 |
|-------|-------|-------|
| 內容1 | 內容2 | 內容3 |
| 內容4 | 內容5 | 內容6 |
```

**對齊控制**：
```
| 左對齊 | 居中對齊 | 右對齊 |
|:-------|:-------:|-------:|
| 內容   |   內容   |   內容 |
```

#### 2.3.2 教育表格特殊格式

**填寫表格**：
```
| 項目 | 學生填寫 | 備註 |
|------|----------|------|
| 姓名 |          |      |
| 學號 |          |      |
| 班級 |          |      |
```

**評分表格**：
```
| 評分項目 | 滿分 | 得分 | 評語 |
|----------|------|------|------|
| 內容完整性 | 20 |      |      |
| 邏輯清晰度 | 20 |      |      |
| 創新性     | 10 |      |      |
```

### 2.4 引用與註釋

#### 2.4.1 引用格式

**基本引用**：
```
> 這是一段引用文字。
> 可以跨越多行。
```

**嵌套引用**：
```
> 第一層引用
>> 第二層引用
>>> 第三層引用
```

**引用來源標註**：
```
> 知識就是力量。
> 
> —— 培根
```

#### 2.4.2 註釋與說明

**腳註格式**：
```
這是需要註釋的內容[^1]。

[^1]: 這是腳註的詳細說明。
```

**參考資料**：
```
詳細內容請參考[教材第三章](link-to-chapter3)。
```

## 3. 多媒體內容格式

### 3.1 圖片格式

#### 3.1.1 基本圖片語法

**標準圖片**：
```
![圖片描述](圖片路徑)
```

**帶標題的圖片**：
```
![圖片描述](圖片路徑 "圖片標題")
```

#### 3.1.2 圖片路徑處理規範

**本地圖片路徑**：
```
![圖片描述](TestFile/photo/圖片名稱.png)
```

**Placeholder URL映射**：
系統自動將placeholder URL映射到實際圖片檔案：
```
![思考與應變](https://placeholder-image.com/thinking-responding)
```
自動轉換為：
```
![思考與應變](../../../TestFile/photo/Gemini_Generated_Image_1.png)
```

**支援的映射URL**：
- `https://placeholder-image.com/student-smile` → `Gemini_Generated_Image_1.png`
- `https://placeholder-image.com/positive-attitude` → `Gemini_Generated_Image_2.png`
- `https://placeholder-image.com/clear-confident-speech` → `Gemini_Generated_Image_3.png`
- `https://placeholder-image.com/thinking-responding` → `Gemini_Generated_Image_1.png`

#### 3.1.3 教育圖片特殊格式

**尺寸控制**：
```
![圖片描述](圖片路徑|width=300)
![圖片描述](圖片路徑|height=200)
![圖片描述](圖片路徑|size=50%)
```

**佈局控制**：
```
![圖片描述](圖片路徑|left)   # 左浮動
![圖片描述](圖片路徑|right)  # 右浮動
![圖片描述](圖片路徑|center) # 居中顯示
```

**組合使用**：
```
![示意圖](diagram.png|width=400|center)
```

### 3.2 程式碼格式

#### 3.2.1 行內程式碼

**基本語法**：
```
使用 `print()` 函數輸出結果。
```

**變數標記**：
```
將值賦給變數 `x`，然後計算 `x + 1`。
```

#### 3.2.2 程式碼區塊

**基本程式碼區塊**：
````
```
這是程式碼區塊
可以包含多行程式碼
```
````

**指定語言的程式碼區塊**：
````
```python
def hello_world():
    print("Hello, World!")
```
````

**程式碼說明**：
````
```python
# 這是一個簡單的函數
def calculate_area(radius):
    return 3.14159 * radius ** 2
```
````

## 4. 特殊元素格式

### 4.1 數學公式

#### 4.1.1 行內數學公式

**基本語法**：
```
計算結果是 $x = \frac{-b \pm \sqrt{b^2-4ac}}{2a}$
```

**簡單數學表達式**：
```
當 $x = 2$ 時，$y = x^2 + 3x + 1 = 11$
```

#### 4.1.2 獨立數學公式

**區塊數學公式**：
```
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$
```

**多行數學公式**：
```
$$
\begin{align}
x + y &= 10 \\
2x - y &= 2
\end{align}
$$
```

### 4.2 水平分隔線

#### 4.2.1 標準水平線

**語法格式**：
```
---
***
___
```

**使用場景**：
- 章節分隔（單獨一條水平線）
- 主題轉換
- 內容區塊分隔

#### 4.2.2 連續水平線統一化規則

**統一化原則**：
- **連續多條水平線**：自動統一為4條，用於提供書寫空間
- **單獨水平線**：保持1條，用於章節分隔

**連續水平線示例**：
```
**我的回答**：
---
---
---
---
```
轉換後提供4條統一的書寫線條。

**單獨分隔線示例**：
```
## 第一章

內容...

---

## 第二章
```
保持單條分隔線用於章節分隔。

#### 4.2.3 與填空線區分

**水平線**（三個符號）：
```
---  # 標準水平分隔線
***  # 強調分隔線
___  # 基礎分隔線
```

**填空線**（四個以上下劃線）：
```
____        # 短填空
__________  # 中填空
__________________  # 長填空
```

#### 4.2.4 顏色統一規範

**螢幕顯示**：
- 填空線和水平線統一使用 `#333` 顏色
- 確保視覺一致性

**PDF列印**：
- 填空線和水平線統一使用 `#000` 顏色
- 確保列印效果清晰

**列表符號隱藏**：
- 包含填空線或水平線的列表項目自動隱藏符號
- 創造純淨的書寫區域

### 4.3 連結格式

#### 4.3.1 基本連結

**行內連結**：
```
請訪問[官方網站](https://example.com)獲取更多資訊。
```

**參考式連結**：
```
詳細內容請參考[教學指南][guide]。

[guide]: https://example.com/guide
```

#### 4.3.2 內部連結

**章節連結**：
```
請參考[第三章：基礎概念](#第三章基礎概念)
```

**圖片連結**：
```
[![圖片描述](圖片路徑)](連結地址)
```

## 5. 格式化最佳實踐

### 5.1 文檔結構組織

#### 5.1.1 標題層級規劃

**層級使用原則**：
- 保持標題層級的邏輯性和一致性
- 避免跳級使用標題（如從H2直接跳到H4）
- 每個章節的標題層級應該平衡
- 使用標題建立清晰的文檔大綱

**標題命名規範**：
- 使用描述性的標題名稱
- 避免使用過於技術性的術語
- 保持標題長度適中
- 使用一致的命名風格

#### 5.1.2 內容組織策略

**段落組織**：
- 每個段落專注於一個主要概念
- 使用過渡句連接相關段落
- 控制段落長度，避免過長或過短
- 使用空行清晰分隔段落

**列表使用**：
- 使用列表組織相關項目
- 保持列表項目的平行結構
- 避免列表嵌套過深
- 適當使用有序和無序列表

### 5.2 教育內容特殊考量

#### 5.2.1 學習目標明確

**目標陳述**：
```
## 學習目標

完成本章學習後，學生應能夠：

1. 理解基本概念的定義
2. 應用相關公式解決問題
3. 分析實際案例中的應用
```

**重點標記**：
```
**重要概念**：這是需要重點掌握的內容。

*注意*：這是需要特別注意的事項。
```

#### 5.2.2 互動元素設計

**思考問題**：
```
> **思考題**：為什麼會出現這種現象？
> 
> 請結合所學知識進行分析。
```

**實踐活動**：
```
### 實踐活動：實驗設計

1. 準備實驗材料
2. 按照步驟進行實驗
3. 記錄實驗結果
4. 分析實驗數據
```

### 5.3 可訪問性考量

#### 5.3.1 圖片可訪問性

**替代文字**：
- 為所有圖片提供有意義的替代文字
- 描述圖片的主要內容和功能
- 避免使用「圖片」、「照片」等無意義描述

**圖片說明**：
```
![流程圖：展示數據處理的五個主要步驟](process-diagram.png)

圖1：數據處理流程圖，包含數據收集、清理、分析、可視化和報告五個步驟。
```

#### 5.3.2 結構可訪問性

**語義化標記**：
- 正確使用標題層級建立文檔結構
- 使用列表組織相關內容
- 使用表格展示結構化數據
- 提供清晰的導航結構

**內容組織**：
- 使用邏輯順序組織內容
- 提供清晰的章節劃分
- 使用一致的格式約定
- 避免依賴顏色傳達重要信息

## 6. 質量檢查清單

### 6.1 格式檢查

**基礎語法檢查**：
- [ ] 標題使用正確的井號格式
- [ ] 列表項目格式一致
- [ ] 連結語法正確
- [ ] 圖片路徑有效

**教育格式檢查**：
- [ ] 填空線長度適當
- [ ] 練習區塊標記清晰
- [ ] 表格結構完整
- [ ] 數學公式語法正確

### 6.2 內容檢查

**結構檢查**：
- [ ] 標題層級邏輯清晰
- [ ] 段落組織合理
- [ ] 內容流暢連貫
- [ ] 重點突出明確

**教育內容檢查**：
- [ ] 學習目標明確
- [ ] 練習題目清楚
- [ ] 答題空間充足
- [ ] 評估標準清晰

### 6.3 技術檢查

**兼容性檢查**：
- [ ] 特殊字符正確顯示
- [ ] 圖片格式支援
- [ ] 連結有效性
- [ ] 跨平台兼容性

**轉換檢查**：
- [ ] HTML轉換正確
- [ ] PDF輸出完整
- [ ] 樣式應用正確
- [ ] 分頁效果合理

## 7. 總結

本Markdown格式規範文檔提供了教育材料創作的完整格式指南。遵循這些規範可以確保內容的一致性、可讀性和正確轉換。建議在創作過程中定期參考此規範，並使用提供的檢查清單進行質量控制。

規範的持續改進將基於實際使用經驗和用戶反饋，以確保其實用性和有效性。
