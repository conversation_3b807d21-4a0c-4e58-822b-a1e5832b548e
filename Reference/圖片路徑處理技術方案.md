# 教育材料轉換系統 - 圖片路徑處理技術方案

## 問題分析

### 核心挑戰
1. **相對路徑依賴**：Markdown中的圖片使用相對路徑，上傳後路徑結構改變
2. **檔案分離**：單檔上傳時圖片檔案可能缺失
3. **路徑解析**：需要正確解析和重寫圖片路徑
4. **PDF嵌入**：WeasyPrint需要能夠訪問圖片檔案

### 技術目標
- 支援單檔和資料夾上傳兩種模式
- 自動檢測和處理圖片依賴
- 確保HTML和PDF中圖片正確顯示
- 提供良好的錯誤處理和用戶反饋

## 整體解決方案架構

### 處理流程圖
```
用戶上傳 → 檔案分析 → 圖片提取 → 路徑重寫 → 轉換處理 → 輸出生成
    ↓         ↓         ↓         ↓         ↓         ↓
  檔案存儲   依賴檢測   圖片存儲   路徑映射   HTML生成   PDF生成
```

### 核心組件
1. **FileAnalyzer** - 分析檔案結構和圖片依賴
2. **ImageProcessor** - 處理圖片檔案和路徑重寫
3. **PathResolver** - 解析和映射圖片路徑
4. **AssetManager** - 管理圖片資源的存儲和訪問

## 詳細技術實現

### 1. 前端檔案上傳增強

#### 檔案結構分析組件
```typescript
// frontend/components/FileUpload/FileAnalyzer.tsx
interface FileStructure {
  markdownFiles: File[];
  imageFiles: File[];
  structure: Map<string, File>;
  dependencies: Map<string, string[]>; // markdown -> image paths
}

export class FileAnalyzer {
  static async analyzeUploadedFiles(files: File[]): Promise<FileStructure> {
    const markdownFiles: File[] = [];
    const imageFiles: File[] = [];
    const structure = new Map<string, File>();
    const dependencies = new Map<string, string[]>();

    // 分類檔案
    for (const file of files) {
      const relativePath = file.webkitRelativePath || file.name;
      structure.set(relativePath, file);

      if (this.isMarkdownFile(file)) {
        markdownFiles.push(file);
        // 分析Markdown中的圖片依賴
        const imagePaths = await this.extractImagePaths(file);
        dependencies.set(relativePath, imagePaths);
      } else if (this.isImageFile(file)) {
        imageFiles.push(file);
      }
    }

    return { markdownFiles, imageFiles, structure, dependencies };
  }

  private static isMarkdownFile(file: File): boolean {
    return file.name.endsWith('.md') || file.name.endsWith('.markdown');
  }

  private static isImageFile(file: File): boolean {
    const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp'];
    return imageExtensions.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
  }

  private static async extractImagePaths(file: File): Promise<string[]> {
    const content = await file.text();
    const imageRegex = /!\[.*?\]\(([^)]+)\)/g;
    const paths: string[] = [];
    let match;

    while ((match = imageRegex.exec(content)) !== null) {
      const imagePath = match[1];
      // 只處理相對路徑，忽略絕對URL
      if (!imagePath.startsWith('http') && !imagePath.startsWith('data:')) {
        paths.push(imagePath);
      }
    }

    return paths;
  }
}
```

#### 增強的檔案上傳組件
```typescript
// frontend/components/FileUpload/EnhancedFileUpload.tsx
import { FileAnalyzer } from './FileAnalyzer';

export const EnhancedFileUpload: React.FC = () => {
  const [analysisResult, setAnalysisResult] = useState<FileStructure | null>(null);
  const [missingImages, setMissingImages] = useState<string[]>([]);

  const handleFilesSelected = async (files: File[]) => {
    const analysis = await FileAnalyzer.analyzeUploadedFiles(files);
    setAnalysisResult(analysis);

    // 檢查缺失的圖片
    const missing = findMissingImages(analysis);
    setMissingImages(missing);
  };

  const findMissingImages = (analysis: FileStructure): string[] => {
    const missing: string[] = [];
    
    for (const [mdPath, imagePaths] of analysis.dependencies) {
      for (const imagePath of imagePaths) {
        const resolvedPath = resolvePath(mdPath, imagePath);
        if (!analysis.structure.has(resolvedPath)) {
          missing.push(`${mdPath} → ${imagePath}`);
        }
      }
    }
    
    return missing;
  };

  const resolvePath = (basePath: string, relativePath: string): string => {
    const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
    return path.resolve(baseDir, relativePath).replace(/\\/g, '/');
  };

  return (
    <div>
      <FileUploadZone onFilesAccepted={handleFilesSelected} />
      
      {analysisResult && (
        <FileAnalysisReport 
          analysis={analysisResult}
          missingImages={missingImages}
        />
      )}
    </div>
  );
};
```

### 2. 後端檔案處理服務

#### 檔案管理器增強
```python
# backend/services/enhanced_file_manager.py
import os
import shutil
from pathlib import Path
from typing import Dict, List, Tuple
import re
import base64
from PIL import Image
import mimetypes

class EnhancedFileManager:
    def __init__(self, base_upload_dir: str = "uploads"):
        self.base_upload_dir = Path(base_upload_dir)
        self.base_upload_dir.mkdir(exist_ok=True)
    
    async def process_uploaded_files(
        self, 
        files: List[UploadFile], 
        task_id: str
    ) -> Dict[str, any]:
        """處理上傳的檔案，包括圖片依賴分析"""
        task_dir = self.base_upload_dir / task_id
        task_dir.mkdir(exist_ok=True)
        
        # 分類和存儲檔案
        markdown_files = []
        image_files = []
        file_structure = {}
        
        for file in files:
            file_path = task_dir / file.filename
            
            # 確保目錄存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存檔案
            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)
            
            # 記錄檔案結構
            relative_path = str(file_path.relative_to(task_dir))
            file_structure[relative_path] = str(file_path)
            
            if self._is_markdown_file(file.filename):
                markdown_files.append((relative_path, str(file_path)))
            elif self._is_image_file(file.filename):
                image_files.append((relative_path, str(file_path)))
        
        # 分析圖片依賴
        dependencies = await self._analyze_image_dependencies(
            markdown_files, file_structure
        )
        
        return {
            'task_dir': str(task_dir),
            'markdown_files': markdown_files,
            'image_files': image_files,
            'file_structure': file_structure,
            'dependencies': dependencies
        }
    
    async def _analyze_image_dependencies(
        self, 
        markdown_files: List[Tuple[str, str]], 
        file_structure: Dict[str, str]
    ) -> Dict[str, List[str]]:
        """分析Markdown檔案中的圖片依賴"""
        dependencies = {}
        
        for relative_path, full_path in markdown_files:
            async with aiofiles.open(full_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            image_paths = self._extract_image_paths(content)
            resolved_paths = []
            
            for image_path in image_paths:
                resolved_path = self._resolve_image_path(
                    relative_path, image_path, file_structure
                )
                resolved_paths.append({
                    'original': image_path,
                    'resolved': resolved_path,
                    'exists': resolved_path in file_structure
                })
            
            dependencies[relative_path] = resolved_paths
        
        return dependencies
    
    def _extract_image_paths(self, markdown_content: str) -> List[str]:
        """從Markdown內容中提取圖片路徑"""
        image_regex = r'!\[.*?\]\(([^)]+)\)'
        matches = re.findall(image_regex, markdown_content)
        
        # 過濾掉絕對URL和data URL
        relative_paths = []
        for path in matches:
            if not path.startswith(('http://', 'https://', 'data:')):
                relative_paths.append(path)
        
        return relative_paths
    
    def _resolve_image_path(
        self, 
        markdown_path: str, 
        image_path: str, 
        file_structure: Dict[str, str]
    ) -> str:
        """解析圖片的實際路徑"""
        # 獲取Markdown檔案的目錄
        md_dir = str(Path(markdown_path).parent)
        if md_dir == '.':
            md_dir = ''
        
        # 解析相對路徑
        if image_path.startswith('./'):
            # 當前目錄
            resolved = os.path.join(md_dir, image_path[2:])
        elif image_path.startswith('../'):
            # 上級目錄
            resolved = os.path.normpath(os.path.join(md_dir, image_path))
        else:
            # 相對於當前目錄
            resolved = os.path.join(md_dir, image_path)
        
        return resolved.replace('\\', '/')

    def _is_markdown_file(self, filename: str) -> bool:
        return filename.lower().endswith(('.md', '.markdown'))

    def _is_image_file(self, filename: str) -> bool:
        image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp')
        return filename.lower().endswith(image_extensions)

### 3. 圖片處理器

#### 核心圖片處理類
```python
# backend/services/image_processor.py
import base64
import os
from pathlib import Path
from typing import Dict, List, Optional
from PIL import Image
import mimetypes

class ImageProcessor:
    def __init__(self, task_dir: str):
        self.task_dir = Path(task_dir)
        self.processed_images = {}  # 緩存處理過的圖片

    async def process_markdown_images(
        self,
        markdown_content: str,
        markdown_path: str,
        file_structure: Dict[str, str],
        dependencies: List[Dict]
    ) -> str:
        """處理Markdown中的圖片路徑，轉換為base64或重寫路徑"""
        processed_content = markdown_content

        for dep in dependencies:
            original_path = dep['original']
            resolved_path = dep['resolved']
            exists = dep['exists']

            if exists and resolved_path in file_structure:
                # 圖片存在，轉換為base64
                image_full_path = file_structure[resolved_path]
                base64_data = await self._convert_image_to_base64(image_full_path)

                if base64_data:
                    # 替換Markdown中的圖片路徑
                    old_pattern = f'![{self._extract_alt_text(processed_content, original_path)}]({original_path})'
                    new_pattern = f'![{self._extract_alt_text(processed_content, original_path)}]({base64_data})'
                    processed_content = processed_content.replace(old_pattern, new_pattern)
            else:
                # 圖片不存在，添加錯誤標記
                alt_text = self._extract_alt_text(processed_content, original_path)
                error_html = f'<div class="missing-image-error">❌ 圖片缺失: {original_path}</div>'
                old_pattern = f'![{alt_text}]({original_path})'
                processed_content = processed_content.replace(old_pattern, error_html)

        return processed_content

    async def _convert_image_to_base64(self, image_path: str) -> Optional[str]:
        """將圖片轉換為base64 data URL"""
        try:
            # 檢查緩存
            if image_path in self.processed_images:
                return self.processed_images[image_path]

            # 讀取圖片檔案
            with open(image_path, 'rb') as img_file:
                img_data = img_file.read()

            # 獲取MIME類型
            mime_type, _ = mimetypes.guess_type(image_path)
            if not mime_type:
                mime_type = 'image/png'  # 默認類型

            # 優化圖片大小（可選）
            optimized_data = await self._optimize_image(img_data, mime_type)

            # 轉換為base64
            base64_data = base64.b64encode(optimized_data).decode('utf-8')
            data_url = f"data:{mime_type};base64,{base64_data}"

            # 緩存結果
            self.processed_images[image_path] = data_url

            return data_url

        except Exception as e:
            print(f"Error converting image {image_path}: {e}")
            return None

    async def _optimize_image(self, img_data: bytes, mime_type: str) -> bytes:
        """優化圖片大小和質量"""
        try:
            # 對於SVG，直接返回原始數據
            if mime_type == 'image/svg+xml':
                return img_data

            # 使用PIL優化其他格式
            from io import BytesIO
            img = Image.open(BytesIO(img_data))

            # 限制最大尺寸
            max_size = (1200, 1200)
            if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                img.thumbnail(max_size, Image.Resampling.LANCZOS)

            # 轉換為RGB（如果需要）
            if img.mode in ('RGBA', 'LA', 'P'):
                if mime_type == 'image/jpeg':
                    # JPEG不支援透明度，轉換為RGB
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'P':
                        img = img.convert('RGBA')
                    background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                    img = background

            # 保存優化後的圖片
            output = BytesIO()
            format_map = {
                'image/jpeg': 'JPEG',
                'image/png': 'PNG',
                'image/webp': 'WEBP'
            }

            img_format = format_map.get(mime_type, 'PNG')
            quality = 85 if img_format == 'JPEG' else None

            save_kwargs = {'format': img_format}
            if quality:
                save_kwargs['quality'] = quality
                save_kwargs['optimize'] = True

            img.save(output, **save_kwargs)
            return output.getvalue()

        except Exception as e:
            print(f"Error optimizing image: {e}")
            return img_data  # 返回原始數據

    def _extract_alt_text(self, markdown_content: str, image_path: str) -> str:
        """從Markdown中提取圖片的alt文字"""
        import re
        pattern = rf'!\[([^\]]*)\]\({re.escape(image_path)}\)'
        match = re.search(pattern, markdown_content)
        return match.group(1) if match else '圖片'

### 4. 路徑解析器

#### 智能路徑解析
```python
# backend/services/path_resolver.py
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse

class PathResolver:
    def __init__(self, base_dir: str):
        self.base_dir = Path(base_dir)

    def resolve_image_path(
        self,
        markdown_file_path: str,
        image_path: str,
        file_structure: Dict[str, str]
    ) -> Optional[str]:
        """解析圖片路徑，返回實際檔案路徑"""

        # 跳過絕對URL
        if self._is_absolute_url(image_path):
            return None

        # 跳過data URL
        if image_path.startswith('data:'):
            return None

        # 獲取可能的路徑候選
        candidates = self._generate_path_candidates(markdown_file_path, image_path)

        # 在檔案結構中查找匹配的路徑
        for candidate in candidates:
            if candidate in file_structure:
                return file_structure[candidate]

        # 嘗試模糊匹配（檔名相同但路徑不同）
        image_filename = os.path.basename(image_path)
        for file_path in file_structure.keys():
            if os.path.basename(file_path) == image_filename:
                return file_structure[file_path]

        return None

    def _is_absolute_url(self, path: str) -> bool:
        """檢查是否為絕對URL"""
        try:
            result = urlparse(path)
            return bool(result.scheme and result.netloc)
        except:
            return False

    def _generate_path_candidates(
        self,
        markdown_path: str,
        image_path: str
    ) -> List[str]:
        """生成可能的圖片路徑候選"""
        candidates = []

        # 獲取Markdown檔案的目錄
        md_dir = str(Path(markdown_path).parent)
        if md_dir == '.':
            md_dir = ''

        # 1. 直接相對路徑解析
        if image_path.startswith('./'):
            # 當前目錄
            resolved = os.path.join(md_dir, image_path[2:])
            candidates.append(self._normalize_path(resolved))
        elif image_path.startswith('../'):
            # 上級目錄
            resolved = os.path.normpath(os.path.join(md_dir, image_path))
            candidates.append(self._normalize_path(resolved))
        else:
            # 相對於當前目錄
            resolved = os.path.join(md_dir, image_path)
            candidates.append(self._normalize_path(resolved))

        # 2. 常見的圖片目錄結構
        common_image_dirs = ['images', 'img', 'assets', 'static', 'media']
        image_filename = os.path.basename(image_path)

        for img_dir in common_image_dirs:
            # 在Markdown同級目錄下的圖片目錄
            candidate = os.path.join(md_dir, img_dir, image_filename)
            candidates.append(self._normalize_path(candidate))

            # 在根目錄下的圖片目錄
            candidate = os.path.join(img_dir, image_filename)
            candidates.append(self._normalize_path(candidate))

        # 3. 直接在根目錄
        candidates.append(self._normalize_path(image_filename))

        # 去重並返回
        return list(dict.fromkeys(candidates))

    def _normalize_path(self, path: str) -> str:
        """標準化路徑格式"""
        return path.replace('\\', '/').lstrip('./')

### 5. 增強的Markdown處理器

#### 集成圖片處理的Markdown處理器
```python
# processor/markdown_processor/enhanced_processor.py
from .core import MarkdownProcessor
from backend.services.image_processor import ImageProcessor
from backend.services.path_resolver import PathResolver

class EnhancedMarkdownProcessor(MarkdownProcessor):
    def __init__(self, theme: str = "default", task_dir: str = None):
        super().__init__(theme)
        self.task_dir = task_dir
        self.image_processor = ImageProcessor(task_dir) if task_dir else None
        self.path_resolver = PathResolver(task_dir) if task_dir else None

    async def process_with_images(
        self,
        content: str,
        markdown_path: str,
        file_structure: Dict[str, str],
        dependencies: List[Dict]
    ) -> ProcessedContent:
        """處理包含圖片的Markdown內容"""

        # 1. 預處理圖片路徑
        if self.image_processor:
            processed_content = await self.image_processor.process_markdown_images(
                content, markdown_path, file_structure, dependencies
            )
        else:
            processed_content = content

        # 2. 執行標準Markdown處理
        result = self.process(processed_content)

        # 3. 添加圖片相關的CSS
        enhanced_css = result.css + self._get_image_css()

        return ProcessedContent(
            html=result.html,
            css=enhanced_css,
            images_processed=len(dependencies)
        )

    def _get_image_css(self) -> str:
        """獲取圖片相關的CSS樣式"""
        return """
        /* 圖片樣式 */
        .markdown-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 1rem auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 圖片說明文字 */
        .markdown-content img + em {
            display: block;
            text-align: center;
            font-style: italic;
            color: #666;
            margin-top: 0.5rem;
            font-size: 0.9em;
        }

        /* 缺失圖片錯誤樣式 */
        .missing-image-error {
            background-color: #fee;
            border: 1px solid #fcc;
            color: #c33;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 1rem 0;
            text-align: center;
        }

        /* PDF專用圖片樣式 */
        @media print {
            .markdown-content img {
                max-width: 100%;
                page-break-inside: avoid;
                break-inside: avoid;
            }

            .missing-image-error {
                border: 2px solid #c33;
                background-color: #fff;
            }
        }
        """

class ProcessedContent:
    def __init__(self, html: str, css: str, images_processed: int = 0):
        self.html = html
        self.css = css
        self.images_processed = images_processed
```

### 6. API路由更新

#### 增強的轉換API
```python
# backend/api/routes/enhanced_conversion.py
from fastapi import APIRouter, UploadFile, File, Form, BackgroundTasks, HTTPException
from typing import List
import uuid
from backend.services.enhanced_file_manager import EnhancedFileManager
from processor.markdown_processor.enhanced_processor import EnhancedMarkdownProcessor

router = APIRouter(prefix="/api/conversion", tags=["conversion"])
file_manager = EnhancedFileManager()

@router.post("/upload-with-images")
async def upload_files_with_images(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    theme: str = Form("default"),
    pagination_mode: str = Form("balanced"),
    exercise_handling: str = Form("protect"),
    table_handling: str = Form("protect")
):
    """上傳檔案並處理圖片依賴"""
    task_id = str(uuid.uuid4())

    try:
        # 處理上傳的檔案
        file_analysis = await file_manager.process_uploaded_files(files, task_id)

        # 檢查圖片依賴
        missing_images = []
        for md_path, dependencies in file_analysis['dependencies'].items():
            for dep in dependencies:
                if not dep['exists']:
                    missing_images.append({
                        'markdown_file': md_path,
                        'missing_image': dep['original']
                    })

        # 建立轉換任務
        conversion_options = {
            'theme': theme,
            'pagination_mode': pagination_mode,
            'exercise_handling': exercise_handling,
            'table_handling': table_handling,
            'file_analysis': file_analysis
        }

        # 啟動背景任務
        background_tasks.add_task(
            process_conversion_with_images,
            task_id,
            conversion_options
        )

        return {
            "task_id": task_id,
            "status": "pending",
            "files_count": len(file_analysis['markdown_files']),
            "images_count": len(file_analysis['image_files']),
            "missing_images": missing_images
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"檔案處理失敗: {str(e)}")

async def process_conversion_with_images(task_id: str, options: dict):
    """處理包含圖片的轉換任務"""
    try:
        await update_task_status(task_id, "processing", 0)

        file_analysis = options['file_analysis']
        task_dir = file_analysis['task_dir']

        # 初始化處理器
        processor = EnhancedMarkdownProcessor(
            theme=options['theme'],
            task_dir=task_dir
        )
        pdf_generator = PDFGenerator()

        results = []
        markdown_files = file_analysis['markdown_files']
        total_files = len(markdown_files)

        for i, (md_path, md_full_path) in enumerate(markdown_files):
            # 讀取Markdown內容
            async with aiofiles.open(md_full_path, 'r', encoding='utf-8') as f:
                markdown_content = await f.read()

            # 獲取該檔案的圖片依賴
            dependencies = file_analysis['dependencies'].get(md_path, [])

            # 處理Markdown（包含圖片）
            processed_content = await processor.process_with_images(
                markdown_content,
                md_path,
                file_analysis['file_structure'],
                dependencies
            )

            # 生成HTML
            html_output = processed_content.html
            css_output = processed_content.css

            # 生成PDF
            pdf_output = pdf_generator.generate_pdf(
                html_output,
                css_output,
                options
            )

            # 保存結果
            result_files = await save_conversion_results(
                task_id,
                md_path,
                html_output,
                pdf_output
            )

            result_files['images_processed'] = processed_content.images_processed
            results.append(result_files)

            # 更新進度
            progress = int((i + 1) / total_files * 100)
            await update_task_status(task_id, "processing", progress)

        await update_task_status(task_id, "completed", 100, results)

    except Exception as e:
        await update_task_status(task_id, "failed", 0, error=str(e))

### 7. 前端用戶體驗增強

#### 圖片依賴檢查組件
```typescript
// frontend/components/ImageDependency/ImageDependencyChecker.tsx
interface ImageDependency {
  markdownFile: string;
  missingImage: string;
}

interface ImageCheckResult {
  totalImages: number;
  missingImages: ImageDependency[];
  hasIssues: boolean;
}

export const ImageDependencyChecker: React.FC<{
  checkResult: ImageCheckResult;
  onProceed: () => void;
  onCancel: () => void;
}> = ({ checkResult, onProceed, onCancel }) => {
  if (!checkResult.hasIssues) {
    return (
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="text-green-600 text-xl mr-3">✅</div>
          <div>
            <h3 className="font-medium text-green-800">圖片檢查通過</h3>
            <p className="text-green-600">
              找到 {checkResult.totalImages} 張圖片，所有依賴都正確
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
      <div className="flex items-start">
        <div className="text-yellow-600 text-xl mr-3">⚠️</div>
        <div className="flex-1">
          <h3 className="font-medium text-yellow-800 mb-2">發現圖片依賴問題</h3>
          <p className="text-yellow-700 mb-3">
            以下圖片檔案缺失，將在轉換結果中顯示錯誤標記：
          </p>

          <div className="bg-white rounded border max-h-40 overflow-y-auto">
            {checkResult.missingImages.map((dep, index) => (
              <div key={index} className="px-3 py-2 border-b last:border-b-0">
                <div className="text-sm">
                  <span className="font-medium">{dep.markdownFile}</span>
                  <span className="text-gray-500"> → </span>
                  <span className="text-red-600">{dep.missingImage}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 flex space-x-3">
            <button
              onClick={onProceed}
              className="btn btn-warning"
            >
              繼續轉換（忽略缺失圖片）
            </button>
            <button
              onClick={onCancel}
              className="btn btn-outline"
            >
              重新上傳檔案
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
```

#### 增強的檔案上傳流程
```typescript
// frontend/components/FileUpload/EnhancedUploadFlow.tsx
export const EnhancedUploadFlow: React.FC = () => {
  const [uploadState, setUploadState] = useState<'selecting' | 'analyzing' | 'confirming' | 'converting'>('selecting');
  const [files, setFiles] = useState<File[]>([]);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [conversionConfig, setConversionConfig] = useState({
    theme: 'default',
    paginationMode: 'balanced',
    exerciseHandling: 'protect',
    tableHandling: 'protect'
  });

  const handleFilesSelected = async (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    setUploadState('analyzing');

    try {
      // 分析檔案結構
      const analysis = await FileAnalyzer.analyzeUploadedFiles(selectedFiles);
      setAnalysisResult(analysis);
      setUploadState('confirming');
    } catch (error) {
      console.error('檔案分析失敗:', error);
      setUploadState('selecting');
    }
  };

  const handleStartConversion = async () => {
    setUploadState('converting');

    try {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));

      Object.entries(conversionConfig).forEach(([key, value]) => {
        formData.append(key, value);
      });

      const response = await fetch('/api/conversion/upload-with-images', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (response.ok) {
        // 處理轉換結果
        handleConversionStarted(result);
      } else {
        throw new Error(result.detail || '轉換失敗');
      }
    } catch (error) {
      console.error('轉換錯誤:', error);
      setUploadState('confirming');
    }
  };

  const renderCurrentStep = () => {
    switch (uploadState) {
      case 'selecting':
        return <FileUploadZone onFilesAccepted={handleFilesSelected} />;

      case 'analyzing':
        return <div className="text-center py-8">分析檔案結構中...</div>;

      case 'confirming':
        return (
          <div className="space-y-6">
            <FileAnalysisReport analysis={analysisResult} />
            <ImageDependencyChecker
              checkResult={analysisResult.imageCheck}
              onProceed={handleStartConversion}
              onCancel={() => setUploadState('selecting')}
            />
            <ConversionConfigPanel
              config={conversionConfig}
              onChange={setConversionConfig}
            />
          </div>
        );

      case 'converting':
        return <ConversionProgress />;

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <StepIndicator currentStep={uploadState} />
      </div>
      {renderCurrentStep()}
    </div>
  );
};
```

## 最佳實踐建議

### 1. 性能優化
- **圖片緩存**：對處理過的圖片進行緩存，避免重複轉換
- **並行處理**：多個圖片可以並行轉換為base64
- **大小限制**：設置合理的圖片大小限制，避免記憶體問題
- **格式優化**：自動優化圖片格式和質量

### 2. 錯誤處理
- **優雅降級**：圖片缺失時顯示友好的錯誤信息
- **詳細日誌**：記錄圖片處理過程中的詳細信息
- **用戶反饋**：提供清晰的錯誤提示和解決建議

### 3. 用戶體驗
- **預檢查**：上傳前檢查圖片依賴
- **進度顯示**：顯示圖片處理進度
- **預覽功能**：允許用戶預覽處理結果

### 4. 安全考慮
- **檔案類型驗證**：嚴格驗證上傳的檔案類型
- **大小限制**：限制單個檔案和總檔案大小
- **路徑安全**：防止路徑遍歷攻擊

## 總結

這個圖片路徑處理方案提供了完整的解決方案，包括：

1. **智能路徑解析**：自動解析和修正圖片路徑
2. **Base64轉換**：將圖片嵌入到HTML/PDF中
3. **錯誤處理**：優雅處理缺失圖片的情況
4. **用戶體驗**：提供清晰的反饋和控制選項
5. **性能優化**：緩存和並行處理提升效率

該方案確保了在Markdown→HTML→PDF的轉換過程中，圖片能夠正確顯示，同時提供了良好的用戶體驗和錯誤處理機制。
