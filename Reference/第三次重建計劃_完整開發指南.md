# 教育材料轉換系統開發進度追蹤計劃

## 📊 整體進度統計

### 總體完成度
- [ ] **第一階段：基礎架構建立** (0/3 任務完成) - 0%
- [ ] **第二階段：核心處理引擎** (0/3 任務完成) - 0%
- [ ] **第三階段：PDF生成優化** (0/2 任務完成) - 0%
- [ ] **第四階段：用戶Portal開發** (0/3 任務完成) - 0%
- [ ] **第五階段：後端服務整合** (0/2 任務完成) - 0%
- [ ] **第六階段：系統整合與測試** (0/2 任務完成) - 0%
- [ ] **第七階段：部署與優化** (0/2 任務完成) - 0%

**總進度：0/17 主要任務完成 (0%)**

---

# 香港學生教育材料轉換系統 - 第三次重建計劃

## 項目概述

### 目標用戶
- **主要用戶**：香港10-17歲學生的教師和教育工作者
- **使用場景**：製作教學材料、練習題、測驗卷等教育內容

### 核心功能
- **轉換流程**：Markdown → HTML → PDF
- **檔案處理**：支援單檔、多檔、整個資料夾上傳
- **主題化**：5種預設主題（Default、Nature、Space、Tech、Sport）
- **輸出格式**：同時生成HTML和PDF檔案
- **特殊格式**：填空線、練習區塊、數學公式、表格等教育專用格式

### 技術選擇確認
- **PDF引擎**：WeasyPrint（專注單一引擎，確保穩定性）
- **數學公式**：MathJax 3.x（完整功能支援）
- **Markdown解析**：markdown-it-py（Python生態整合）
- **前端框架**：Next.js 14 + TypeScript + Tailwind CSS
- **後端框架**：FastAPI + Python 3.11+

## 技術架構設計

### 系統架構圖
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Portal    │───▶│   後端API      │───▶│   處理引擎      │
│   (Next.js)     │    │   (FastAPI)     │    │   (Python)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   檔案上傳      │    │   任務管理      │    │   輸出生成      │
│   拖拽介面      │    │   進度追蹤      │    │   HTML + PDF    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心模組架構
```
src/
├── frontend/                 # Next.js 前端應用
│   ├── components/          # React 組件
│   ├── pages/              # 頁面路由
│   ├── hooks/              # 自定義 Hooks
│   ├── utils/              # 工具函數
│   └── styles/             # 樣式檔案
├── backend/                 # FastAPI 後端應用
│   ├── api/                # API 路由
│   ├── core/               # 核心配置
│   ├── services/           # 業務邏輯服務
│   └── models/             # 數據模型
└── processor/              # 處理引擎
    ├── markdown_processor/ # Markdown 處理
    ├── theme_engine/       # 主題引擎
    ├── pdf_generator/      # PDF 生成
    └── math_processor/     # 數學公式處理
```

## 📋 詳細開發階段追蹤

## 第一階段：基礎架構建立（週1-2）

### 任務1.1：項目初始化
**目標**：建立基本的項目結構和開發環境
**預估工時**：8小時
**負責人**：開發團隊
**依賴關係**：無

#### 具體執行步驟：
- [ ] 建立根目錄結構
- [ ] 初始化 Next.js 前端項目
- [ ] 初始化 FastAPI 後端項目
- [ ] 配置 TypeScript 和 ESLint
- [ ] 設置 Python 虛擬環境和依賴管理

#### 交付成果：
- [ ] 完整的項目目錄結構
- [ ] 前端開發環境配置完成
- [ ] 後端開發環境配置完成
- [ ] 所有依賴包安裝完成
- [ ] 基本的README文檔

#### 驗收標準：
- [ ] 前端可以成功啟動開發服務器
- [ ] 後端可以成功啟動API服務器
- [ ] 所有依賴包無衝突
- [ ] TypeScript編譯無錯誤

**檔案結構**：
```
education-material-converter/
├── frontend/
│   ├── package.json
│   ├── next.config.js
│   ├── tailwind.config.js
│   └── tsconfig.json
├── backend/
│   ├── requirements.txt
│   ├── main.py
│   └── pyproject.toml
├── processor/
│   ├── requirements.txt
│   └── __init__.py
├── docker-compose.yml
└── README.md
```

**依賴清單**：
- [ ] **前端**：next@14, react@18, typescript, tailwindcss, axios, react-dropzone
- [ ] **後端**：fastapi, uvicorn, python-multipart, pydantic, aiofiles
- [ ] **處理器**：markdown-it-py, weasyprint, beautifulsoup4, pillow

### 任務1.2：基礎API架構
**目標**：建立基本的API結構和路由
**預估工時**：6小時
**負責人**：後端開發者
**依賴關係**：任務1.1完成

#### 具體執行步驟：
- [ ] 設計API端點結構
- [ ] 建立基本路由檔案
- [ ] 定義數據模型和Schema
- [ ] 實現基本的錯誤處理
- [ ] 建立API文檔結構

#### 交付成果：
- [ ] 完整的API路由定義
- [ ] 數據模型Schema檔案
- [ ] 基本的錯誤處理機制
- [ ] API文檔框架

#### 驗收標準：
- [ ] 所有API端點可以正常訪問
- [ ] 數據模型驗證正常工作
- [ ] 錯誤處理返回正確的HTTP狀態碼
- [ ] API文檔可以正常顯示

**API端點設計**：
```python
# backend/api/routes.py
POST /api/upload          # 檔案上傳
POST /api/convert         # 轉換請求
GET  /api/status/{task_id} # 任務狀態查詢
GET  /api/download/{file_id} # 檔案下載
GET  /api/themes          # 獲取主題列表
```

**數據模型**：
```python
# backend/models/schemas.py
class ConvertRequest(BaseModel):
    files: List[str]
    theme: str = "default"
    options: Dict[str, Any] = {}

class TaskStatus(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int
    result_files: Optional[List[str]] = None
    error_message: Optional[str] = None
```

### 任務1.3：前端基礎組件
**目標**：建立基本的用戶介面組件
**預估工時**：10小時
**負責人**：前端開發者
**依賴關係**：任務1.1完成

#### 具體執行步驟：
- [ ] 建立檔案上傳組件
- [ ] 建立主題選擇器組件
- [ ] 建立轉換進度組件
- [ ] 建立基本的佈局組件
- [ ] 實現響應式設計

#### 交付成果：
- [ ] FileUpload組件完成
- [ ] ThemeSelector組件完成
- [ ] ConversionProgress組件完成
- [ ] 基本佈局組件完成
- [ ] 組件樣式和響應式設計完成

#### 驗收標準：
- [ ] 所有組件可以正常渲染
- [ ] 組件間的數據傳遞正常
- [ ] 響應式設計在不同螢幕尺寸下正常
- [ ] 組件符合設計規範

**核心組件**：
```typescript
// frontend/components/FileUpload.tsx
interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  acceptedTypes: string[];
  maxFiles?: number;
}

// frontend/components/ThemeSelector.tsx
interface ThemeSelectorProps {
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
  themes: Theme[];
}

// frontend/components/ConversionProgress.tsx
interface ConversionProgressProps {
  taskId: string;
  onComplete: (results: ConversionResult) => void;
}
```

## 第二階段：核心處理引擎（週3-4）

### 任務2.1：Markdown處理器重構
**目標**：基於現有技術文檔重新實現Markdown處理器
**預估工時**：16小時
**負責人**：後端開發者
**依賴關係**：任務1.2完成

#### 具體執行步驟：
- [ ] 設計MarkdownProcessor核心類別
- [ ] 實現預處理階段功能
- [ ] 實現解析階段功能
- [ ] 實現轉換階段功能
- [ ] 實現後處理階段功能
- [ ] 建立特殊元素處理器
- [ ] 實現填空線處理功能
- [ ] 實現練習區塊識別功能
- [ ] 實現表格增強功能

#### 交付成果：
- [ ] MarkdownProcessor核心類別完成
- [ ] FillBlankProcessor處理器完成
- [ ] ExerciseBlockProcessor處理器完成
- [ ] TableProcessor處理器完成
- [ ] 單元測試覆蓋率達到80%以上

#### 驗收標準：
- [ ] 可以正確處理基本Markdown語法
- [ ] 填空線轉換功能正常
- [ ] 練習區塊識別準確率達到95%以上
- [ ] 表格處理功能正常
- [ ] 所有單元測試通過

**核心類別設計**：
```python
# processor/markdown_processor/core.py
class MarkdownProcessor:
    def __init__(self, theme: str = "default"):
        self.theme = theme
        self.md_parser = self._setup_markdown_parser()
        self.theme_engine = ThemeEngine(theme)
    
    def process(self, content: str) -> ProcessedContent:
        # 1. 預處理階段
        cleaned_content = self._preprocess(content)
        # 2. 解析階段
        parsed_tokens = self.md_parser.parse(cleaned_content)
        # 3. 轉換階段
        html_content = self._convert_to_html(parsed_tokens)
        # 4. 主題化階段
        themed_html = self.theme_engine.apply_theme(html_content)
        # 5. 後處理階段
        final_html = self._postprocess(themed_html)
        return ProcessedContent(html=final_html, css=self.theme_engine.get_css())
```

**特殊元素處理器**：
```python
# processor/markdown_processor/elements.py
class FillBlankProcessor:
    @staticmethod
    def process(content: str) -> str:
        # 處理填空線：4+ 下劃線轉換為填空框
        pattern = r'_{4,}'
        return re.sub(pattern, lambda m: f'<span class="fill-blank fill-blank-{len(m.group())//4}">{m.group()}</span>', content)

class ExerciseBlockProcessor:
    @staticmethod
    def identify_exercise_blocks(tokens: List[Token]) -> List[Token]:
        # 識別練習區塊並添加特殊標記
        pass

class TableProcessor:
    @staticmethod
    def enhance_tables(html: str) -> str:
        # 增強表格功能，處理空儲存格
        pass
```

### 任務2.2：主題引擎實現
**目標**：實現完整的主題系統，包含SVG數字生成
**預估工時**：14小時
**負責人**：前端+後端開發者
**依賴關係**：任務2.1完成

#### 具體執行步驟：
- [ ] 設計ThemeEngine核心架構
- [ ] 實現SVGNumberGenerator類別
- [ ] 建立5種主題配置檔案（Default、Nature、Space、Tech、Sport）
- [ ] 實現主題CSS生成功能
- [ ] 實現bullet points替換功能
- [ ] 實現SVG數字替換功能
- [ ] 實現主題圖標添加功能
- [ ] 建立主題預覽功能

#### 交付成果：
- [ ] ThemeEngine核心類別完成
- [ ] SVGNumberGenerator類別完成
- [ ] 5種主題配置檔案完成
- [ ] 主題CSS生成功能完成
- [ ] 主題預覽功能完成

#### 驗收標準：
- [ ] 所有5種主題可以正常應用
- [ ] SVG數字生成質量符合設計要求
- [ ] 主題切換功能正常
- [ ] CSS生成無語法錯誤
- [ ] 主題預覽準確反映實際效果

**主題引擎架構**：
```python
# processor/theme_engine/core.py
class ThemeEngine:
    def __init__(self, theme_name: str):
        self.theme_name = theme_name
        self.config = self._load_theme_config(theme_name)
        self.svg_generator = SVGNumberGenerator(self.config)

    def apply_theme(self, html: str) -> str:
        # 1. 應用主題CSS類名
        themed_html = self._apply_theme_classes(html)
        # 2. 替換bullet points
        themed_html = self._replace_bullet_points(themed_html)
        # 3. 生成SVG數字
        themed_html = self._replace_ordered_list_numbers(themed_html)
        # 4. 添加主題圖標
        themed_html = self._add_theme_icons(themed_html)
        return themed_html

    def get_css(self) -> str:
        return self._generate_theme_css()

# processor/theme_engine/svg_generator.py
class SVGNumberGenerator:
    def __init__(self, theme_config: dict):
        self.config = theme_config

    def generate_number_svg(self, number: int) -> str:
        shape = self.config['svg_shape']
        colors = self.config['colors']

        if shape == 'circle':
            return self._generate_circle_svg(number, colors)
        elif shape == 'hexagon':
            return self._generate_hexagon_svg(number, colors)
        elif shape == 'star':
            return self._generate_star_svg(number, colors)
        elif shape == 'square':
            return self._generate_square_svg(number, colors)
        elif shape == 'badge':
            return self._generate_badge_svg(number, colors)
```

**主題配置檔案**：
```python
# processor/theme_engine/themes/default.py
DEFAULT_THEME = {
    'name': 'default',
    'svg_shape': 'circle',
    'colors': {
        'primary': '#2563eb',
        'secondary': '#3b82f6',
        'border': '#1d4ed8',
        'text': '#ffffff'
    },
    'bullet_icons': {
        'level_1': '🔹',
        'level_2': '▫️',
        'level_3': '▪️'
    },
    'css_variables': {
        '--theme-primary': '#2563eb',
        '--theme-secondary': '#3b82f6',
        '--theme-accent': '#1d4ed8'
    }
}

# processor/theme_engine/themes/nature.py
NATURE_THEME = {
    'name': 'nature',
    'svg_shape': 'hexagon',
    'colors': {
        'primary': '#059669',
        'secondary': '#10b981',
        'border': '#047857',
        'text': '#ffffff'
    },
    'bullet_icons': {
        'level_1': '🌿',
        'level_2': '🍃',
        'level_3': '🌱'
    }
}
```

### 任務2.3：數學公式處理器
**目標**：整合MathJax 3.x進行數學公式渲染
**預估工時**：10小時
**負責人**：後端開發者
**依賴關係**：任務2.1完成

#### 具體執行步驟：
- [ ] 設計MathProcessor核心類別
- [ ] 配置MathJax 3.x設定
- [ ] 實現數學公式識別功能
- [ ] 實現公式預渲染功能
- [ ] 實現HTML中公式替換功能
- [ ] 實現超長公式自動換行功能
- [ ] 建立數學公式測試案例

#### 交付成果：
- [ ] MathProcessor核心類別完成
- [ ] MathJax配置檔案完成
- [ ] 公式渲染功能完成
- [ ] 超長公式處理功能完成
- [ ] 數學公式測試案例完成

#### 驗收標準：
- [ ] 可以正確識別inline和display數學公式
- [ ] 公式渲染質量符合要求
- [ ] 超長公式可以正確換行
- [ ] 渲染性能滿足要求（<2秒/公式）
- [ ] 所有測試案例通過

**數學處理器設計**：
```python
# processor/math_processor/core.py
class MathProcessor:
    def __init__(self):
        self.mathjax_config = self._get_mathjax_config()

    def process_math(self, html: str) -> str:
        # 1. 識別數學公式
        math_expressions = self._extract_math_expressions(html)
        # 2. 預渲染公式（用於PDF）
        rendered_math = self._render_math_expressions(math_expressions)
        # 3. 替換HTML中的公式
        processed_html = self._replace_math_in_html(html, rendered_math)
        return processed_html

    def _get_mathjax_config(self) -> dict:
        return {
            'loader': {'load': ['[tex]/ams', '[tex]/color']},
            'tex': {
                'inlineMath': [['$', '$']],
                'displayMath': [['$$', '$$']],
                'packages': {'[+]': ['ams', 'color']},
                'processEscapes': True
            },
            'svg': {
                'fontCache': 'global',
                'displayAlign': 'center',
                'displayIndent': '0'
            }
        }

    def _handle_long_formulas(self, formula: str) -> str:
        # 處理超長公式的自動換行
        if len(formula) > 80:  # 字符長度閾值
            return self._apply_line_breaking(formula)
        return formula
```

## 第三階段：PDF生成優化（週5-6）

### 任務3.1：WeasyPrint整合優化
**目標**：優化PDF生成質量和性能
**預估工時**：12小時
**負責人**：後端開發者
**依賴關係**：任務2.1、2.2、2.3完成

#### 具體執行步驟：
- [ ] 設計PDFGenerator核心類別
- [ ] 配置WeasyPrint最佳化設定
- [ ] 實現HTML文檔準備功能
- [ ] 實現分頁控制功能
- [ ] 建立PaginationController類別
- [ ] 實現三種分頁模式（balanced、compact、integrity）
- [ ] 優化PDF生成性能
- [ ] 建立PDF品質測試

#### 交付成果：
- [ ] PDFGenerator核心類別完成
- [ ] PaginationController類別完成
- [ ] 三種分頁模式CSS完成
- [ ] PDF生成性能優化完成
- [ ] PDF品質測試案例完成

#### 驗收標準：
- [ ] PDF生成成功率達到99%以上
- [ ] 分頁控制功能正常
- [ ] PDF檔案大小合理（<5MB/10頁）
- [ ] 生成速度滿足要求（<10秒/10頁）
- [ ] PDF品質符合印刷要求

**PDF生成器設計**：
```python
# processor/pdf_generator/core.py
class PDFGenerator:
    def __init__(self):
        self.weasyprint_config = self._get_weasyprint_config()

    def generate_pdf(self, html: str, css: str, options: dict = None) -> bytes:
        # 1. 準備HTML文檔
        full_html = self._prepare_html_document(html, css)
        # 2. 配置WeasyPrint
        html_doc = HTML(string=full_html, base_url='.')
        # 3. 應用分頁控制
        css_with_pagination = self._apply_pagination_rules(css, options)
        # 4. 生成PDF
        pdf_bytes = html_doc.write_pdf(
            stylesheets=[CSS(string=css_with_pagination)],
            **self.weasyprint_config
        )
        return pdf_bytes

    def _get_weasyprint_config(self) -> dict:
        return {
            'presentational_hints': True,
            'optimize_images': True,
            'pdf_version': '1.7',
            'pdf_forms': False
        }

    def _apply_pagination_rules(self, css: str, options: dict) -> str:
        pagination_mode = options.get('pagination_mode', 'balanced')

        if pagination_mode == 'compact':
            return css + self._get_compact_pagination_css()
        elif pagination_mode == 'integrity':
            return css + self._get_integrity_pagination_css()
        else:  # balanced
            return css + self._get_balanced_pagination_css()
```

**分頁控制CSS生成**：
```python
# processor/pdf_generator/pagination.py
class PaginationController:
    @staticmethod
    def get_balanced_pagination_css() -> str:
        return """
        @page {
            size: A4;
            margin: 2cm;
            @bottom-center {
                content: counter(page);
                font-size: 12px;
            }
        }

        .exercise-block {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .markdown-table {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        h3, h4, h5, h6 {
            page-break-after: avoid;
            break-after: avoid;
        }

        .math-display {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        p {
            orphans: 2;
            widows: 2;
        }
        """

    @staticmethod
    def get_compact_pagination_css() -> str:
        return """
        @page {
            size: A4;
            margin: 1.5cm;
        }

        p {
            orphans: 1;
            widows: 1;
        }

        .exercise-block {
            page-break-inside: auto;
        }
        """

    @staticmethod
    def get_integrity_pagination_css() -> str:
        return """
        @page {
            size: A4;
            margin: 2.5cm;
        }

        .exercise-block,
        .markdown-table,
        .math-display,
        .figure-with-caption {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        h1, h2 {
            page-break-before: always;
            break-before: page;
        }

        h3, h4, h5, h6 {
            page-break-after: avoid;
            break-after: avoid;
            page-break-inside: avoid;
            break-inside: avoid;
        }

        p {
            orphans: 3;
            widows: 3;
        }
        """
```

### 任務3.2：圖片處理優化
**目標**：優化圖片在PDF中的顯示質量
**預估工時**：8小時
**負責人**：後端開發者
**依賴關係**：任務3.1完成

#### 具體執行步驟：
- [ ] 設計ImageProcessor核心類別
- [ ] 實現圖片格式支援檢查
- [ ] 實現圖片路徑解析功能
- [ ] 實現圖片base64轉換功能
- [ ] 實現MIME類型識別功能
- [ ] 建立圖片品質優化功能
- [ ] 實現圖片錯誤處理機制
- [ ] 建立圖片處理測試案例

#### 交付成果：
- [ ] ImageProcessor核心類別完成
- [ ] 圖片base64轉換功能完成
- [ ] 圖片品質優化功能完成
- [ ] 圖片錯誤處理機制完成
- [ ] 圖片處理測試案例完成

#### 驗收標準：
- [ ] 支援所有常見圖片格式（PNG、JPG、GIF、SVG）
- [ ] 圖片轉換成功率達到95%以上
- [ ] 圖片在PDF中顯示清晰
- [ ] 圖片處理不影響PDF生成速度
- [ ] 錯誤處理機制完善

**圖片處理器**：
```python
# processor/pdf_generator/image_processor.py
class ImageProcessor:
    def __init__(self):
        self.supported_formats = ['.png', '.jpg', '.jpeg', '.gif', '.svg']

    def process_images_for_pdf(self, html: str, base_path: str) -> str:
        soup = BeautifulSoup(html, 'html.parser')
        images = soup.find_all('img')

        for img in images:
            src = img.get('src')
            if src and not src.startswith('data:'):
                # 轉換為base64編碼
                base64_src = self._convert_to_base64(src, base_path)
                if base64_src:
                    img['src'] = base64_src

        return str(soup)

    def _convert_to_base64(self, image_path: str, base_path: str) -> str:
        try:
            full_path = os.path.join(base_path, image_path)
            with open(full_path, 'rb') as img_file:
                img_data = img_file.read()
                img_base64 = base64.b64encode(img_data).decode('utf-8')
                mime_type = self._get_mime_type(image_path)
                return f"data:{mime_type};base64,{img_base64}"
        except Exception as e:
            print(f"Error converting image {image_path}: {e}")
            return None

    def _get_mime_type(self, file_path: str) -> str:
        ext = os.path.splitext(file_path)[1].lower()
        mime_types = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml'
        }
        return mime_types.get(ext, 'image/png')
```

### 第四階段：用戶Portal開發（週7-8）

#### 任務4.1：檔案上傳介面
**目標**：建立現代化的檔案上傳介面

**檔案上傳組件**：
```typescript
// frontend/components/FileUpload/FileUploadZone.tsx
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';

interface FileUploadZoneProps {
  onFilesAccepted: (files: File[]) => void;
  maxFiles?: number;
  maxSize?: number;
  acceptedTypes?: string[];
}

export const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFilesAccepted,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ['.md', '.markdown']
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setUploadedFiles(prev => [...prev, ...acceptedFiles]);
    onFilesAccepted(acceptedFiles);
  }, [onFilesAccepted]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/markdown': acceptedTypes
    },
    maxFiles,
    maxSize
  });

  return (
    <div className="w-full">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer
          transition-colors duration-200
          ${isDragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
          }
        `}
      >
        <input {...getInputProps()} />
        <div className="space-y-4">
          <div className="text-4xl">📁</div>
          <div>
            <p className="text-lg font-medium">
              {isDragActive
                ? '放開檔案以上傳'
                : '拖拽檔案到此處或點擊選擇'
              }
            </p>
            <p className="text-sm text-gray-500 mt-2">
              支援 .md 和 .markdown 檔案，最大 {maxSize / 1024 / 1024}MB
            </p>
          </div>
        </div>
      </div>

      {uploadedFiles.length > 0 && (
        <FileList
          files={uploadedFiles}
          onRemove={(index) => {
            setUploadedFiles(prev => prev.filter((_, i) => i !== index));
          }}
        />
      )}
    </div>
  );
};
```

**資料夾上傳支援**：
```typescript
// frontend/components/FileUpload/FolderUpload.tsx
export const FolderUpload: React.FC = () => {
  const handleFolderSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const markdownFiles = files.filter(file =>
      file.name.endsWith('.md') || file.name.endsWith('.markdown')
    );

    // 保持資料夾結構
    const fileStructure = buildFileStructure(markdownFiles);
    onFolderSelected(fileStructure);
  };

  return (
    <div className="mt-4">
      <label className="block">
        <input
          type="file"
          webkitdirectory=""
          multiple
          onChange={handleFolderSelect}
          className="hidden"
        />
        <div className="btn btn-outline">
          📂 選擇資料夾
        </div>
      </label>
    </div>
  );
};

function buildFileStructure(files: File[]): FileStructure {
  const structure: FileStructure = {};

  files.forEach(file => {
    const pathParts = file.webkitRelativePath.split('/');
    let current = structure;

    pathParts.forEach((part, index) => {
      if (index === pathParts.length - 1) {
        // 檔案
        current[part] = file;
      } else {
        // 資料夾
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part] as FileStructure;
      }
    });
  });

  return structure;
}
```

#### 任務4.2：轉換配置介面
**目標**：提供直觀的轉換選項配置

**主題選擇器**：
```typescript
// frontend/components/Configuration/ThemeSelector.tsx
interface Theme {
  id: string;
  name: string;
  description: string;
  preview: string;
  colors: {
    primary: string;
    secondary: string;
  };
}

export const ThemeSelector: React.FC<{
  selectedTheme: string;
  onThemeChange: (themeId: string) => void;
}> = ({ selectedTheme, onThemeChange }) => {
  const themes: Theme[] = [
    {
      id: 'default',
      name: '預設主題',
      description: '簡潔專業，適合正式文檔',
      preview: '🔵',
      colors: { primary: '#2563eb', secondary: '#3b82f6' }
    },
    {
      id: 'nature',
      name: '自然主題',
      description: '自然風格，體現生態和諧',
      preview: '🌿',
      colors: { primary: '#059669', secondary: '#10b981' }
    },
    {
      id: 'space',
      name: '太空主題',
      description: '太空科幻感，激發想像力',
      preview: '⭐',
      colors: { primary: '#7c3aed', secondary: '#8b5cf6' }
    },
    {
      id: 'tech',
      name: '科技主題',
      description: '現代科技感，體現數位時代',
      preview: '🔷',
      colors: { primary: '#0891b2', secondary: '#06b6d4' }
    },
    {
      id: 'sport',
      name: '運動主題',
      description: '運動活力感，激發競爭精神',
      preview: '🏆',
      colors: { primary: '#dc2626', secondary: '#ef4444' }
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">選擇主題</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {themes.map(theme => (
          <div
            key={theme.id}
            className={`
              border rounded-lg p-4 cursor-pointer transition-all
              ${selectedTheme === theme.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
              }
            `}
            onClick={() => onThemeChange(theme.id)}
          >
            <div className="flex items-center space-x-3">
              <div className="text-2xl">{theme.preview}</div>
              <div>
                <h4 className="font-medium">{theme.name}</h4>
                <p className="text-sm text-gray-600">{theme.description}</p>
              </div>
            </div>
            <div className="flex space-x-2 mt-3">
              <div
                className="w-4 h-4 rounded"
                style={{ backgroundColor: theme.colors.primary }}
              />
              <div
                className="w-4 h-4 rounded"
                style={{ backgroundColor: theme.colors.secondary }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
```

**分頁選項配置**：
```typescript
// frontend/components/Configuration/PaginationOptions.tsx
export const PaginationOptions: React.FC<{
  options: PaginationConfig;
  onChange: (options: PaginationConfig) => void;
}> = ({ options, onChange }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">分頁設定</h3>

      <div>
        <label className="block text-sm font-medium mb-2">分頁模式</label>
        <select
          value={options.mode}
          onChange={(e) => onChange({...options, mode: e.target.value})}
          className="w-full border rounded-md px-3 py-2"
        >
          <option value="balanced">平衡模式 - 平衡內容完整性與頁面密度</option>
          <option value="compact">緊湊模式 - 最大化頁面利用率</option>
          <option value="integrity">完整性模式 - 最大化內容邏輯完整性</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          練習區塊處理
        </label>
        <select
          value={options.exerciseHandling}
          onChange={(e) => onChange({...options, exerciseHandling: e.target.value})}
          className="w-full border rounded-md px-3 py-2"
        >
          <option value="protect">完全保護 - 練習區塊不分頁</option>
          <option value="question-break">題目間分頁 - 允許在題目間分頁</option>
          <option value="free">自由分頁 - 允許內部分頁</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          表格處理
        </label>
        <select
          value={options.tableHandling}
          onChange={(e) => onChange({...options, tableHandling: e.target.value})}
          className="w-full border rounded-md px-3 py-2"
        >
          <option value="protect">完整保護 - 表格不分頁</option>
          <option value="row-break">行間分頁 - 允許在行間分頁</option>
          <option value="free">自由分頁 - 允許任意分頁</option>
        </select>
      </div>
    </div>
  );
};
```

#### 任務4.3：轉換進度追蹤
**目標**：提供即時的轉換進度反饋

**進度追蹤組件**：
```typescript
// frontend/components/Conversion/ConversionProgress.tsx
interface ConversionTask {
  id: string;
  filename: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  error?: string;
}

export const ConversionProgress: React.FC<{
  tasks: ConversionTask[];
  onDownload: (taskId: string, format: 'html' | 'pdf') => void;
}> = ({ tasks, onDownload }) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">轉換進度</h3>

      {tasks.map(task => (
        <div key={task.id} className="border rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="font-medium">{task.filename}</span>
            <StatusBadge status={task.status} />
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.progress}%` }}
            />
          </div>

          {task.status === 'completed' && (
            <div className="flex space-x-2">
              <button
                onClick={() => onDownload(task.id, 'html')}
                className="btn btn-sm btn-outline"
              >
                📄 下載 HTML
              </button>
              <button
                onClick={() => onDownload(task.id, 'pdf')}
                className="btn btn-sm btn-primary"
              >
                📑 下載 PDF
              </button>
            </div>
          )}

          {task.status === 'failed' && task.error && (
            <div className="text-red-600 text-sm mt-2">
              錯誤：{task.error}
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

const StatusBadge: React.FC<{ status: ConversionTask['status'] }> = ({ status }) => {
  const statusConfig = {
    pending: { text: '等待中', color: 'bg-gray-500' },
    processing: { text: '處理中', color: 'bg-blue-500' },
    completed: { text: '完成', color: 'bg-green-500' },
    failed: { text: '失敗', color: 'bg-red-500' }
  };

  const config = statusConfig[status];

  return (
    <span className={`px-2 py-1 rounded text-white text-xs ${config.color}`}>
      {config.text}
    </span>
  );
};
```

### 第五階段：後端服務整合（週9-10）

#### 任務5.1：FastAPI後端架構
**目標**：建立完整的後端API服務

**主要API路由**：
```python
# backend/api/routes/conversion.py
from fastapi import APIRouter, UploadFile, File, Form, BackgroundTasks
from typing import List, Optional
import uuid

router = APIRouter(prefix="/api/conversion", tags=["conversion"])

@router.post("/upload")
async def upload_files(
    files: List[UploadFile] = File(...),
    theme: str = Form("default"),
    pagination_mode: str = Form("balanced"),
    exercise_handling: str = Form("protect"),
    table_handling: str = Form("protect")
):
    """上傳檔案並開始轉換任務"""
    task_id = str(uuid.uuid4())

    # 保存上傳的檔案
    file_paths = await save_uploaded_files(files, task_id)

    # 建立轉換任務
    conversion_options = {
        'theme': theme,
        'pagination_mode': pagination_mode,
        'exercise_handling': exercise_handling,
        'table_handling': table_handling
    }

    # 啟動背景任務
    background_tasks.add_task(
        process_conversion_task,
        task_id,
        file_paths,
        conversion_options
    )

    return {
        "task_id": task_id,
        "status": "pending",
        "files_count": len(files)
    }

@router.get("/status/{task_id}")
async def get_conversion_status(task_id: str):
    """獲取轉換任務狀態"""
    task_status = await get_task_status(task_id)
    return task_status

@router.get("/download/{task_id}/{format}")
async def download_result(task_id: str, format: str):
    """下載轉換結果"""
    if format not in ['html', 'pdf']:
        raise HTTPException(status_code=400, detail="Invalid format")

    file_path = get_result_file_path(task_id, format)
    return FileResponse(file_path)

async def process_conversion_task(
    task_id: str,
    file_paths: List[str],
    options: dict
):
    """背景處理轉換任務"""
    try:
        await update_task_status(task_id, "processing", 0)

        processor = MarkdownProcessor(theme=options['theme'])
        pdf_generator = PDFGenerator()

        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            # 讀取Markdown內容
            with open(file_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()

            # 處理Markdown
            processed_content = processor.process(markdown_content)

            # 生成HTML
            html_output = processed_content.html
            css_output = processed_content.css

            # 生成PDF
            pdf_output = pdf_generator.generate_pdf(
                html_output,
                css_output,
                options
            )

            # 保存結果
            result_files = await save_conversion_results(
                task_id,
                file_path,
                html_output,
                pdf_output
            )
            results.append(result_files)

            # 更新進度
            progress = int((i + 1) / total_files * 100)
            await update_task_status(task_id, "processing", progress)

        await update_task_status(task_id, "completed", 100, results)

    except Exception as e:
        await update_task_status(task_id, "failed", 0, error=str(e))
```

**任務管理服務**：
```python
# backend/services/task_manager.py
import asyncio
import json
from typing import Dict, Any, Optional
from datetime import datetime

class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}

    async def create_task(self, task_id: str, file_count: int) -> None:
        self.tasks[task_id] = {
            'id': task_id,
            'status': 'pending',
            'progress': 0,
            'file_count': file_count,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'results': [],
            'error': None
        }

    async def update_task_status(
        self,
        task_id: str,
        status: str,
        progress: int,
        results: Optional[list] = None,
        error: Optional[str] = None
    ) -> None:
        if task_id in self.tasks:
            self.tasks[task_id].update({
                'status': status,
                'progress': progress,
                'updated_at': datetime.now().isoformat()
            })

            if results:
                self.tasks[task_id]['results'] = results

            if error:
                self.tasks[task_id]['error'] = error

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        return self.tasks.get(task_id)

    async def cleanup_old_tasks(self, max_age_hours: int = 24) -> None:
        """清理舊任務"""
        current_time = datetime.now()
        tasks_to_remove = []

        for task_id, task_data in self.tasks.items():
            created_at = datetime.fromisoformat(task_data['created_at'])
            age_hours = (current_time - created_at).total_seconds() / 3600

            if age_hours > max_age_hours:
                tasks_to_remove.append(task_id)

        for task_id in tasks_to_remove:
            del self.tasks[task_id]

# 全局任務管理器實例
task_manager = TaskManager()
```

#### 任務5.2：檔案管理服務
**目標**：處理檔案上傳、存儲和下載

**檔案管理器**：
```python
# backend/services/file_manager.py
import os
import shutil
import aiofiles
from pathlib import Path
from typing import List
from fastapi import UploadFile

class FileManager:
    def __init__(self, base_upload_dir: str = "uploads"):
        self.base_upload_dir = Path(base_upload_dir)
        self.base_upload_dir.mkdir(exist_ok=True)

    async def save_uploaded_files(
        self,
        files: List[UploadFile],
        task_id: str
    ) -> List[str]:
        """保存上傳的檔案"""
        task_dir = self.base_upload_dir / task_id
        task_dir.mkdir(exist_ok=True)

        saved_files = []

        for file in files:
            file_path = task_dir / file.filename

            async with aiofiles.open(file_path, 'wb') as f:
                content = await file.read()
                await f.write(content)

            saved_files.append(str(file_path))

        return saved_files

    async def save_conversion_results(
        self,
        task_id: str,
        original_filename: str,
        html_content: str,
        pdf_content: bytes
    ) -> dict:
        """保存轉換結果"""
        results_dir = self.base_upload_dir / task_id / "results"
        results_dir.mkdir(exist_ok=True)

        base_name = Path(original_filename).stem

        # 保存HTML
        html_path = results_dir / f"{base_name}.html"
        async with aiofiles.open(html_path, 'w', encoding='utf-8') as f:
            await f.write(html_content)

        # 保存PDF
        pdf_path = results_dir / f"{base_name}.pdf"
        async with aiofiles.open(pdf_path, 'wb') as f:
            await f.write(pdf_content)

        return {
            'original_filename': original_filename,
            'html_path': str(html_path),
            'pdf_path': str(pdf_path)
        }

    def get_result_file_path(self, task_id: str, filename: str) -> str:
        """獲取結果檔案路徑"""
        return str(self.base_upload_dir / task_id / "results" / filename)

    async def cleanup_task_files(self, task_id: str) -> None:
        """清理任務檔案"""
        task_dir = self.base_upload_dir / task_id
        if task_dir.exists():
            shutil.rmtree(task_dir)

# 全局檔案管理器實例
file_manager = FileManager()
```

### 第六階段：系統整合與測試（週11-12）

#### 任務6.1：端到端整合
**目標**：整合前後端，實現完整的工作流程

**主應用程式**：
```python
# backend/main.py
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import asyncio

from api.routes import conversion, themes
from services.task_manager import task_manager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 啟動時執行
    # 啟動定期清理任務
    cleanup_task = asyncio.create_task(periodic_cleanup())
    yield
    # 關閉時執行
    cleanup_task.cancel()

app = FastAPI(
    title="教育材料轉換系統",
    description="香港學生教育材料 Markdown 轉 HTML/PDF 系統",
    version="3.0.0",
    lifespan=lifespan
)

# CORS設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # Next.js開發服務器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 路由註冊
app.include_router(conversion.router)
app.include_router(themes.router)

# 靜態檔案服務
app.mount("/static", StaticFiles(directory="static"), name="static")

async def periodic_cleanup():
    """定期清理舊任務"""
    while True:
        try:
            await task_manager.cleanup_old_tasks(max_age_hours=24)
            await asyncio.sleep(3600)  # 每小時執行一次
        except asyncio.CancelledError:
            break
        except Exception as e:
            print(f"清理任務錯誤: {e}")
            await asyncio.sleep(3600)

@app.get("/")
async def root():
    return {"message": "教育材料轉換系統 API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

**前端主頁面**：
```typescript
// frontend/pages/index.tsx
import { useState, useCallback } from 'react';
import { FileUploadZone } from '../components/FileUpload/FileUploadZone';
import { ThemeSelector } from '../components/Configuration/ThemeSelector';
import { PaginationOptions } from '../components/Configuration/PaginationOptions';
import { ConversionProgress } from '../components/Conversion/ConversionProgress';

interface ConversionConfig {
  theme: string;
  paginationMode: string;
  exerciseHandling: string;
  tableHandling: string;
}

export default function HomePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [config, setConfig] = useState<ConversionConfig>({
    theme: 'default',
    paginationMode: 'balanced',
    exerciseHandling: 'protect',
    tableHandling: 'protect'
  });
  const [conversionTasks, setConversionTasks] = useState([]);
  const [isConverting, setIsConverting] = useState(false);

  const handleFilesSelected = useCallback((newFiles: File[]) => {
    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const handleStartConversion = async () => {
    if (files.length === 0) return;

    setIsConverting(true);

    try {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      formData.append('theme', config.theme);
      formData.append('pagination_mode', config.paginationMode);
      formData.append('exercise_handling', config.exerciseHandling);
      formData.append('table_handling', config.tableHandling);

      const response = await fetch('/api/conversion/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (response.ok) {
        // 開始輪詢任務狀態
        pollTaskStatus(result.task_id);
      } else {
        throw new Error(result.detail || '轉換失敗');
      }
    } catch (error) {
      console.error('轉換錯誤:', error);
      alert('轉換失敗，請重試');
    } finally {
      setIsConverting(false);
    }
  };

  const pollTaskStatus = async (taskId: string) => {
    const poll = async () => {
      try {
        const response = await fetch(`/api/conversion/status/${taskId}`);
        const status = await response.json();

        setConversionTasks(prev => {
          const updated = [...prev];
          const index = updated.findIndex(task => task.id === taskId);
          if (index >= 0) {
            updated[index] = status;
          } else {
            updated.push(status);
          }
          return updated;
        });

        if (status.status === 'processing') {
          setTimeout(poll, 1000); // 每秒輪詢
        }
      } catch (error) {
        console.error('狀態查詢錯誤:', error);
      }
    };

    poll();
  };

  const handleDownload = async (taskId: string, format: 'html' | 'pdf') => {
    try {
      const response = await fetch(`/api/conversion/download/${taskId}/${format}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `converted.${format}`;
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('下載錯誤:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            教育材料轉換系統
          </h1>
          <p className="text-gray-600">
            將 Markdown 教材轉換為精美的 HTML 和 PDF 格式
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 檔案上傳區域 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">上傳檔案</h2>
              <FileUploadZone onFilesAccepted={handleFilesSelected} />
            </div>
          </div>

          {/* 配置區域 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <ThemeSelector
                selectedTheme={config.theme}
                onThemeChange={(theme) => setConfig(prev => ({...prev, theme}))}
              />
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <PaginationOptions
                options={{
                  mode: config.paginationMode,
                  exerciseHandling: config.exerciseHandling,
                  tableHandling: config.tableHandling
                }}
                onChange={(options) => setConfig(prev => ({...prev, ...options}))}
              />
            </div>

            <button
              onClick={handleStartConversion}
              disabled={files.length === 0 || isConverting}
              className="w-full btn btn-primary"
            >
              {isConverting ? '轉換中...' : '開始轉換'}
            </button>
          </div>
        </div>

        {/* 轉換進度區域 */}
        {conversionTasks.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow-md p-6">
            <ConversionProgress
              tasks={conversionTasks}
              onDownload={handleDownload}
            />
          </div>
        )}
      </div>
    </div>
  );
}
```

#### 任務6.2：測試策略實施
**目標**：建立完整的測試體系

**單元測試範例**：
```python
# tests/test_markdown_processor.py
import pytest
from processor.markdown_processor.core import MarkdownProcessor
from processor.theme_engine.core import ThemeEngine

class TestMarkdownProcessor:
    def setup_method(self):
        self.processor = MarkdownProcessor(theme="default")

    def test_basic_markdown_processing(self):
        markdown_content = """
# 測試標題

這是一個段落。

## 二級標題

- 項目一
- 項目二

1. 有序項目一
2. 有序項目二
"""
        result = self.processor.process(markdown_content)
        assert "<h1>" in result.html
        assert "<h2>" in result.html
        assert "<ul>" in result.html
        assert "<ol>" in result.html

    def test_fill_blank_processing(self):
        markdown_content = "請填寫答案：____"
        result = self.processor.process(markdown_content)
        assert "fill-blank" in result.html

    def test_exercise_block_identification(self):
        markdown_content = """
### 練習一：基礎概念

1. 第一題
2. 第二題
"""
        result = self.processor.process(markdown_content)
        assert "exercise-block" in result.html

class TestThemeEngine:
    def test_svg_number_generation(self):
        theme_engine = ThemeEngine("default")
        svg = theme_engine.svg_generator.generate_number_svg(1)
        assert "svg" in svg.lower()
        assert "circle" in svg.lower()

    def test_theme_css_generation(self):
        theme_engine = ThemeEngine("nature")
        css = theme_engine.get_css()
        assert "#059669" in css  # Nature theme primary color
```

**整合測試範例**：
```python
# tests/test_integration.py
import pytest
import asyncio
from fastapi.testclient import TestClient
from backend.main import app

client = TestClient(app)

class TestConversionAPI:
    def test_upload_and_convert(self):
        # 準備測試檔案
        test_markdown = """
# 測試文檔

這是一個測試文檔。

## 練習

### 練習一：選擇題

1. 問題一
2. 問題二
"""

        files = {
            'files': ('test.md', test_markdown, 'text/markdown')
        }
        data = {
            'theme': 'default',
            'pagination_mode': 'balanced'
        }

        response = client.post("/api/conversion/upload", files=files, data=data)
        assert response.status_code == 200

        result = response.json()
        assert "task_id" in result
        assert result["status"] == "pending"

    def test_task_status_endpoint(self):
        # 這需要先有一個有效的task_id
        response = client.get("/api/conversion/status/test-task-id")
        # 根據實際實現調整斷言
```

### 第七階段：部署與優化（週13-14）

#### 任務7.1：Docker容器化
**目標**：建立可部署的容器化環境

**Docker配置**：
```dockerfile
# Dockerfile.backend
FROM python:3.11-slim

WORKDIR /app

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    build-essential \
    libpango-1.0-0 \
    libharfbuzz0b \
    libpangoft2-1.0-0 \
    libfontconfig1 \
    libcairo2 \
    libgdk-pixbuf2.0-0 \
    libxml2 \
    libxslt1.1 \
    && rm -rf /var/lib/apt/lists/*

# 複製依賴檔案
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 複製應用程式碼
COPY . .

# 建立上傳目錄
RUN mkdir -p uploads

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Dockerfile.frontend
FROM node:18-alpine

WORKDIR /app

# 複製package檔案
COPY package*.json ./
RUN npm ci --only=production

# 複製源碼
COPY . .

# 建構應用
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

**Docker Compose配置**：
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.backend
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend
    restart: unless-stopped

volumes:
  uploads:
```

#### 任務7.2：性能優化
**目標**：優化系統性能和用戶體驗

**後端性能優化**：
```python
# backend/core/performance.py
import asyncio
from concurrent.futures import ThreadPoolExecutor
from functools import lru_cache

class PerformanceOptimizer:
    def __init__(self):
        self.thread_pool = ThreadPoolExecutor(max_workers=4)

    async def process_files_parallel(self, file_paths: list, processor_func):
        """並行處理多個檔案"""
        loop = asyncio.get_event_loop()
        tasks = []

        for file_path in file_paths:
            task = loop.run_in_executor(
                self.thread_pool,
                processor_func,
                file_path
            )
            tasks.append(task)

        return await asyncio.gather(*tasks)

    @lru_cache(maxsize=128)
    def get_cached_theme_css(self, theme_name: str) -> str:
        """緩存主題CSS"""
        theme_engine = ThemeEngine(theme_name)
        return theme_engine.get_css()

    @lru_cache(maxsize=256)
    def get_cached_svg_number(self, number: int, theme: str) -> str:
        """緩存SVG數字"""
        theme_engine = ThemeEngine(theme)
        return theme_engine.svg_generator.generate_number_svg(number)
```

**前端性能優化**：
```typescript
// frontend/hooks/useOptimizedConversion.ts
import { useState, useCallback, useMemo } from 'react';
import { debounce } from 'lodash';

export const useOptimizedConversion = () => {
  const [conversionState, setConversionState] = useState({
    files: [],
    config: {},
    tasks: []
  });

  // 防抖的配置更新
  const debouncedConfigUpdate = useMemo(
    () => debounce((newConfig) => {
      setConversionState(prev => ({
        ...prev,
        config: { ...prev.config, ...newConfig }
      }));
    }, 300),
    []
  );

  // 批量檔案處理
  const handleBatchUpload = useCallback(async (files: File[]) => {
    const chunks = chunkArray(files, 5); // 每次處理5個檔案

    for (const chunk of chunks) {
      await processFileChunk(chunk);
      // 添加小延遲避免服務器過載
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }, []);

  return {
    conversionState,
    debouncedConfigUpdate,
    handleBatchUpload
  };
};

function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}
```

## 部署指南

### 開發環境設置
```bash
# 1. 克隆項目
git clone <repository-url>
cd education-material-converter

# 2. 設置後端
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt

# 3. 設置前端
cd ../frontend
npm install

# 4. 啟動開發服務器
# 終端1：後端
cd backend
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 終端2：前端
cd frontend
npm run dev
```

### 生產環境部署
```bash
# 使用Docker Compose
docker-compose up -d

# 或者手動部署
# 1. 建構前端
cd frontend
npm run build

# 2. 配置Nginx
# 3. 啟動後端服務
cd backend
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 項目管理

### 開發里程碑
- **週1-2**：基礎架構完成
- **週3-4**：核心處理引擎完成
- **週5-6**：PDF生成優化完成
- **週7-8**：用戶Portal完成
- **週9-10**：後端服務整合完成
- **週11-12**：系統整合與測試完成
- **週13-14**：部署與優化完成

### 質量控制檢查點
1. **代碼審查**：每個主要功能完成後
2. **單元測試**：每個模組開發完成後
3. **整合測試**：每個階段完成後
4. **用戶測試**：系統整合完成後
5. **性能測試**：部署前

### 風險管理
- **技術風險**：定期技術評估，準備備選方案
- **進度風險**：每週進度檢查，及時調整計劃
- **質量風險**：持續測試，自動化質量檢查

## 總結

本重建計劃提供了完整的開發路線圖，從基礎架構到最終部署的每個步驟都有詳細說明。計劃專門針對香港10-17歲學生的教育材料需求，整合了現有的技術分析和改進建議。

AI助手可以按照此計劃逐步執行開發工作，每個階段都有明確的目標、具體的實施步驟和可驗證的交付成果。計劃確保了系統的可擴展性、可維護性和用戶體驗的優化。
