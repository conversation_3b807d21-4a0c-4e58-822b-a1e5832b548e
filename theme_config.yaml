# 教育材料轉換系統 - 主題配置檔案
# ================================================
# 此檔案包含所有主題化元素的可調整參數
# 修改此檔案後，系統會自動應用新的主題設定

# 主題定義
themes:
  # ========================================
  # 經典主題 (Default)
  # ========================================
  default:
    name: "經典主題"
    description: "專業的藍色主題，適合正式文檔和商務材料"
    category: "professional"
    
    # 顏色系統
    colors:
      primary: "#1e40af"        # 主要藍色（更深，提高對比度）
      secondary: "#374151"      # 次要灰色（更深）
      accent: "#d97706"         # 強調橙色（更深）
      background: "#ffffff"     # 背景白色
      text: "#111827"          # 文字深黑（提高對比度）
      border: "#e5e7eb"        # 邊框淺灰
    
    # 漸變效果
    gradients:
      primary: "linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%)"
      accent: "linear-gradient(135deg, #d97706 0%, #b45309 100%)"
    
    # 陰影效果
    shadows:
      primary: "0 4px 12px rgba(30, 64, 175, 0.15)"
      accent: "0 2px 8px rgba(217, 119, 6, 0.2)"
    
    # 列表圖標系統（5個層級）
    list_icons:
      level_1: "🔵"           # 主列表：藍色圓形
      level_2: "🔹"           # 子列表：藍色菱形
      level_3: "▪️"            # 三級列表：小方塊
      level_4: "•"            # 四級列表：圓點
      level_5: "‣"            # 五級列表：箭頭
    
    # 教育元素樣式
    educational_elements:
      fill_blank_style: "solid"     # 填空線樣式：實線
      math_bg_color: "#f8fafc"      # 數學公式背景
      code_bg_color: "#f1f5f9"      # 代碼背景
      quote_bg_color: "#eff6ff"     # 引用背景

  # ========================================
  # 自然主題 (Nature)
  # ========================================
  nature:
    name: "自然主題"
    description: "清新的綠色主題，適合環境科學和生物學內容"
    category: "organic"
    
    # 顏色系統
    colors:
      primary: "#047857"        # 主要綠色（更深，提高對比度）
      secondary: "#374151"      # 次要灰色（更深）
      accent: "#b45309"         # 強調橙色（更深）
      background: "#fefffe"     # 背景微綠白
      text: "#111827"          # 文字深黑（提高對比度）
      border: "#d1fae5"        # 邊框淺綠
    
    # 漸變效果
    gradients:
      primary: "linear-gradient(135deg, #047857 0%, #065f46 100%)"
      accent: "linear-gradient(135deg, #b45309 0%, #92400e 100%)"
    
    # 陰影效果
    shadows:
      primary: "0 4px 12px rgba(4, 120, 87, 0.15)"
      accent: "0 2px 8px rgba(180, 83, 9, 0.2)"
    
    # 列表圖標系統（5個層級）
    list_icons:
      level_1: "🌿"           # 主列表：葉子
      level_2: "🌱"           # 子列表：幼苗
      level_3: "🍃"           # 三級列表：飄葉
      level_4: "🌾"           # 四級列表：麥穗
      level_5: "🌸"           # 五級列表：花朵
    
    # 教育元素樣式
    educational_elements:
      fill_blank_style: "dotted"    # 填空線樣式：點線
      math_bg_color: "#f0fdf4"      # 數學公式背景：淺綠
      code_bg_color: "#ecfdf5"      # 代碼背景：極淺綠
      quote_bg_color: "#f0fdf4"     # 引用背景：淺綠

  # ========================================
  # 科技主題 (Tech)
  # ========================================
  tech:
    name: "科技主題"
    description: "現代的紫色主題，適合程式設計和工程學內容"
    category: "futuristic"
    
    # 顏色系統
    colors:
      primary: "#6d28d9"        # 主要紫色（更深，提高對比度）
      secondary: "#374151"      # 次要灰色（更深）
      accent: "#0891b2"         # 強調青色（更深）
      background: "#fefffe"     # 背景白色
      text: "#111827"          # 文字深黑（提高對比度）
      border: "#e0e7ff"        # 邊框淺紫
    
    # 漸變效果
    gradients:
      primary: "linear-gradient(135deg, #6d28d9 0%, #5b21b6 100%)"
      accent: "linear-gradient(135deg, #0891b2 0%, #0e7490 100%)"
    
    # 陰影效果
    shadows:
      primary: "0 4px 12px rgba(109, 40, 217, 0.15)"
      accent: "0 2px 8px rgba(8, 145, 178, 0.2)"
    
    # 列表圖標系統（5個層級）
    list_icons:
      level_1: "⚡"           # 主列表：閃電
      level_2: "🔧"           # 子列表：工具
      level_3: "⚙️"           # 三級列表：齒輪
      level_4: "🔹"           # 四級列表：菱形
      level_5: "▫️"           # 五級列表：空心方塊
    
    # 教育元素樣式
    educational_elements:
      fill_blank_style: "dashed"    # 填空線樣式：虛線
      math_bg_color: "#faf5ff"      # 數學公式背景：淺紫
      code_bg_color: "#f3f4f6"      # 代碼背景：淺灰
      quote_bg_color: "#ede9fe"     # 引用背景：極淺紫

  # ========================================
  # 太空主題 (Space) - 深色主題
  # ========================================
  space:
    name: "太空主題"
    description: "深邃的藍色主題，適合天文學和物理學內容"
    category: "cosmic"
    
    # 顏色系統（深色主題）
    colors:
      primary: "#60a5fa"        # 主要亮藍色（更亮，提高對比度）
      secondary: "#d1d5db"      # 次要淺灰（更亮）
      accent: "#c084fc"         # 強調紫色（更亮）
      background: "#0f172a"     # 背景深藍黑
      text: "#f9fafb"          # 文字更淺（提高對比度）
      border: "#1e293b"        # 邊框深灰藍
    
    # 漸變效果
    gradients:
      primary: "linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%)"
      accent: "linear-gradient(135deg, #c084fc 0%, #a855f7 100%)"
    
    # 陰影效果
    shadows:
      primary: "0 4px 12px rgba(96, 165, 250, 0.3)"
      accent: "0 2px 8px rgba(192, 132, 252, 0.25)"
    
    # 列表圖標系統（5個層級）
    list_icons:
      level_1: "🚀"           # 主列表：火箭
      level_2: "⭐"           # 子列表：星星
      level_3: "🌟"           # 三級列表：閃亮星
      level_4: "✨"           # 四級列表：星花
      level_5: "🔸"           # 五級列表：橙色菱形
    
    # 教育元素樣式
    educational_elements:
      fill_blank_style: "solid"     # 填空線樣式：實線
      math_bg_color: "#1e293b"      # 數學公式背景：深藍灰
      code_bg_color: "#334155"      # 代碼背景：中藍灰
      quote_bg_color: "#1e293b"     # 引用背景：深藍灰

  # ========================================
  # 運動主題 (Sport)
  # ========================================
  sport:
    name: "運動主題"
    description: "活力的紅色主題，適合體育和健康教育內容"
    category: "energetic"
    
    # 顏色系統
    colors:
      primary: "#b91c1c"        # 主要紅色（更深，提高對比度）
      secondary: "#374151"      # 次要灰色（更深）
      accent: "#c2410c"         # 強調橙紅色（更深）
      background: "#ffffff"     # 背景白色
      text: "#111827"          # 文字深黑（提高對比度）
      border: "#fecaca"        # 邊框淺紅
    
    # 漸變效果
    gradients:
      primary: "linear-gradient(135deg, #b91c1c 0%, #991b1b 100%)"
      accent: "linear-gradient(135deg, #c2410c 0%, #9a3412 100%)"
    
    # 陰影效果
    shadows:
      primary: "0 4px 12px rgba(185, 28, 28, 0.15)"
      accent: "0 2px 8px rgba(194, 65, 12, 0.2)"
    
    # 列表圖標系統（5個層級）
    list_icons:
      level_1: "⚽"           # 主列表：足球
      level_2: "🏃"           # 子列表：跑步
      level_3: "🏆"           # 三級列表：獎杯
      level_4: "🥇"           # 四級列表：金牌
      level_5: "🔥"           # 五級列表：火焰
    
    # 教育元素樣式
    educational_elements:
      fill_blank_style: "double"    # 填空線樣式：雙線
      math_bg_color: "#fef2f2"      # 數學公式背景：淺紅
      code_bg_color: "#fef2f2"      # 代碼背景：淺紅
      quote_bg_color: "#fef2f2"     # 引用背景：淺紅

# ========================================
# 全域設定
# ========================================
global_settings:
  # 公司 Logo 設定
  company_logo:
    enabled: true
    path: "TestFile/Company icon.png"
    position: "bottom-right"      # 位置：bottom-left, bottom-right, bottom-center
    size:
      width: "3cm"
      max_height: "1.5cm"
    opacity: 0.8
    margin:
      bottom: "0.5cm"
      right: "1cm"
  
  # PDF 頁面設定
  pdf_settings:
    page_margins:
      top: "1.5cm"
      right: "1.5cm"
      bottom: "3cm"               # 為 logo 預留空間
      left: "1.5cm"
    
  # 填空線樣式定義
  fill_blank_styles:
    solid: "border-bottom: 2px solid {color};"
    dotted: "border-bottom: 2px dotted {color};"
    dashed: "border-bottom: 2px dashed {color};"
    double: "border-bottom: 3px double {color};"

# ========================================
# 使用說明
# ========================================
# 1. 修改顏色：直接編輯 colors 區段中的十六進制顏色值
# 2. 更換圖標：修改 list_icons 區段中的 emoji 字符
# 3. 調整樣式：修改 educational_elements 區段中的設定
# 4. 自定義漸變：編輯 gradients 區段中的 CSS 漸變定義
# 5. 調整陰影：修改 shadows 區段中的 CSS 陰影參數
# 6. Logo 設定：在 global_settings.company_logo 中調整 logo 相關設定
# 
# 注意事項：
# - 顏色值必須使用有效的十六進制格式（如 #ffffff）
# - 修改後需要重新生成 HTML 檔案才能看到效果
# - 建議在修改前備份此檔案
