<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教育材料轉換系統 - 週 1 綜合測試文件</title>
    
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$']],
                displayMath: [['$$', '$$']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    
    <style>
        /* CSS 變數定義 */
        :root {
            --theme-primary: #047857;
            --theme-secondary: #374151;
            --theme-accent: #b45309;
            --theme-background: #fefffe;
            --theme-text: #111827;
            --theme-border: #d1fae5;
            --theme-gradient-primary: linear-gradient(135deg, #047857 0%, #065f46 100%);
            --theme-gradient-accent: linear-gradient(135deg, #b45309 0%, #92400e 100%);
            --theme-shadow-primary: 0 4px 12px rgba(4, 120, 87, 0.15);
            --theme-shadow-accent: 0 2px 8px rgba(180, 83, 9, 0.2);
        }

        /* 基礎佈局 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans TC', sans-serif;
            line-height: 1.7;
            color: var(--theme-text);
            background: white;
            max-width: 900px;
            margin: 0 auto;
            padding: 30px 20px;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 100%;
            background: white;
            border-radius: 12px;
            padding: 20px;
            /* 移除邊框和陰影效果，背景改為白色以降低印刷成本 */
        }

        /* 增強標題樣式 */
        .theme-h1 {
            color: var(--theme-primary);
            /* 移除 background-clip: text 以確保 PDF 兼容性 */
            background: transparent;
            border-bottom: 3px solid var(--theme-primary);
            padding-bottom: 15px;
            margin-bottom: 25px;
            position: relative;
            font-weight: 700;
        }

        /* H1 標題裝飾線已移除 */

        .theme-h2 {
            color: var(--theme-primary);
            border-left: 6px solid var(--theme-accent);
            padding-left: 20px;
            margin: 25px 0 15px 0;
            background: transparent;
            padding-top: 10px;
            padding-bottom: 10px;
            border-radius: 0 8px 8px 0;
            font-weight: 600;
        }

        .theme-h3 {
            color: var(--theme-secondary);
            border-bottom: 2px dotted var(--theme-accent);
            padding-bottom: 8px;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        /* 增強列表樣式 */
        .theme-list {
            list-style-type: none;
            padding-left: 0;
            margin: 15px 0;
        }

        .theme-list li {
            margin: 8px 0;
            padding-left: 35px;
            position: relative;
            line-height: 1.6;
        }

        /* 多層級列表圖標 */
        .theme-list li::before {
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.2em;
            line-height: 1.6;
        }

        .theme-list li::before { content: "🌿 "; }
        .theme-list .theme-list li::before { content: "🌱 "; }
        .theme-list .theme-list .theme-list li::before { content: "🍃 "; }
        .theme-list .theme-list .theme-list .theme-list li::before { content: "🌾 "; }
        .theme-list .theme-list .theme-list .theme-list .theme-list li::before { content: "🌸 "; }

        /* 檢查清單樣式 */
        .task-list {
            list-style-type: none;
            padding-left: 0;
        }

        .task-list-item {
            margin: 8px 0;
            padding-left: 0 !important;
            position: relative;
        }

        .task-list-item::before {
            display: none !important; /* 隱藏主題圖標 */
        }

        .task-list-checkbox {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: var(--theme-primary);
        }

        /* 增強填空線樣式 */
        .fill-blank {
            border-bottom: 2px dotted #047857;
            display: inline-block;
            min-width: 120px;
            margin: 0 8px;
            padding: 2px 0;
            position: relative;
        }

        /* 圖片增強 */
        img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: var(--theme-shadow-accent);
            transition: transform 0.3s ease;
        }

        img:hover {
            transform: scale(1.02);
        }

        /* 增強表格樣式 */
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 25px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--theme-shadow-primary);
        }

        th, td {
            border: 1px solid var(--theme-border);
            padding: 15px 12px;
            text-align: left;
        }

        th {
            background: var(--theme-gradient-primary);
            color: white;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        tr:nth-child(even) {
            background-color: var(--theme-border);
        }

        /* 增強引用樣式 */
        blockquote {
            border-left: 6px solid var(--theme-accent);
            margin: 25px 0;
            padding: 20px 25px;
            background: #f0fdf4;
            border-radius: 0 8px 8px 0;
            box-shadow: var(--theme-shadow-accent);
            position: relative;
        }

        blockquote::before {
            content: '"';
            font-size: 4em;
            color: var(--theme-accent);
            position: absolute;
            top: -10px;
            left: 10px;
            opacity: 0.3;
        }

        /* 增強代碼樣式 */
        code {
            background-color: #ecfdf5;
            color: var(--theme-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.9em;
            border: 1px solid var(--theme-border);
        }

        pre {
            background-color: #ecfdf5;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid var(--theme-border);
            box-shadow: var(--theme-shadow-accent);
        }

        /* 數學公式區域增強 */
        .MathJax {
            background-color: #f0fdf4 !important;
            padding: 8px 12px !important;
            border-radius: 6px !important;
            border: 1px solid var(--theme-border) !important;
            margin: 5px 0 !important;
        }

        /* 水平線增強 */
        hr {
            border: none;
            height: 3px;
            background: var(--theme-gradient-accent);
            margin: 30px 0;
            border-radius: 2px;
        }

        /* Logo 樣式已移除 - 改為 PDF 後處理策略 */

        /* 響應式設計 */
        @media (max-width: 768px) {
            body { padding: 20px 15px; }
            .container { padding: 15px; }
            .theme-list li { padding-left: 30px; }
            table { font-size: 0.9em; }
            th, td { padding: 10px 8px; }
        }

        
    </style>
</head>
<body>
    <div class="container">
        <h1 class="theme-h1">教育材料轉換系統 - 週 1 綜合測試文件</h1>
<p>這是一個全面的 Markdown 測試文件，用於驗證教育材料轉換系統的各項功能。</p>
<h2 class="theme-h2">1. 基礎格式測試</h2>
<h3 class="theme-h3">1.1 標題層級測試</h3>
<h1 class="theme-h1">一級標題 - 課程主題</h1>
<h2 class="theme-h2">二級標題 - 章節標題</h2>
<h3 class="theme-h3">三級標題 - 小節標題</h3>
<h4 class="theme-h4">四級標題 - 重點內容</h4>
<h5 class="theme-h5">五級標題 - 細節說明</h5>
<h6 class="theme-h6">六級標題 - 補充資料</h6>
<h3 class="theme-h3">1.2 段落和換行測試</h3>
<p>這是第一個段落，包含普通的文字內容。段落之間應該有適當的間距。</p>
<p>這是第二個段落。
這一行使用了強制換行（兩個空格）。
這一行是正常的換行。</p>
<p>這是第三個段落，測試空行處理。</p>
<h2 class="theme-h2">2. 文字樣式測試</h2>
<h3 class="theme-h3">2.1 基本文字格式</h3>
<p><strong>這是粗體文字（雙星號）</strong>
<strong>這是粗體文字（雙下劃線）</strong></p>
<p><em>這是斜體文字（單星號）</em>
<em>這是斜體文字（單下劃線）</em></p>
<p><em><strong>這是粗斜體文字（三星號）</strong></em>
<em><strong>這是粗斜體文字（三下劃線）</strong></em></p>
<p><code>這是行內程式碼</code></p>
<h3 class="theme-h3">2.2 中英文混排測試</h3>
<p>這段文字包含 <strong>English words</strong> 和 <em>中文字符</em>，測試 <code>mixed content</code> 的顯示效果。</p>
<p>重要的 <strong>教學概念</strong> 需要特別 <em>強調</em>，例如：<code>變數 variable</code> 和 <code>函數 function</code>。</p>
<h2 class="theme-h2">3. 列表測試</h2>
<h3 class="theme-h3">3.1 無序列表（使用不同符號）</h3>
<p>使用減號：</p>
<ul class="theme-list theme-list-depth-1">
<li>第一項</li>
<li>第二項</li>
<li>第三項</li>
</ul>
<p>使用星號：</p>
<ul class="theme-list theme-list-depth-1">
<li>項目 A</li>
<li>項目 B</li>
<li>項目 C</li>
</ul>
<p>使用加號：</p>
<ul class="theme-list theme-list-depth-1">
<li>選項 1</li>
<li>選項 2</li>
<li>選項 3</li>
</ul>
<h3 class="theme-h3">3.2 有序列表</h3>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>第一步：準備材料</li>
<li>第二步：開始實驗</li>
<li>第三步：記錄結果</li>
<li>第四步：分析數據</li>
</ol>
<h3 class="theme-h3">3.3 嵌套列表（多層級）</h3>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>
<p><strong>第一層級：主要學習目標</strong></p>
<ol class="theme-list theme-ordered-list theme-list-depth-2">
<li><strong>第二層級：理解基本概念</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-3">
<li><strong>第三層級：理論基礎</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-4">
<li><strong>第四層級：核心概念</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：基本定義</strong></li>
<li><strong>第五層級：重要性質</strong></li>
</ol>
</li>
<li><strong>第四層級：應用範圍</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：實際應用</strong></li>
<li><strong>第五層級：理論延伸</strong></li>
</ol>
</li>
</ol>
</li>
<li><strong>第三層級：實踐應用</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-4">
<li><strong>第四層級：操作技能</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：基礎操作</strong></li>
<li><strong>第五層級：進階技巧</strong></li>
</ol>
</li>
<li><strong>第四層級：分析技能</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：數據分析</strong></li>
<li><strong>第五層級：結果解釋</strong></li>
</ol>
</li>
</ol>
</li>
</ol>
</li>
<li><strong>第二層級：掌握實用技能</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-3">
<li><strong>第三層級：技術能力</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-4">
<li><strong>第四層級：程式設計</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：語法掌握</strong></li>
<li><strong>第五層級：邏輯思維</strong></li>
</ol>
</li>
<li><strong>第四層級：問題解決</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：分析問題</strong></li>
<li><strong>第五層級：設計方案</strong></li>
</ol>
</li>
</ol>
</li>
</ol>
</li>
</ol>
</li>
<li>
<p><strong>第一層級：評估方法</strong></p>
<ol class="theme-list theme-ordered-list theme-list-depth-2">
<li><strong>第二層級：平時表現 (30%)</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-3">
<li><strong>第三層級：課堂參與 (15%)</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-4">
<li><strong>第四層級：發言質量</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：思考深度</strong></li>
<li><strong>第五層級：表達清晰度</strong></li>
</ol>
</li>
<li><strong>第四層級：互動頻率</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：主動提問</strong></li>
<li><strong>第五層級：回答問題</strong></li>
</ol>
</li>
</ol>
</li>
<li><strong>第三層級：作業完成 (15%)</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-4">
<li><strong>第四層級：完成度</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：按時提交</strong></li>
<li><strong>第五層級：內容完整</strong></li>
</ol>
</li>
<li><strong>第四層級：質量評估</strong>
<ol class="theme-list theme-ordered-list theme-list-depth-5">
<li><strong>第五層級：正確性</strong></li>
<li><strong>第五層級：創新性</strong></li>
</ol>
</li>
</ol>
</li>
</ol>
</li>
<li><strong>第二層級：期中考試 (30%)</strong></li>
<li><strong>第二層級：期末考試 (40%)</strong></li>
</ol>
</li>
</ol>
<h3 class="theme-h3">3.4 列表項目內的複雜內容</h3>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>
<p><strong>第一章：導論</strong></p>
<p>這一章介紹課程的基本概念。</p>
<ul class="theme-list theme-list-depth-2">
<li>學習重點：
<ul class="theme-list theme-list-depth-3">
<li>基礎理論</li>
<li>實際應用</li>
</ul>
</li>
</ul>
<pre class="theme-code-block"><code>重要提醒：請仔細閱讀每個章節
</code></pre>
</li>
<li>
<p><strong>第二章：實踐</strong></p>
<p>包含實際操作和練習。</p>
</li>
</ol>
<h2 class="theme-h2">4. 圖片測試</h2>
<h3 class="theme-h3">4.1 多張圖片顯示測試</h3>
<p><strong>第一組圖片：</strong>
<img alt="圖片1" src="photo/Gemini_Generated_Image_1.png"/>
<img alt="圖片2" src="photo/Gemini_Generated_Image_2.png"/>
<img alt="圖片3" src="photo/Gemini_Generated_Image_3.png"/></p>
<p><strong>第二組圖片（重複測試）：</strong>
<img alt="教學圖片A" src="photo/Gemini_Generated_Image_1.png"/>
<img alt="教學圖片B" src="photo/Gemini_Generated_Image_2.png"/>
<img alt="教學圖片C" src="photo/Gemini_Generated_Image_3.png"/></p>
<p><strong>第三組圖片（帶說明）：</strong>
<img alt="示意圖1" src="photo/Gemini_Generated_Image_1.png" title="第一張示意圖"/>
<img alt="示意圖2" src="photo/Gemini_Generated_Image_2.png" title="第二張示意圖"/>
<img alt="示意圖3" src="photo/Gemini_Generated_Image_3.png" title="第三張示意圖"/></p>
<h3 class="theme-h3">4.2 圖片與文字混排測試</h3>
<p>這是一段文字，後面跟著圖片：<img alt="內嵌圖片" src="photo/Gemini_Generated_Image_1.png"/></p>
<p><strong>圖片說明測試：</strong></p>
<p><img alt="教學流程圖" src="photo/Gemini_Generated_Image_1.png" title="教學流程示意圖"/>
<em>圖 1：教學流程示意圖</em></p>
<p><img alt="學習架構圖" src="photo/Gemini_Generated_Image_2.png" title="學習架構示意圖"/>
<em>圖 2：學習架構示意圖</em></p>
<p><img alt="評估方法圖" src="photo/Gemini_Generated_Image_3.png" title="評估方法示意圖"/>
<em>圖 3：評估方法示意圖</em></p>
<h3 class="theme-h3">4.3 圖片數量壓力測試</h3>
<p>連續多張圖片：
<img alt="測試1" src="photo/Gemini_Generated_Image_1.png"/>
<img alt="測試2" src="photo/Gemini_Generated_Image_2.png"/>
<img alt="測試3" src="photo/Gemini_Generated_Image_3.png"/>
<img alt="測試4" src="photo/Gemini_Generated_Image_1.png"/>
<img alt="測試5" src="photo/Gemini_Generated_Image_2.png"/>
<img alt="測試6" src="photo/Gemini_Generated_Image_3.png"/>
<img alt="測試7" src="photo/Gemini_Generated_Image_1.png"/>
<img alt="測試8" src="photo/Gemini_Generated_Image_2.png"/>
<img alt="測試9" src="photo/Gemini_Generated_Image_3.png"/></p>
<h2 class="theme-h2">5. 表格測試</h2>
<h3 class="theme-h3">5.1 基本表格</h3>
<table class="theme-table">
<thead>
<tr>
<th>學生姓名</th>
<th>數學</th>
<th>英文</th>
<th>中文</th>
</tr>
</thead>
<tbody>
<tr>
<td>張小明</td>
<td>85</td>
<td>78</td>
<td>92</td>
</tr>
<tr>
<td>李小華</td>
<td>92</td>
<td>85</td>
<td>88</td>
</tr>
<tr>
<td>王小美</td>
<td>78</td>
<td>92</td>
<td>85</td>
</tr>
</tbody>
</table>
<h3 class="theme-h3">5.2 對齊設定表格</h3>
<table class="theme-table">
<thead>
<tr>
<th style="text-align:left">左對齊</th>
<th style="text-align:center">居中對齊</th>
<th style="text-align:right">右對齊</th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align:left">內容1</td>
<td style="text-align:center">內容2</td>
<td style="text-align:right">內容3</td>
</tr>
<tr>
<td style="text-align:left">較長的內容</td>
<td style="text-align:center">中等內容</td>
<td style="text-align:right">短內容</td>
</tr>
</tbody>
</table>
<h3 class="theme-h3">5.3 表格內格式化文字</h3>
<table class="theme-table">
<thead>
<tr>
<th>科目</th>
<th>重要程度</th>
<th>學習建議</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>數學</strong></td>
<td>⭐⭐⭐⭐⭐</td>
<td><em>每日練習</em></td>
</tr>
<tr>
<td><strong>英文</strong></td>
<td>⭐⭐⭐⭐</td>
<td><code>多聽多說</code></td>
</tr>
<tr>
<td><strong>科學</strong></td>
<td>⭐⭐⭐</td>
<td><strong>理解概念</strong></td>
</tr>
</tbody>
</table>
<h3 class="theme-h3">5.4 課程時間表</h3>
<table class="theme-table">
<thead>
<tr>
<th>時間</th>
<th>星期一</th>
<th>星期二</th>
<th>星期三</th>
<th>星期四</th>
<th>星期五</th>
</tr>
</thead>
<tbody>
<tr>
<td>08:30-09:15</td>
<td>數學</td>
<td>英文</td>
<td>數學</td>
<td>英文</td>
<td>數學</td>
</tr>
<tr>
<td>09:15-10:00</td>
<td>中文</td>
<td>數學</td>
<td>英文</td>
<td>數學</td>
<td>英文</td>
</tr>
<tr>
<td>10:15-11:00</td>
<td>英文</td>
<td>科學</td>
<td>中文</td>
<td>科學</td>
<td>中文</td>
</tr>
<tr>
<td>11:00-11:45</td>
<td>科學</td>
<td>中文</td>
<td>科學</td>
<td>中文</td>
<td>科學</td>
</tr>
</tbody>
</table>
<h2 class="theme-h2">6. 程式碼測試</h2>
<h3 class="theme-h3">6.1 行內程式碼</h3>
<p>在 Python 中，我們使用 <code>print()</code> 函數來輸出內容，例如 <code>print("Hello World")</code>。</p>
<p>變數賦值使用 <code>=</code> 符號，如 <code>x = 10</code> 或 <code>name = "張三"</code>。</p>
<h3 class="theme-h3">6.2 程式碼區塊（縮排式）</h3>
<pre class="theme-code-block"><code>def hello_world():
    print("Hello, World!")
    return "完成"

# 這是縮排式的程式碼區塊
result = hello_world()
</code></pre>
<h3 class="theme-h3">6.3 圍欄式程式碼區塊</h3>
<pre class="theme-code-block"><code>這是一個簡單的程式碼區塊
沒有指定語言
可以包含任何文字內容
</code></pre>
<h3 class="theme-h3">6.4 指定語言的程式碼區塊</h3>
<p>Python 範例：</p>
<pre class="theme-code-block"><code class="language-python">def calculate_average(numbers):
    """計算平均值"""
    if not numbers:
        return 0
    return sum(numbers) / len(numbers)

# 測試數據
scores = [85, 92, 78, 96, 88]
avg = calculate_average(scores)
print(f"平均分數：{avg:.2f}")
</code></pre>
<p>JavaScript 範例：</p>
<pre class="theme-code-block"><code class="language-javascript">function calculateGrade(score) {
    if (score &gt;= 90) return 'A';
    if (score &gt;= 80) return 'B';
    if (score &gt;= 70) return 'C';
    if (score &gt;= 60) return 'D';
    return 'F';
}

// 使用範例
const studentScore = 85;
console.log(`成績等級：${calculateGrade(studentScore)}`);
</code></pre>
<p>HTML 範例：</p>
<pre class="theme-code-block"><code class="language-html">&lt;!DOCTYPE html&gt;
&lt;html lang="zh-TW"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;title&gt;教學網頁&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;歡迎來到線上學習平台&lt;/h1&gt;
    &lt;p&gt;這是一個&lt;strong&gt;教育&lt;/strong&gt;網站。&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;
</code></pre>
<h2 class="theme-h2">7. 引用測試</h2>
<h3 class="theme-h3">7.1 單層引用</h3>
<blockquote class="theme-blockquote">
<p>這是一個重要的教學提醒。學生在學習過程中應該注意理論與實踐的結合。</p>
</blockquote>
<blockquote class="theme-blockquote">
<p><strong>老師提醒</strong>：請務必完成課後練習，這對理解概念非常重要。</p>
</blockquote>
<h3 class="theme-h3">7.2 多層嵌套引用</h3>
<blockquote class="theme-blockquote">
<p>教育專家指出：</p>
<blockquote class="theme-blockquote">
<p>有效的學習需要主動參與和持續練習。</p>
<blockquote class="theme-blockquote">
<p>正如孔子所說：「學而時習之，不亦說乎？」</p>
</blockquote>
</blockquote>
</blockquote>
<blockquote class="theme-blockquote">
<p>現代教育理論強調：</p>
<blockquote class="theme-blockquote">
<p>學習是一個<strong>主動建構</strong>的過程，包括：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>接收新信息</li>
<li>與已有知識連結</li>
<li>實際應用驗證</li>
</ol>
<blockquote class="theme-blockquote">
<p>因此，<em>實踐</em> 是學習的關鍵環節。</p>
</blockquote>
</blockquote>
</blockquote>
<h3 class="theme-h3">7.3 引用內的其他格式</h3>
<blockquote class="theme-blockquote">
<p><strong>重要概念</strong>：變數 (Variable)</p>
<p>在程式設計中，變數是用來儲存數據的容器。例如：</p>
<pre class="theme-code-block"><code class="language-python">name = "學生姓名"
age = 16
</code></pre>
<p><em>注意</em>：變數名稱應該有意義且易於理解。</p>
</blockquote>
<h2 class="theme-h2">8. 線條測試</h2>
<h3 class="theme-h3">8.1 填空線</h3>
<p>請完成以下填空題：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>Python 是一種 <span class="fill-blank"> </span> 程式語言。</li>
<li>變數 <code>x = 10</code> 中，x 的值是 <span class="fill-blank"> </span>。</li>
<li>函數 <code>print()</code> 的作用是 <span class="fill-blank"> </span>。</li>
</ol>
<h3 class="theme-h3">8.2 水平分隔線</h3>
<p>使用三個減號：</p>
<hr/>
<p>使用三個星號：</p>
<hr/>
<p>使用三個下劃線：</p>
<hr/>
<h2 class="theme-h2">9. 教育專用元素</h2>
<h3 class="theme-h3">9.1 填空線變化</h3>
<p>短填空：答案是 <span class="fill-blank"> </span></p>
<p>中等填空：學生的姓名是 <span class="fill-blank"> </span></p>
<p>長填空：請寫出完整的解題過程 <span class="fill-blank"> </span></p>
<h3 class="theme-h3">9.2 數學符號和特殊字符</h3>
<ul class="theme-list theme-list-depth-1">
<li>希臘字母：α β γ δ ε θ λ μ π σ φ ψ ω</li>
<li>數學符號：± × ÷ ≤ ≥ ≠ ≈ ∞ ∑ ∏ √ ∫</li>
<li>上下標：H₂O、E=mc²、x^n、log₂</li>
<li>分數：½ ⅓ ¼ ¾ ⅛</li>
<li>箭頭：→ ← ↑ ↓ ↔ ⇒ ⇔</li>
</ul>
<h3 class="theme-h3">9.3 教學重點標記</h3>
<p>⭐ <strong>重點</strong>：這是需要特別注意的內容</p>
<p>⚠️ <strong>注意</strong>：常見錯誤提醒</p>
<p>💡 <strong>提示</strong>：解題技巧分享</p>
<p>✅ <strong>正確</strong>：標準答案或做法</p>
<p>❌ <strong>錯誤</strong>：需要避免的做法</p>
<p>🔍 <strong>深入</strong>：進階內容探討</p>
<h3 class="theme-h3">9.4 練習題格式</h3>
<p><strong>練習 1：選擇題</strong></p>
<p>下列哪個是正確的 Python 語法？</p>
<p>A) <code>print "Hello"</code>
B) <code>print("Hello")</code>  ✅
C) <code>print{"Hello"}</code>
D) <code>print[Hello]</code></p>
<p><strong>練習 2：填空題</strong></p>
<p>在 Python 中，用來定義函數的關鍵字是 <span class="fill-blank"> </span>。</p>
<p><strong>練習 3：簡答題</strong></p>
<p>請解釋變數和常數的區別。</p>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>練習 4：回答題</strong></p>
<p>請說明程式設計中「迴圈」的概念及其重要性。</p>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>練習 5：論述題</strong></p>
<p>分析比較 Python 和 JavaScript 兩種程式語言的特點，並說明它們各自適用的場景。</p>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>練習 6：計算題</strong></p>
<p>給定一個數列：2, 4, 6, 8, 10...</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>寫出這個數列的通項公式</li>
<li>計算前10項的和</li>
</ol>
<p>解答：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>通項公式：</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="2">
<li>前10項和的計算過程：</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>練習 7：程式設計題</strong></p>
<p>編寫一個 Python 函數，計算給定數字的階乘。</p>
<p>程式碼：</p>
<pre class="theme-code-block"><code class="language-python"># 請在下方寫出完整的函數定義

---
---
---
---

# 測試程式碼

---
---
---
---
</code></pre>
<p><strong>練習 8：分析題</strong></p>
<p>閱讀以下程式碼片段，分析其功能並指出可能的問題：</p>
<pre class="theme-code-block"><code class="language-python">def mystery_function(lst):
    result = []
    for i in range(len(lst)):
        if lst[i] % 2 == 0:
            result.append(lst[i] * 2)
    return result
</code></pre>
<p>分析：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>程式功能說明：</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="2">
<li>可能存在的問題：</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="3">
<li>改進建議：</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<h2 class="theme-h2">10. 中文內容測試</h2>
<h3 class="theme-h3">10.1 繁體中文字符</h3>
<p><strong>傳統文化</strong>：中華文化博大精深，包含豐富的哲學思想、文學藝術和科學技術。</p>
<p><strong>現代教育</strong>：香港的教育制度融合了中西方的教學理念，注重培養學生的創新思維和實踐能力。</p>
<h3 class="theme-h3">10.2 中英文混排</h3>
<p>在 <strong>Computer Science（電腦科學）</strong> 領域中，<em>Algorithm（演算法）</em> 是解決問題的重要工具。</p>
<p>學習 <code>Programming（程式設計）</code> 需要掌握：</p>
<ul class="theme-list theme-list-depth-1">
<li><strong>Syntax（語法）</strong> - 程式語言的規則</li>
<li><strong>Logic（邏輯）</strong> - 解決問題的思路</li>
<li><strong>Practice（實踐）</strong> - 動手編寫程式</li>
</ul>
<h3 class="theme-h3">10.3 中文標點符號</h3>
<p>「學而時習之，不亦說乎？」——《論語》</p>
<p>現代教育強調：學習、思考、實踐；這三者缺一不可。</p>
<p>重要提醒：請注意以下幾點——</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>認真聽講；</li>
<li>積極參與；</li>
<li>按時完成作業。</li>
</ol>
<h3 class="theme-h3">10.4 特殊中文教育術語</h3>
<ul class="theme-list theme-list-depth-1">
<li><strong>學科專有名詞</strong>：數學、物理、化學、生物、地理、歷史</li>
<li><strong>教學方法</strong>：啟發式教學、合作學習、探究式學習、翻轉課堂</li>
<li><strong>評估術語</strong>：形成性評估、總結性評估、同儕評估、自我評估</li>
<li><strong>香港教育</strong>：中學文憑考試（DSE）、校本評核（SBA）、其他學習經歷（OLE）</li>
</ul>
<h2 class="theme-h2">11. 複雜組合測試</h2>
<h3 class="theme-h3">11.1 表格內的列表</h3>
<table class="theme-table">
<thead>
<tr>
<th>學習階段</th>
<th>主要任務</th>
<th>具體要求</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>準備階段</strong></td>
<td>基礎知識學習</td>
<td>• 閱讀教材<br/>• 理解概念<br/>• 記錄重點</td>
</tr>
<tr>
<td><strong>實踐階段</strong></td>
<td>動手操作</td>
<td>1. 完成練習<br/>2. 解決問題<br/>3. 總結經驗</td>
</tr>
<tr>
<td><strong>評估階段</strong></td>
<td>檢驗成果</td>
<td>- 自我檢測<br/>- 同儕互評<br/>- 教師評估</td>
</tr>
</tbody>
</table>
<h3 class="theme-h3">11.2 引用內的程式碼</h3>
<blockquote class="theme-blockquote">
<p><strong>程式設計最佳實踐</strong></p>
<p>在編寫程式時，應該遵循以下原則：</p>
<pre class="theme-code-block"><code class="language-python"># 1. 使用有意義的變數名稱
student_name = "張小明"  # 好的命名
x = "張小明"            # 不好的命名

# 2. 添加適當的註釋
def calculate_average(scores):
    """計算學生成績的平均值"""
    return sum(scores) / len(scores)
</code></pre>
<blockquote class="theme-blockquote">
<p>記住：<strong>好的程式碼是給人看的，順便讓電腦執行。</strong></p>
</blockquote>
</blockquote>
<h3 class="theme-h3">11.3 列表內的表格</h3>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>
<p><strong>第一學期成績統計</strong></p>
<table class="theme-table">
<thead>
<tr>
<th>科目</th>
<th>平均分</th>
<th>最高分</th>
<th>最低分</th>
</tr>
</thead>
<tbody>
<tr>
<td>數學</td>
<td>85.2</td>
<td>98</td>
<td>72</td>
</tr>
<tr>
<td>英文</td>
<td>78.5</td>
<td>92</td>
<td>65</td>
</tr>
</tbody>
</table>
</li>
<li>
<p><strong>第二學期成績統計</strong></p>
<table class="theme-table">
<thead>
<tr>
<th>科目</th>
<th>平均分</th>
<th>最高分</th>
<th>最低分</th>
</tr>
</thead>
<tbody>
<tr>
<td>數學</td>
<td>87.1</td>
<td>99</td>
<td>75</td>
</tr>
<tr>
<td>英文</td>
<td>81.3</td>
<td>95</td>
<td>68</td>
</tr>
</tbody>
</table>
</li>
</ol>
<h3 class="theme-h3">11.4 多種格式混合使用</h3>
<blockquote class="theme-blockquote">
<p><strong>重要通知</strong>：期末考試安排</p>
<p>各位同學請注意以下考試安排：</p>
<table class="theme-table">
<thead>
<tr>
<th>日期</th>
<th>時間</th>
<th>科目</th>
<th>地點</th>
</tr>
</thead>
<tbody>
<tr>
<td>6月15日</td>
<td>09:00-11:00</td>
<td><strong>數學</strong></td>
<td>課室A</td>
</tr>
<tr>
<td>6月16日</td>
<td>09:00-11:00</td>
<td><strong>英文</strong></td>
<td>課室B</td>
</tr>
</tbody>
</table>
<p><strong>考試須知</strong>：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>請提前 <em>15分鐘</em> 到達考場</li>
<li>攜帶 <code>學生證</code> 和 <code>准考證</code></li>
<li>只能使用 <strong>指定計算器</strong></li>
</ol>
<pre class="theme-code-block"><code>重要提醒：考試期間請保持安靜
</code></pre>
<blockquote class="theme-blockquote">
<p>祝各位同學考試順利！ 🍀</p>
</blockquote>
</blockquote>
<h3 class="theme-h3">11.5 引用內的回答題</h3>
<blockquote class="theme-blockquote">
<p><strong>課堂討論題</strong></p>
<p>請根據今天學習的內容，回答以下問題：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li><strong>概念理解</strong>：什麼是演算法？請用自己的話說明。</li>
</ol>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="2">
<li><strong>實例分析</strong>：請舉出一個日常生活中的演算法例子，並說明其步驟。</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="3">
<li><strong>比較分析</strong>：比較以下兩種排序方法的優缺點：</li>
</ol>
<table class="theme-table">
<thead>
<tr>
<th>排序方法</th>
<th>優點</th>
<th>缺點</th>
</tr>
</thead>
<tbody>
<tr>
<td>氣泡排序</td>
<td></td>
<td></td>
</tr>
<tr>
<td>快速排序</td>
<td></td>
<td></td>
</tr>
</tbody>
</table>
<p>詳細說明：</p>
<hr/>
<hr/>
<hr/>
</blockquote>
<h3 class="theme-h3">11.6 表格內的回答區域</h3>
<table class="theme-table">
<thead>
<tr>
<th>題目類型</th>
<th>題目內容</th>
<th>答案區域</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>選擇題</strong></td>
<td>Python 的創始人是誰？<br/>A) Guido van Rossum<br/>B) Dennis Ritchie<br/>C) Bjarne Stroustrup</td>
<td>答案：<span class="fill-blank"> </span></td>
</tr>
<tr>
<td><strong>填空題</strong></td>
<td>Python 是一種 <span class="fill-blank"> </span> 語言</td>
<td>---</td>
</tr>
<tr>
<td><strong>簡答題</strong></td>
<td>解釋什麼是物件導向程式設計</td>
<td>---<br/>---<br/>---</td>
</tr>
<tr>
<td><strong>計算題</strong></td>
<td>計算 2^8 的值</td>
<td>計算過程：<br/>---<br/>答案：<span class="fill-blank"> </span></td>
</tr>
</tbody>
</table>
<h2 class="theme-h2">12. 邊界情況測試</h2>
<h3 class="theme-h3">12.1 特殊字符處理</h3>
<p>HTML 特殊字符：&lt; &gt; &amp; " '</p>
<p>程式碼中的特殊字符：<code>&lt; &gt; &amp; " ' \n \t \r</code></p>
<p>數學表達式：<code>x &lt; y &amp;&amp; y &gt; z || a != b</code></p>
<h3 class="theme-h3">12.2 空白字符處理</h3>
<p>這行有多個    空格    在中間。</p>
<p>這行結尾有空格。</p>
<pre class="theme-code-block"><code>這行開頭有Tab字符。
</code></pre>
<h3 class="theme-h3">12.3 長文本處理</h3>
<p>這是一個非常長的段落，用來測試系統對長文本的處理能力。在實際的教學環境中，經常會遇到包含大量文字的教材內容，系統需要能夠正確地處理這些長文本，保持良好的排版效果和閱讀體驗。這個段落包含了中文和英文的混合內容，以及各種標點符號，用來全面測試文本處理的穩定性和可靠性。</p>
<h3 class="theme-h3">12.4 格式嵌套的極限情況</h3>
<p><em><strong><code>這是嵌套的格式：粗體、斜體和程式碼</code></strong></em></p>
<blockquote class="theme-blockquote">
<p><strong>引用中的複雜格式</strong></p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>列表項目包含 <em>斜體</em> 和 <strong>粗體</strong>
<ul class="theme-list theme-list-depth-2">
<li>子項目有 <code>程式碼</code> 和 <em><strong>粗斜體</strong></em>
<ul class="theme-list theme-list-depth-3">
<li>更深層的 <em><strong>粗斜體</strong></em> 內容</li>
</ul>
</li>
</ul>
</li>
</ol>
<table class="theme-table">
<thead>
<tr>
<th>表格</th>
<th>在引用中</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>程式碼</code></td>
<td><strong>粗體</strong></td>
</tr>
<tr>
<td><em>斜體</em></td>
<td><em><strong>粗斜體</strong></em></td>
</tr>
</tbody>
</table>
</blockquote>
<h2 class="theme-h2">13. 教學場景模擬</h2>
<h3 class="theme-h3">13.1 課程大綱格式</h3>
<h1 class="theme-h1">📚 程式設計基礎課程</h1>
<p><strong>課程編號</strong>：CS101
<strong>學分</strong>：3學分
<strong>授課教師</strong>：張老師
<strong>上課時間</strong>：星期二、四 14:30-16:00</p>
<h2 class="theme-h2">📋 課程目標</h2>
<p>完成本課程後，學生將能夠：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>
<p><strong>理解程式設計的基本概念</strong></p>
<ul class="theme-list theme-list-depth-2">
<li>變數與資料型態</li>
<li>控制結構（條件、迴圈）</li>
<li>函數與模組</li>
</ul>
</li>
<li>
<p><strong>掌握 Python 程式語言</strong></p>
<ul class="theme-list theme-list-depth-2">
<li>基本語法和操作</li>
<li>常用函式庫的使用</li>
<li>除錯和測試技巧</li>
</ul>
</li>
<li>
<p><strong>培養解決問題的能力</strong></p>
<ul class="theme-list theme-list-depth-2">
<li>分析問題的方法</li>
<li>設計演算法的思維</li>
<li>程式碼優化的技巧</li>
</ul>
</li>
</ol>
<h3 class="theme-h3">13.2 學習目標列表</h3>
<h4 class="theme-h4">🎯 知識目標 (Knowledge)</h4>
<ul class="task-list task-list task-list theme-list theme-list-depth-1">
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 理解程式設計的基本原理</li>
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 掌握 Python 語法規則</li>
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 認識常見的資料結構</li>
</ul>
<h4 class="theme-h4">🛠️ 技能目標 (Skills)</h4>
<ul class="task-list task-list task-list theme-list theme-list-depth-1">
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 能夠編寫簡單的 Python 程式</li>
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 能夠除錯和修正程式錯誤</li>
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 能夠使用開發工具和環境</li>
</ul>
<h4 class="theme-h4">💭 態度目標 (Attitudes)</h4>
<ul class="task-list task-list task-list theme-list theme-list-depth-1">
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 培養邏輯思維能力</li>
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 建立持續學習的習慣</li>
<li class="task-list-item"><input class="task-list-checkbox" disabled="" type="checkbox"/> 發展團隊合作精神</li>
</ul>
<h3 class="theme-h3">13.3 練習題和答案</h3>
<p><strong>🔢 練習題 1：基礎計算</strong></p>
<p>編寫一個程式，計算圓的面積。</p>
<pre class="theme-code-block"><code class="language-python"># 學生作答區域
import math

def calculate_circle_area(radius):
    # 請在此處完成程式碼
    pass

# 測試
radius = 5
area = calculate_circle_area(radius)
print(f"半徑為 {radius} 的圓面積是：{area}")
</code></pre>
<p><strong>✅ 參考答案</strong></p>
<pre class="theme-code-block"><code class="language-python">import math

def calculate_circle_area(radius):
    """計算圓的面積"""
    return math.pi * radius ** 2

# 測試
radius = 5
area = calculate_circle_area(radius)
print(f"半徑為 {radius} 的圓面積是：{area:.2f}")
</code></pre>
<p><strong>🔤 練習題 2：字串處理</strong></p>
<p>請完成以下填空：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>在 Python 中，字串的長度可以用 <span class="fill-blank"> </span> 函數獲得。</li>
<li>將字串轉換為大寫的方法是 <span class="fill-blank"> </span>。</li>
<li>檢查字串是否以特定字符開頭的方法是 <span class="fill-blank"> </span>。</li>
</ol>
<p><strong>✅ 答案</strong></p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li><code>len()</code></li>
<li><code>.upper()</code></li>
<li><code>.startswith()</code></li>
</ol>
<h3 class="theme-h3">13.4 重點提示框</h3>
<blockquote class="theme-blockquote">
<p>💡 <strong>學習提示</strong></p>
<p>程式設計是一門實踐性很強的學科，建議：</p>
<ul class="theme-list theme-list-depth-1">
<li>每天至少練習 30 分鐘</li>
<li>多閱讀他人的程式碼</li>
<li>參與程式設計社群討論</li>
</ul>
</blockquote>
<blockquote class="theme-blockquote">
<p>⚠️ <strong>常見錯誤</strong></p>
<p>初學者容易犯的錯誤：</p>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>忘記縮排（Python 特有）</li>
<li>變數名稱拼寫錯誤</li>
<li>混淆 <code>=</code> 和 <code>==</code> 的用法</li>
</ol>
</blockquote>
<blockquote class="theme-blockquote">
<p>🎯 <strong>考試重點</strong></p>
<p>期末考試將重點考查：</p>
<ul class="theme-list theme-list-depth-1">
<li><strong>基礎語法</strong>（30%）</li>
<li><strong>程式邏輯</strong>（40%）</li>
<li><strong>實際應用</strong>（30%）</li>
</ul>
</blockquote>
<h3 class="theme-h3">13.5 課堂筆記格式</h3>
<p><strong>📅 日期</strong>：2024年12月6日
<strong>📖 主題</strong>：變數與資料型態</p>
<h4 class="theme-h4">🔑 重點概念</h4>
<p><strong>變數 (Variable)</strong></p>
<ul class="theme-list theme-list-depth-1">
<li>定義：用來儲存資料的容器</li>
<li>語法：<code>變數名 = 值</code></li>
<li>範例：<code>name = "張小明"</code></li>
</ul>
<p><strong>資料型態 (Data Types)</strong></p>
<table class="theme-table">
<thead>
<tr>
<th>型態</th>
<th>英文名稱</th>
<th>範例</th>
<th>說明</th>
</tr>
</thead>
<tbody>
<tr>
<td>整數</td>
<td>int</td>
<td><code>42</code></td>
<td>沒有小數點的數字</td>
</tr>
<tr>
<td>浮點數</td>
<td>float</td>
<td><code>3.14</code></td>
<td>有小數點的數字</td>
</tr>
<tr>
<td>字串</td>
<td>str</td>
<td><code>"Hello"</code></td>
<td>文字內容</td>
</tr>
<tr>
<td>布林值</td>
<td>bool</td>
<td><code>True</code></td>
<td>真或假</td>
</tr>
</tbody>
</table>
<h4 class="theme-h4">📝 課堂練習</h4>
<pre class="theme-code-block"><code class="language-python"># 練習：宣告不同型態的變數
student_name = "李小華"        # 字串
student_age = 16              # 整數
student_height = 165.5        # 浮點數
is_student = True             # 布林值

# 輸出變數內容
print(f"姓名：{student_name}")
print(f"年齡：{student_age}")
print(f"身高：{student_height} 公分")
print(f"是否為學生：{is_student}")
</code></pre>
<h4 class="theme-h4">💭 課後思考</h4>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li>為什麼需要不同的資料型態？</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="2">
<li>如何選擇適當的變數名稱？</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<ol class="theme-list theme-ordered-list theme-list-depth-1" start="3">
<li>變數的命名有什麼規則？</li>
</ol>
<hr/>
<hr/>
<hr/>
<hr/>
<h4 class="theme-h4">📝 課後作業</h4>
<p><strong>作業 1：基礎概念題</strong></p>
<p>請用自己的話解釋什麼是「變數」，並舉出三個生活中的例子來說明變數的概念。</p>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>作業 2：實作練習</strong></p>
<p>請寫一個簡單的 Python 程式，宣告至少5個不同型態的變數，並輸出它們的值和型態。</p>
<p>程式碼：</p>
<pre class="theme-code-block"><code class="language-python"># 請在此處寫出你的程式碼

---
---
---
---
</code></pre>
<p><strong>作業 3：錯誤分析</strong></p>
<p>以下程式碼有什麼問題？請指出錯誤並提供正確的寫法。</p>
<pre class="theme-code-block"><code class="language-python">student name = "張小明"
student-age = 16
2grade = "高一"
</code></pre>
<p>錯誤分析：</p>
<hr/>
<hr/>
<hr/>
<hr/>
<p>正確寫法：</p>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>作業 4：應用題</strong></p>
<p>假設你要設計一個學生資訊管理系統，需要儲存學生的姓名、年齡、班級、各科成績等資訊。請列出你會使用哪些變數，並說明每個變數的資料型態。</p>
<p>設計說明：</p>
<hr/>
<hr/>
<hr/>
<hr/>
<hr/>
<h2 class="theme-h2">14. 數學公式測試（LaTeX 格式）</h2>
<h3 class="theme-h3">14.1 行內數學公式測試</h3>
<p>這是一個簡單的行內公式：$E = mc^2$，以及更複雜的：$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$。</p>
<p>複雜的行內公式：$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$ 和 $\lim_{x \to 0} \frac{\sin x}{x} = 1$。</p>
<p><strong>基礎數學行內公式</strong>：</p>
<ul class="theme-list theme-list-depth-1">
<li>二次方程式：$ax^2 + bx + c = 0$</li>
<li>解的公式：$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$</li>
<li>畢氏定理：$a^2 + b^2 = c^2$</li>
<li>歐拉恆等式：$e^{i\pi} + 1 = 0$</li>
</ul>
<p><strong>物理公式行內</strong>：</p>
<ul class="theme-list theme-list-depth-1">
<li>牛頓第二定律：$F = ma$</li>
<li>動能：$E_k = \frac{1}{2}mv^2$</li>
<li>重力位能：$E_p = mgh$</li>
<li>歐姆定律：$V = IR$</li>
</ul>
<p><strong>化學公式行內</strong>：</p>
<ul class="theme-list theme-list-depth-1">
<li>水的分子式：$H_2O$</li>
<li>二氧化碳：$CO_2$</li>
<li>硫酸：$H_2SO_4$</li>
<li>甲烷：$CH_4$</li>
</ul>
<h3 class="theme-h3">14.2 行中數學公式測試（顯示模式）</h3>
<p><strong>微積分複雜公式</strong>：</p>
<p>多重積分（高斯散度定理）：
$$\iiint_V \nabla \cdot \mathbf{F} , dV = \iint_{\partial V} \mathbf{F} \cdot \mathbf{n} , dS$$</p>
<p>拉普拉斯變換：
$$\mathcal{L}{f(t)} = F(s) = \int_0^{\infty} f(t) e^{-st} dt$$</p>
<p>傅立葉變換：
$$\mathcal{F}{f(x)} = \hat{f}(\xi) = \int_{-\infty}^{\infty} f(x) e^{-2\pi i x \xi} dx$$</p>
<p><strong>線性代數複雜公式</strong>：</p>
<p>矩陣指數：
$$e^{\mathbf{A}t} = \sum_{n=0}^{\infty} \frac{(\mathbf{A}t)^n}{n!} = \mathbf{I} + \mathbf{A}t + \frac{(\mathbf{A}t)^2}{2!} + \frac{(\mathbf{A}t)^3}{3!} + \cdots$$</p>
<p>複雜矩陣運算：
$$\begin{vmatrix}
a_{11} &amp; a_{12} &amp; \cdots &amp; a_{1n} \
a_{21} &amp; a_{22} &amp; \cdots &amp; a_{2n} \
\vdots &amp; \vdots &amp; \ddots &amp; \vdots \
a_{n1} &amp; a_{n2} &amp; \cdots &amp; a_{nn}
\end{vmatrix} = \sum_{\sigma \in S_n} \text{sgn}(\sigma) \prod_{i=1}^n a_{i,\sigma(i)}$$</p>
<p><strong>統計學複雜公式</strong>：</p>
<p>多元正態分佈：
$$f(\mathbf{x}) = \frac{1}{(2\pi)^{k/2}|\boldsymbol{\Sigma}|^{1/2}} \exp\left(-\frac{1}{2}(\mathbf{x}-\boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{x}-\boldsymbol{\mu})\right)$$</p>
<p>貝葉斯定理的複雜形式：
$$P(\theta|\mathbf{x}) = \frac{P(\mathbf{x}|\theta)P(\theta)}{\int_{\Theta} P(\mathbf{x}|\theta')P(\theta') d\theta'} = \frac{\mathcal{L}(\theta|\mathbf{x})\pi(\theta)}{m(\mathbf{x})}$$</p>
<h3 class="theme-h3">14.3 量子力學公式</h3>
<p>薛丁格方程：
$$i\hbar\frac{\partial}{\partial t}\Psi(\mathbf{r},t) = \hat{H}\Psi(\mathbf{r},t) = \left[-\frac{\hbar^2}{2m}\nabla^2 + V(\mathbf{r},t)\right]\Psi(\mathbf{r},t)$$</p>
<p>量子場論：
$$\mathcal{L} = \bar{\psi}(i\gamma^\mu D_\mu - m)\psi - \frac{1}{4}F_{\mu\nu}F^{\mu\nu}$$</p>
<p>其中 $D_\mu = \partial_\mu + ieA_\mu$ 和 $F_{\mu\nu} = \partial_\mu A_\nu - \partial_\nu A_\mu$。</p>
<h3 class="theme-h3">14.4 化學公式測試</h3>
<p><strong>基本化學反應</strong>：
$$\ce{2H2 + O2 -&gt; 2H2O}$$</p>
<p>$$\ce{CaCO3 + 2HCl -&gt; CaCl2 + H2O + CO2 ^}$$</p>
<p><strong>複雜有機化學反應</strong>：
$$\ce{C6H5-CHO + HCN -&gt;[OH-] C6H5-CH(OH)-CN}$$</p>
<p><strong>生化反應</strong>：
葡萄糖代謝：
$$\ce{C6H12O6 + 6O2 -&gt; 6CO2 + 6H2O + ATP}$$</p>
<p><strong>配位化合物</strong>：
$$\ce{[Cu(NH3)4]^2+ + 4H2O &lt;=&gt; [Cu(H2O)4]^2+ + 4NH3}$$</p>
<h3 class="theme-h3">14.5 複雜分段函數</h3>
<p>$$f(x) = \begin{cases}
\frac{\sin(\pi x)}{x} &amp; \text{if } x \neq 0 \
\pi &amp; \text{if } x = 0 \
\int_0^x e^{-t^2} dt &amp; \text{if } x &gt; 1 \
\sum_{n=0}^{\infty} \frac{(-1)^n x^{2n+1}}{(2n+1)!} &amp; \text{if } |x| \leq 1
\end{cases}$$</p>
<h3 class="theme-h3">14.6 極度複雜的組合公式</h3>
<p>拉馬努金的無窮級數：
$$\frac{1}{\pi} = \frac{2\sqrt{2}}{9801} \sum_{k=0}^{\infty} \frac{(4k)!(1103+26390k)}{(k!)^4 396^{4k}}$$</p>
<p>黎曼ζ函數：
$$\zeta(s) = \sum_{n=1}^{\infty} \frac{1}{n^s} = \prod_{p \text{ prime}} \frac{1}{1-p^{-s}} = \frac{1}{\Gamma(s)} \int_0^{\infty} \frac{t^{s-1}}{e^t - 1} dt$$</p>
<h3 class="theme-h3">14.7 物理學複雜公式</h3>
<p>愛因斯坦場方程：
$$G_{\mu\nu} + \Lambda g_{\mu\nu} = \frac{8\pi G}{c^4} T_{\mu\nu}$$</p>
<p>其中 $G_{\mu\nu} = R_{\mu\nu} - \frac{1}{2}g_{\mu\nu}R$。</p>
<p>麥克斯韋方程組（張量形式）：
$$\partial_\mu F^{\mu\nu} = \mu_0 J^\nu$$
$$\partial_{[\mu} F_{\nu\rho]} = 0$$</p>
<hr/>
<h2 class="theme-h2">🎉 測試文件完成</h2>
<p>這個綜合測試文件包含了所有要求的 Markdown 元素，特別針對教育材料的需求進行了設計。</p>
<h3 class="theme-h3">✅ 測試覆蓋範圍</h3>
<ul class="theme-list theme-list-depth-1">
<li>✅ 基礎格式（標題、段落、換行）</li>
<li>✅ 文字樣式（粗體、斜體、程式碼）</li>
<li>✅ 複雜列表（5層深度嵌套、混合內容）</li>
<li>✅ 圖片測試（22張圖片、多種顯示方式）</li>
<li>✅ 表格（基本、對齊、格式化、內嵌回答區）</li>
<li>✅ 程式碼（行內、區塊、語言指定）</li>
<li>✅ 引用（單層、多層、混合格式、內嵌回答題）</li>
<li>✅ 線條（填空線、水平分隔線、回答題橫線）</li>
<li>✅ 教育元素（重點標記、練習題、回答題）</li>
<li>✅ 中文內容（繁體、混排、教育術語）</li>
<li>✅ 複雜組合（表格+列表、引用+程式碼+回答題）</li>
<li>✅ 邊界情況（特殊字符、長文本）</li>
<li>✅ 教學場景（課程大綱、筆記格式、作業設計）</li>
<li>✅ LaTeX 公式（行內+行中、數學、化學、物理）</li>
<li>✅ 回答題格式（3-4條橫線、多種題型）</li>
</ul>
<h3 class="theme-h3">🎯 使用建議</h3>
<ol class="theme-list theme-ordered-list theme-list-depth-1">
<li><strong>上傳測試</strong>：將此文件上傳到系統進行轉換</li>
<li><strong>逐項檢查</strong>：按照各個章節檢查轉換效果</li>
<li><strong>重點關注</strong>：中文顯示、表格格式、程式碼高亮</li>
<li><strong>教育功能</strong>：填空線、重點標記的顯示效果</li>
</ol>
<p>這個測試文件將幫助您全面驗證週 1 的 Markdown → HTML 轉換功能！</p>

    </div>
    <!-- Logo 將在 PDF 後處理階段添加 -->
</body>
</html>