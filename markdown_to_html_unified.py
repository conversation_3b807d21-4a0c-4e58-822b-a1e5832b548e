#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育材料轉換系統 - 統一 Markdown 到 HTML 轉換器

版本: 3.0.0 (主程式版本)
作者: 教育材料轉換系統開發團隊
日期: 2024-12-02
許可證: 遵循原項目許可證

這是教育材料轉換系統的核心轉換引擎，提供完整的 Markdown 到 HTML 轉換功能：

核心功能：
- 標準 Markdown 解析 (CommonMark 兼容)
- LaTeX 數學公式處理 (行內和顯示公式)
- 智能圖片處理 (尺寸控制、佈局管理)
- 教育特殊元素 (填空線、練習區塊、水平線統一)
- 多主題系統 (5種內建主題)
- MathJax 整合 (完整數學公式渲染)

設計原則：
- 單一檔案包含所有功能，便於部署和維護
- 簡化的 API 接口，易於使用和整合
- 最小化外部依賴，提高系統穩定性
- 高性能和可靠性，適合生產環境使用
- 完整的錯誤處理和日誌記錄

使用方式：
1. 命令行: python markdown_to_html_unified.py input.md [output.html]
2. Python API: convert_markdown_string() 或 convert_markdown_file()
3. 類實例: UnifiedMarkdownConverter()

依賴要求：
- Python 3.8+
- markdown-it-py >= 3.0.0
- beautifulsoup4 >= 4.12.0
"""

import re
import logging
import sys
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
import urllib.parse
from dataclasses import dataclass, field

# 核心依賴
try:
    from markdown_it import MarkdownIt
    from bs4 import BeautifulSoup
except ImportError as e:
    print(f"錯誤: 缺少必要的依賴庫 - {e}")
    print("請運行: pip install markdown-it-py beautifulsoup4")
    sys.exit(1)

# 配置日誌系統
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


@dataclass
class ConversionConfig:
    """
    轉換配置類

    控制 Markdown 到 HTML 轉換過程中的各種選項和行為。
    所有選項都有合理的默認值，可以根據需要進行調整。
    """
    # 基礎功能開關
    enable_math: bool = True                    # 啟用 LaTeX 數學公式處理
    enable_images: bool = True                  # 啟用圖片處理和尺寸控制
    enable_fill_blanks: bool = True             # 啟用填空線處理
    enable_themes: bool = True                  # 啟用主題系統

    # 主題設置
    theme_name: str = "default"                 # 主題名稱: default, nature, tech, space, sport
    pdf_mode: bool = False                      # PDF 優化模式（調整樣式以適合列印）

    # 圖片設置
    image_base_path: str = ""                   # 圖片基礎路徑（用於相對路徑處理）
    default_image_width: Optional[int] = None   # 默認圖片寬度（像素）

    # 輸出設置
    include_mathjax: bool = True                # 包含 MathJax 腳本和配置
    include_css: bool = True                    # 包含內建 CSS 樣式

    # 高級設置
    strict_mode: bool = False                   # 嚴格模式（更嚴格的錯誤處理）
    custom_css: str = ""                        # 自定義 CSS 樣式
    mathjax_config: Optional[Dict] = None       # 自定義 MathJax 配置


@dataclass
class ConversionResult:
    """
    轉換結果類

    包含轉換過程的所有結果信息，包括生成的 HTML 內容、
    統計信息、警告和錯誤信息。
    """
    html_content: str                           # 生成的完整 HTML 內容
    title: str                                  # 文檔標題
    statistics: Dict[str, int] = field(default_factory=dict)  # 統計信息
    warnings: List[str] = field(default_factory=list)         # 警告信息
    errors: List[str] = field(default_factory=list)           # 錯誤信息
    processing_time: float = 0.0               # 處理時間（秒）

    @property
    def success(self) -> bool:
        """檢查轉換是否成功（無錯誤）"""
        return len(self.errors) == 0

    @property
    def has_warnings(self) -> bool:
        """檢查是否有警告"""
        return len(self.warnings) > 0

    def get_summary(self) -> str:
        """獲取轉換結果摘要"""
        status = "成功" if self.success else "失敗"
        return f"轉換{status} | 標題: {self.title} | 統計: {self.statistics}"


class UnifiedMarkdownConverter:
    """
    統一 Markdown 轉換器 - 教育材料轉換系統核心引擎

    這個類是教育材料轉換系統的核心組件，提供完整的 Markdown 到 HTML 轉換功能。
    它整合了所有必要的功能模組，包括數學公式處理、圖片管理、主題系統和教育特殊元素處理。

    主要特性：
    - 高性能的 Markdown 解析（基於 markdown-it-py）
    - 完整的 LaTeX 數學公式支援（MathJax 整合）
    - 智能圖片處理（尺寸控制、佈局管理）
    - 多主題系統（5種內建主題）
    - 教育特殊元素（填空線、練習區塊等）
    - 完整的錯誤處理和日誌記錄

    使用範例：
        # 基本使用
        converter = UnifiedMarkdownConverter()
        result = converter.convert(markdown_content)

        # 自定義配置
        config = ConversionConfig(theme_name="nature", pdf_mode=True)
        converter = UnifiedMarkdownConverter(config)
        result = converter.convert(markdown_content, "output.html")
    """

    # 版本信息
    VERSION = "3.0.0"

    def __init__(self, config: Optional[ConversionConfig] = None):
        """
        初始化統一 Markdown 轉換器

        Args:
            config: 轉換配置對象，如果為 None 則使用默認配置

        Raises:
            ImportError: 當缺少必要依賴時
            ValueError: 當配置參數無效時
        """
        # 驗證和設置配置
        self.config = self._validate_config(config or ConversionConfig())

        # 初始化核心組件
        self.md = self._setup_markdown_parser()
        self.themes = self._load_themes()

        # 初始化狀態變量
        self.statistics = {}
        self.warnings = []
        self.errors = []

        logger.info(f"UnifiedMarkdownConverter v{self.VERSION} 初始化完成")
        logger.debug(f"配置: 主題={self.config.theme_name}, 數學公式={self.config.enable_math}, "
                    f"圖片處理={self.config.enable_images}")

    def _validate_config(self, config: ConversionConfig) -> ConversionConfig:
        """
        驗證配置參數的有效性

        Args:
            config: 要驗證的配置對象

        Returns:
            ConversionConfig: 驗證後的配置對象

        Raises:
            ValueError: 當配置參數無效時
        """
        # 驗證主題名稱
        valid_themes = ["default", "nature", "tech", "space", "sport"]
        if config.theme_name not in valid_themes:
            raise ValueError(f"無效的主題名稱: {config.theme_name}. 有效選項: {valid_themes}")

        # 驗證圖片基礎路徑
        if config.image_base_path and not isinstance(config.image_base_path, str):
            raise ValueError("image_base_path 必須是字符串類型")

        # 驗證默認圖片寬度
        if config.default_image_width is not None and config.default_image_width <= 0:
            raise ValueError("default_image_width 必須是正整數")

        return config
    
    def _setup_markdown_parser(self) -> MarkdownIt:
        """
        設置和配置 Markdown 解析器

        使用 markdown-it-py 作為核心解析引擎，配置為 CommonMark 兼容模式，
        並啟用必要的插件以支援表格、刪除線等擴展語法。

        Returns:
            MarkdownIt: 配置好的 Markdown 解析器實例
        """
        # 創建 CommonMark 兼容的解析器
        md = MarkdownIt("commonmark", {
            "html": True,           # 允許 HTML 標籤
            "linkify": True,        # 自動轉換 URL 為連結
            "typographer": True,    # 啟用智能引號和其他排版功能
        })

        # 啟用額外插件
        available_plugins = ["table", "strikethrough"]
        enabled_plugins = []

        for plugin in available_plugins:
            try:
                md.enable([plugin])
                enabled_plugins.append(plugin)
            except ValueError as e:
                logger.warning(f"無法啟用 Markdown 插件 '{plugin}': {e}")



        logger.debug(f"Markdown 解析器已配置，啟用插件: {enabled_plugins}")
        return md

    def _load_themes(self) -> Dict[str, Dict]:
        """
        載入增強版內建主題配置

        定義了5種內建主題，每個主題包含完整的視覺設計系統：
        - 顏色方案（主色、次色、強調色、背景色）
        - SVG 圖標系統（多層級列表圖標）
        - 視覺效果（漸變、陰影、邊框）
        - 教育元素樣式（填空線、數學公式區域）

        Returns:
            Dict[str, Dict]: 主題配置字典，鍵為主題名稱，值為主題配置
        """
        themes = {
            "default": {
                "name": "經典主題",
                "description": "專業的藍色主題，適合正式文檔和商務材料",
                "category": "professional",

                # 顏色系統
                "primary_color": "#2563eb",      # 主要藍色
                "secondary_color": "#64748b",    # 次要灰色
                "accent_color": "#f59e0b",       # 強調橙色
                "background_color": "#ffffff",   # 背景白色
                "text_color": "#1f2937",         # 文字深灰
                "border_color": "#e5e7eb",       # 邊框淺灰

                # 漸變和效果
                "gradient_primary": "linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)",
                "gradient_accent": "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
                "shadow_primary": "0 4px 12px rgba(37, 99, 235, 0.15)",
                "shadow_accent": "0 2px 8px rgba(245, 158, 11, 0.2)",

                # 列表圖標系統
                "list_icons": {
                    "primary": "🔵",     # 主列表：藍色圓形
                    "secondary": "🔹",   # 子列表：藍色菱形
                    "tertiary": "▪️",    # 三級列表：小方塊
                    "quaternary": "•",   # 四級列表：圓點
                    "quinary": "‣"       # 五級列表：箭頭
                },

                # 教育元素樣式
                "fill_blank_style": "solid",     # 填空線樣式：實線
                "math_bg_color": "#f8fafc",      # 數學公式背景
                "code_bg_color": "#f1f5f9",      # 代碼背景
                "quote_bg_color": "#eff6ff"      # 引用背景
            },

            "nature": {
                "name": "自然主題",
                "description": "清新的綠色主題，適合環境科學和生物學內容",
                "category": "organic",

                # 顏色系統
                "primary_color": "#059669",      # 主要綠色
                "secondary_color": "#6b7280",    # 次要灰色
                "accent_color": "#d97706",       # 強調橙色
                "background_color": "#fefffe",   # 背景微綠白
                "text_color": "#1f2937",         # 文字深灰
                "border_color": "#d1fae5",       # 邊框淺綠

                # 漸變和效果
                "gradient_primary": "linear-gradient(135deg, #059669 0%, #047857 100%)",
                "gradient_accent": "linear-gradient(135deg, #d97706 0%, #b45309 100%)",
                "shadow_primary": "0 4px 12px rgba(5, 150, 105, 0.15)",
                "shadow_accent": "0 2px 8px rgba(217, 119, 6, 0.2)",

                # 列表圖標系統
                "list_icons": {
                    "primary": "🌿",     # 主列表：葉子
                    "secondary": "🌱",   # 子列表：幼苗
                    "tertiary": "🍃",    # 三級列表：飄葉
                    "quaternary": "🌾",  # 四級列表：麥穗
                    "quinary": "🌸"      # 五級列表：花朵
                },

                # 教育元素樣式
                "fill_blank_style": "dotted",    # 填空線樣式：點線
                "math_bg_color": "#f0fdf4",      # 數學公式背景：淺綠
                "code_bg_color": "#ecfdf5",      # 代碼背景：極淺綠
                "quote_bg_color": "#f0fdf4"      # 引用背景：淺綠
            },

            "tech": {
                "name": "科技主題",
                "description": "現代的紫色主題，適合程式設計和工程學內容",
                "category": "futuristic",

                # 顏色系統
                "primary_color": "#7c3aed",      # 主要紫色
                "secondary_color": "#6b7280",    # 次要灰色
                "accent_color": "#06b6d4",       # 強調青色
                "background_color": "#fefffe",   # 背景白色
                "text_color": "#1f2937",         # 文字深灰
                "border_color": "#e0e7ff",       # 邊框淺紫

                # 漸變和效果
                "gradient_primary": "linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%)",
                "gradient_accent": "linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)",
                "shadow_primary": "0 4px 12px rgba(124, 58, 237, 0.15)",
                "shadow_accent": "0 2px 8px rgba(6, 182, 212, 0.2)",

                # 列表圖標系統
                "list_icons": {
                    "primary": "⚡",     # 主列表：閃電
                    "secondary": "🔧",   # 子列表：工具
                    "tertiary": "⚙️",    # 三級列表：齒輪
                    "quaternary": "🔹",  # 四級列表：菱形
                    "quinary": "▫️"      # 五級列表：空心方塊
                },

                # 教育元素樣式
                "fill_blank_style": "dashed",    # 填空線樣式：虛線
                "math_bg_color": "#faf5ff",      # 數學公式背景：淺紫
                "code_bg_color": "#f3f4f6",      # 代碼背景：淺灰
                "quote_bg_color": "#ede9fe"      # 引用背景：極淺紫
            },

            "space": {
                "name": "太空主題",
                "description": "深邃的藍色主題，適合天文學和物理學內容",
                "category": "cosmic",

                # 顏色系統（深色主題）
                "primary_color": "#3b82f6",      # 主要亮藍色
                "secondary_color": "#9ca3af",    # 次要淺灰
                "accent_color": "#a855f7",       # 強調紫色
                "background_color": "#0f172a",   # 背景深藍黑
                "text_color": "#f1f5f9",         # 文字淺色
                "border_color": "#1e293b",       # 邊框深灰藍

                # 漸變和效果
                "gradient_primary": "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)",
                "gradient_accent": "linear-gradient(135deg, #a855f7 0%, #9333ea 100%)",
                "shadow_primary": "0 4px 12px rgba(59, 130, 246, 0.3)",
                "shadow_accent": "0 2px 8px rgba(168, 85, 247, 0.25)",

                # 列表圖標系統
                "list_icons": {
                    "primary": "🚀",     # 主列表：火箭
                    "secondary": "⭐",   # 子列表：星星
                    "tertiary": "🌟",    # 三級列表：閃亮星
                    "quaternary": "✨",  # 四級列表：星花
                    "quinary": "🔸"      # 五級列表：橙色菱形
                },

                # 教育元素樣式
                "fill_blank_style": "solid",     # 填空線樣式：實線
                "math_bg_color": "#1e293b",      # 數學公式背景：深藍灰
                "code_bg_color": "#334155",      # 代碼背景：中藍灰
                "quote_bg_color": "#1e293b"      # 引用背景：深藍灰
            },

            "sport": {
                "name": "運動主題",
                "description": "活力的紅色主題，適合體育和健康教育內容",
                "category": "energetic",

                # 顏色系統
                "primary_color": "#dc2626",      # 主要紅色
                "secondary_color": "#6b7280",    # 次要灰色
                "accent_color": "#ea580c",       # 強調橙紅色
                "background_color": "#ffffff",   # 背景白色
                "text_color": "#1f2937",         # 文字深灰
                "border_color": "#fecaca",       # 邊框淺紅

                # 漸變和效果
                "gradient_primary": "linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)",
                "gradient_accent": "linear-gradient(135deg, #ea580c 0%, #c2410c 100%)",
                "shadow_primary": "0 4px 12px rgba(220, 38, 38, 0.15)",
                "shadow_accent": "0 2px 8px rgba(234, 88, 12, 0.2)",

                # 列表圖標系統
                "list_icons": {
                    "primary": "⚽",     # 主列表：足球
                    "secondary": "🏃",   # 子列表：跑步
                    "tertiary": "🏆",    # 三級列表：獎杯
                    "quaternary": "🥇",  # 四級列表：金牌
                    "quinary": "🔥"      # 五級列表：火焰
                },

                # 教育元素樣式
                "fill_blank_style": "double",    # 填空線樣式：雙線
                "math_bg_color": "#fef2f2",      # 數學公式背景：淺紅
                "code_bg_color": "#fef2f2",      # 代碼背景：淺紅
                "quote_bg_color": "#fef2f2"      # 引用背景：淺紅
            }
        }

        logger.debug(f"已載入 {len(themes)} 個增強主題: {list(themes.keys())}")
        return themes
    
    def convert(self, markdown_content: str, output_file: Optional[str] = None) -> ConversionResult:
        """
        轉換 Markdown 到 HTML 的主要方法

        這是轉換器的核心方法，執行完整的轉換流程：
        1. 預處理：清理和標準化 Markdown 內容
        2. 解析：將 Markdown 轉換為 HTML
        3. 後處理：應用主題、處理特殊元素
        4. 生成：創建完整的 HTML 文檔

        Args:
            markdown_content: 要轉換的 Markdown 文本內容
            output_file: 可選的輸出檔案路徑，如果提供則自動保存結果

        Returns:
            ConversionResult: 包含轉換結果和相關信息的對象

        Raises:
            ValueError: 當輸入內容無效時
            IOError: 當檔案操作失敗時
        """
        import time
        start_time = time.time()

        try:
            # 輸入驗證
            if not isinstance(markdown_content, str):
                raise ValueError("markdown_content 必須是字符串類型")

            if not markdown_content.strip():
                logger.warning("輸入的 Markdown 內容為空")

            logger.info("開始 Markdown 到 HTML 轉換")
            logger.debug(f"輸入內容長度: {len(markdown_content)} 字符")

            # 重置轉換狀態
            self._reset_state()

            # 階段 1: 預處理 - 清理和標準化內容
            logger.debug("階段 1: 預處理")
            processed_content = self._preprocess(markdown_content)

            # 階段 2: Markdown 解析 - 轉換為 HTML
            logger.debug("階段 2: Markdown 解析")
            html_content = self._parse_and_convert(processed_content)

            # 階段 3: 後處理 - 應用主題和特殊元素處理
            logger.debug("階段 3: 後處理")
            final_html = self._postprocess(html_content)

            # 階段 4: 生成完整 HTML 文檔
            logger.debug("階段 4: 生成完整 HTML")
            complete_html = self._generate_complete_html(final_html, processed_content)

            # 保存檔案（如果指定）
            if output_file:
                self._save_html(complete_html, output_file)

            # 計算處理時間
            processing_time = time.time() - start_time

            # 生成轉換結果
            result = ConversionResult(
                html_content=complete_html,
                title=self._extract_title(processed_content),
                statistics=self.statistics.copy(),
                warnings=self.warnings.copy(),
                errors=self.errors.copy(),
                processing_time=processing_time
            )

            logger.info(f"轉換完成 - {result.get_summary()}")
            logger.debug(f"處理時間: {processing_time:.3f} 秒")

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"轉換過程中發生錯誤: {str(e)}"
            logger.error(error_msg, exc_info=True if self.config.strict_mode else False)
            self.errors.append(error_msg)

            return ConversionResult(
                html_content="",
                title="轉換失敗",
                statistics=self.statistics.copy(),
                warnings=self.warnings.copy(),
                errors=self.errors.copy(),
                processing_time=processing_time
            )
    
    def _reset_state(self):
        """
        重置轉換器的內部狀態

        在每次轉換開始前調用，確保統計信息、警告和錯誤列表都是乾淨的。
        這對於重複使用同一個轉換器實例進行多次轉換非常重要。
        """
        self.statistics = {
            'total_lines': 0,           # 總行數
            'math_formulas': 0,         # 數學公式數量
            'images': 0,                # 圖片數量
            'fill_blanks': 0,           # 填空線數量
            'tables': 0,                # 表格數量
            'links': 0,                 # 連結數量
            'headers': 0,               # 標題數量
            'lists': 0,                 # 列表數量
            'code_blocks': 0            # 代碼塊數量
        }
        self.warnings.clear()
        self.errors.clear()
        logger.debug("轉換器狀態已重置")
    
    def _preprocess(self, content: str) -> str:
        """
        預處理 Markdown 內容

        執行各種預處理操作以準備內容進行解析：
        - 標準化換行符和空白字符
        - 統計基本信息
        - 預處理數學公式、圖片、填空線等特殊元素
        - 標準化水平線格式

        Args:
            content: 原始 Markdown 內容

        Returns:
            str: 預處理後的 Markdown 內容
        """
        logger.debug("開始預處理階段")

        # 基本統計和清理
        lines = content.splitlines()
        self.statistics['total_lines'] = len(lines)

        # 標準化換行符（統一使用 \n）
        content = content.replace('\r\n', '\n').replace('\r', '\n')

        # 移除行尾空白字符，但保留空行
        cleaned_lines = []
        for line in lines:
            cleaned_lines.append(line.rstrip())
        content = '\n'.join(cleaned_lines)

        # 條件性預處理（根據配置啟用）
        if self.config.enable_math:
            content = self._preprocess_math(content)
            logger.debug("數學公式預處理完成")

        if self.config.enable_images:
            content = self._preprocess_images(content)
            logger.debug("圖片預處理完成")

        if self.config.enable_fill_blanks:
            content = self._preprocess_fill_blanks(content)
            logger.debug("填空線預處理完成")

        # 標準化水平線（總是執行）
        content = self._normalize_horizontal_lines(content)

        logger.debug(f"預處理完成 - 處理了 {self.statistics['total_lines']} 行內容")
        return content
    
    def _preprocess_math(self, content: str) -> str:
        """
        預處理 LaTeX 數學公式

        識別和統計文檔中的數學公式，包括行內公式和顯示公式。
        為數學公式提供保護，防止被 Markdown 解析器錯誤處理。

        支援的數學公式格式：
        - 行內公式: $formula$
        - 顯示公式: $$formula$$
        - 化學公式: \\ce{formula}

        Args:
            content: 包含數學公式的 Markdown 內容

        Returns:
            str: 預處理後的內容
        """
        # 統計行內數學公式（單行，不包含換行）
        inline_math_pattern = r'\$[^$\n]+\$'
        inline_math = re.findall(inline_math_pattern, content)

        # 統計顯示數學公式（可跨行）
        display_math_pattern = r'\$\$[^$]+?\$\$'
        display_math = re.findall(display_math_pattern, content, re.DOTALL)

        # 統計化學公式
        chem_pattern = r'\\ce\{[^}]+\}'
        chem_formulas = re.findall(chem_pattern, content)

        total_formulas = len(inline_math) + len(display_math) + len(chem_formulas)
        self.statistics['math_formulas'] = total_formulas

        if total_formulas > 0:
            logger.debug(f"發現數學公式: {len(inline_math)} 個行內, {len(display_math)} 個顯示, "
                        f"{len(chem_formulas)} 個化學公式")

        # 數學公式保護邏輯（如果需要的話）
        # 目前 markdown-it-py 對數學公式處理良好，暫不需要特殊保護

        return content
    
    def _preprocess_images(self, content: str) -> str:
        """
        預處理圖片引用

        處理 Markdown 圖片語法，支援自定義參數來控制圖片的尺寸和佈局。

        支援的圖片語法：
        - 基本語法: ![alt](path)
        - 帶參數: ![alt](path|width=400|center)

        支援的參數：
        - width=N: 設置寬度（像素）
        - height=N: 設置高度（像素）
        - size=N%: 設置相對尺寸（百分比）
        - left: 左浮動
        - right: 右浮動
        - center: 居中顯示

        Args:
            content: 包含圖片引用的 Markdown 內容

        Returns:
            str: 預處理後的內容
        """
        # 圖片語法模式: ![alt](path|param1|param2|...)
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'

        def process_image(match):
            alt_text = match.group(1)
            path_and_params = match.group(2)

            # 分離圖片路徑和參數
            if '|' in path_and_params:
                parts = path_and_params.split('|')
                image_path = parts[0].strip()
                params = [p.strip() for p in parts[1:] if p.strip()]
            else:
                image_path = path_and_params.strip()
                params = []

            # 處理相對路徑
            processed_path = self._process_image_path(image_path)

            # 驗證參數格式
            valid_params = []
            for param in params:
                if self._validate_image_param(param):
                    valid_params.append(param)
                else:
                    self.warnings.append(f"無效的圖片參數: {param}")

            # 重新組合
            if valid_params:
                final_path = f"{processed_path}|{'|'.join(valid_params)}"
            else:
                final_path = processed_path

            return f'![{alt_text}]({final_path})'

        # 處理所有圖片
        processed_content = re.sub(image_pattern, process_image, content)

        # 統計圖片數量
        image_count = len(re.findall(image_pattern, processed_content))
        self.statistics['images'] = image_count

        if image_count > 0:
            logger.debug(f"發現 {image_count} 張圖片")

        return processed_content

    def _process_image_path(self, image_path: str) -> str:
        """
        處理圖片路徑

        Args:
            image_path: 原始圖片路徑

        Returns:
            str: 處理後的圖片路徑
        """
        # 如果是絕對 URL，直接返回
        if image_path.startswith(('http://', 'https://', '//')):
            return image_path

        # 如果是絕對路徑，直接返回
        if image_path.startswith('/'):
            return image_path

        # 處理相對路徑
        if self.config.image_base_path:
            # 避免重複添加基礎路徑
            if not image_path.startswith(self.config.image_base_path):
                return f"{self.config.image_base_path.rstrip('/')}/{image_path}"

        return image_path

    def _validate_image_param(self, param: str) -> bool:
        """
        驗證圖片參數的有效性

        Args:
            param: 要驗證的參數

        Returns:
            bool: 參數是否有效
        """
        # 尺寸參數
        if param.startswith(('width=', 'height=')):
            try:
                value = param.split('=')[1]
                return value.isdigit() and int(value) > 0
            except (IndexError, ValueError):
                return False

        # 百分比尺寸
        if param.startswith('size='):
            try:
                value = param.split('=')[1]
                if value.endswith('%'):
                    num = value[:-1]
                    return num.replace('.', '').isdigit() and float(num) > 0
                return value.isdigit() and int(value) > 0
            except (IndexError, ValueError):
                return False

        # 佈局參數
        if param in ['left', 'right', 'center']:
            return True

        return False

    def _preprocess_fill_blanks(self, content: str) -> str:
        """
        預處理填空線

        識別和統計文檔中的填空線模式。填空線是教育材料中常用的元素，
        用於創建學生可以填寫答案的空白區域。

        填空線模式：
        - 4個或以上連續的下劃線: ____
        - 將在後處理階段轉換為可視化的填空框

        Args:
            content: 包含填空線的 Markdown 內容

        Returns:
            str: 預處理後的內容（此階段不修改內容，僅統計）
        """
        # 識別填空線模式（4個或以上連續下劃線）
        fill_blank_pattern = r'_{4,}'
        matches = re.findall(fill_blank_pattern, content)

        self.statistics['fill_blanks'] = len(matches)

        if len(matches) > 0:
            logger.debug(f"發現 {len(matches)} 個填空線")

        # 在預處理階段不修改內容，填空線轉換在後處理階段進行
        return content

    def _normalize_horizontal_lines(self, content: str) -> str:
        """統一化水平線"""
        lines = content.splitlines()
        processed_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 檢查是否為水平線
            if line in ['---', '***', '___']:
                # 統計連續的水平線數量
                consecutive_count = 1
                j = i + 1

                while j < len(lines) and lines[j].strip() in ['---', '***', '___']:
                    consecutive_count += 1
                    j += 1

                # 只有當連續水平線數量大於1時才統一為4條
                if consecutive_count > 1:
                    for _ in range(4):
                        processed_lines.append('---')
                    i = j
                    continue
                else:
                    processed_lines.append(lines[i])
                    i += 1
                    continue

            processed_lines.append(lines[i])
            i += 1

        return '\n'.join(processed_lines)

    def _parse_and_convert(self, content: str) -> str:
        """解析並轉換 Markdown 到 HTML"""
        logger.debug("開始 Markdown 解析")

        try:
            # 使用 markdown-it-py 解析
            html_content = self.md.render(content)
            logger.debug("Markdown 解析完成")
            return html_content
        except Exception as e:
            error_msg = f"Markdown 解析錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return ""

    def _postprocess(self, html_content: str) -> str:
        """後處理 HTML 內容"""
        logger.debug("開始後處理")

        try:
            soup = BeautifulSoup(html_content, 'html.parser')

            # 處理圖片屬性
            soup = self._process_image_attributes(soup)

            # 處理檢查清單
            soup = self._process_task_lists(soup)

            # 處理填空線
            if self.config.enable_fill_blanks:
                soup = self._process_fill_blanks(soup)

            # 應用主題
            if self.config.enable_themes:
                soup = self._apply_theme(soup)

            # 統計元素
            self._update_statistics(soup)

            logger.debug("後處理完成")
            return str(soup)

        except Exception as e:
            error_msg = f"後處理錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return html_content

    def _process_task_lists(self, soup: BeautifulSoup) -> BeautifulSoup:
        """
        處理檢查清單（Task Lists）

        將 Markdown 檢查清單語法 `- [ ]` 和 `- [x]` 轉換為正確的 HTML 複選框
        """
        try:
            # 查找所有列表項目
            list_items = soup.find_all('li')

            for li in list_items:
                # 獲取列表項目的文本內容
                text = li.get_text().strip()

                # 檢查是否為檢查清單格式
                if text.startswith('[ ]') or text.startswith('[x]') or text.startswith('[X]'):
                    # 確定是否已勾選
                    is_checked = text.startswith('[x]') or text.startswith('[X]')

                    # 移除原始的 [ ] 或 [x] 標記
                    if text.startswith('[ ]'):
                        clean_text = text[3:].strip()
                    else:
                        clean_text = text[3:].strip()

                    # 創建複選框 HTML
                    checkbox_html = f'<input type="checkbox" class="task-list-checkbox" disabled{"" if not is_checked else " checked"}> {clean_text}'

                    # 替換列表項目內容
                    li.clear()
                    li.append(BeautifulSoup(checkbox_html, 'html.parser'))

                    # 添加 CSS 類別
                    li['class'] = li.get('class', []) + ['task-list-item']

                    # 確保父列表也有正確的類別
                    parent_ul = li.find_parent(['ul', 'ol'])
                    if parent_ul:
                        parent_ul['class'] = parent_ul.get('class', []) + ['task-list']

            logger.debug("檢查清單處理完成")
            return soup

        except Exception as e:
            logger.warning(f"檢查清單處理失敗: {e}")
            return soup

    def _process_image_attributes(self, soup: BeautifulSoup) -> BeautifulSoup:
        """處理圖片尺寸和佈局屬性"""
        images = soup.find_all('img')

        for img in images:
            src = img.get('src', '')

            # 檢查是否包含 URL 編碼的參數 (%7C 是 | 的編碼)
            if '%7C' in src:
                # URL 解碼
                decoded_src = urllib.parse.unquote(src)
                src = decoded_src

            # 檢查是否包含參數
            if '|' in src:
                parts = src.split('|')
                actual_src = parts[0]
                params = parts[1:]

                # 更新圖片 src
                img['src'] = actual_src

                # 處理參數
                style_parts = []

                for param in params:
                    param = param.strip()

                    if param.startswith('width='):
                        width = param.split('=')[1]
                        img['width'] = width
                    elif param.startswith('height='):
                        height = param.split('=')[1]
                        img['height'] = height
                    elif param.startswith('size='):
                        size = param.split('=')[1]
                        style_parts.append(f'width: {size}')
                    elif param == 'left':
                        style_parts.append('float: left')
                        style_parts.append('margin: 0 15px 15px 0')
                    elif param == 'right':
                        style_parts.append('float: right')
                        style_parts.append('margin: 0 0 15px 15px')
                    elif param == 'center':
                        style_parts.append('display: block')
                        style_parts.append('margin: 0 auto')

                # 應用樣式
                if style_parts:
                    existing_style = img.get('style', '')
                    new_style = '; '.join(style_parts)
                    if existing_style:
                        img['style'] = f"{existing_style}; {new_style}"
                    else:
                        img['style'] = new_style

        return soup

    def _process_fill_blanks(self, soup: BeautifulSoup) -> BeautifulSoup:
        """
        處理填空線

        將 Markdown 中的連續下劃線（____）轉換為主題化的填空線元素。
        使用純 CSS 樣式而不是下劃線字符，避免雙重線條問題。
        """
        # 查找所有文本節點中的填空線模式
        for element in soup.find_all(string=True):
            if '____' in element:
                # 替換填空線為 HTML 元素（不包含下劃線字符）
                new_text = re.sub(
                    r'_{4,}',
                    '<span class="fill-blank">&nbsp;</span>',
                    element
                )
                if new_text != element:
                    element.replace_with(BeautifulSoup(new_text, 'html.parser'))

        return soup

    def _apply_theme(self, soup: BeautifulSoup) -> BeautifulSoup:
        """應用增強主題樣式"""
        theme = self.themes.get(self.config.theme_name, self.themes["default"])

        # 為標題元素添加主題類
        for h1 in soup.find_all('h1'):
            h1['class'] = h1.get('class', []) + ['theme-h1']

        for h2 in soup.find_all('h2'):
            h2['class'] = h2.get('class', []) + ['theme-h2']

        for h3 in soup.find_all('h3'):
            h3['class'] = h3.get('class', []) + ['theme-h3']

        for h4 in soup.find_all('h4'):
            h4['class'] = h4.get('class', []) + ['theme-h4']

        for h5 in soup.find_all('h5'):
            h5['class'] = h5.get('class', []) + ['theme-h5']

        for h6 in soup.find_all('h6'):
            h6['class'] = h6.get('class', []) + ['theme-h6']

        # 處理多層級列表樣式
        self._apply_nested_list_styles(soup)

        # 處理表格增強
        for table in soup.find_all('table'):
            table['class'] = table.get('class', []) + ['theme-table']

        # 處理引用增強
        for blockquote in soup.find_all('blockquote'):
            blockquote['class'] = blockquote.get('class', []) + ['theme-blockquote']

        # 處理代碼塊增強
        for pre in soup.find_all('pre'):
            pre['class'] = pre.get('class', []) + ['theme-code-block']

        return soup

    def _apply_nested_list_styles(self, soup: BeautifulSoup):
        """應用嵌套列表樣式"""
        # 處理所有無序列表
        for ul in soup.find_all('ul'):
            ul['class'] = ul.get('class', []) + ['theme-list']

            # 計算嵌套深度並添加相應的類
            depth = self._calculate_list_depth(ul)
            ul['class'].append(f'theme-list-depth-{depth}')

        # 處理所有有序列表
        for ol in soup.find_all('ol'):
            ol['class'] = ol.get('class', []) + ['theme-list', 'theme-ordered-list']

            # 計算嵌套深度
            depth = self._calculate_list_depth(ol)
            ol['class'].append(f'theme-list-depth-{depth}')

    def _calculate_list_depth(self, list_element) -> int:
        """計算列表的嵌套深度"""
        depth = 1
        parent = list_element.parent

        while parent:
            if parent.name in ['ul', 'ol']:
                depth += 1
            parent = parent.parent

        return min(depth, 5)  # 最大支援5層深度

    def _update_statistics(self, soup: BeautifulSoup):
        """更新統計信息"""
        self.statistics['tables'] = len(soup.find_all('table'))
        self.statistics['links'] = len(soup.find_all('a'))

    def _extract_title(self, content: str) -> str:
        """提取文檔標題"""
        lines = content.splitlines()
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
        return "未命名文檔"

    def _generate_complete_html(self, body_html: str, original_content: str) -> str:
        """生成完整的 HTML 文檔"""
        title = self._extract_title(original_content)
        theme = self.themes.get(self.config.theme_name, self.themes["default"])

        # 生成 CSS
        css = self._generate_css(theme) if self.config.include_css else ""

        # 生成 MathJax 配置
        mathjax_config = self._generate_mathjax_config() if self.config.include_mathjax else ""

        html_template = f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    {mathjax_config}
    {css}
</head>
<body>
    <div class="container">
        {body_html}
    </div>
</body>
</html>"""

        return html_template

    def _generate_css(self, theme: Dict) -> str:
        """生成增強版主題 CSS"""

        # PDF 優化樣式
        pdf_styles = """
        @media print {
            body {
                margin: 0;
                padding: 20px;
                background: white !important;
                color: black !important;
            }
            .container { max-width: none; }
            .theme-list li::before {
                filter: grayscale(100%);
                opacity: 0.8;
            }
            .fill-blank {
                border-bottom: 2px solid black !important;
            }
        }
        """ if self.config.pdf_mode else ""

        # 深色主題特殊處理
        is_dark_theme = theme.get('category') == 'cosmic'

        # 生成填空線樣式
        fill_blank_border = self._get_fill_blank_style(theme)

        return f"""
    <style>
        /* CSS 變數定義 */
        :root {{
            --theme-primary: {theme['primary_color']};
            --theme-secondary: {theme['secondary_color']};
            --theme-accent: {theme['accent_color']};
            --theme-background: {theme['background_color']};
            --theme-text: {theme['text_color']};
            --theme-border: {theme['border_color']};
            --theme-gradient-primary: {theme['gradient_primary']};
            --theme-gradient-accent: {theme['gradient_accent']};
            --theme-shadow-primary: {theme['shadow_primary']};
            --theme-shadow-accent: {theme['shadow_accent']};
        }}

        /* 基礎佈局 */
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Noto Sans TC', sans-serif;
            line-height: 1.7;
            color: var(--theme-text);
            background: var(--theme-background);
            max-width: 900px;
            margin: 0 auto;
            padding: 30px 20px;
            transition: all 0.3s ease;
        }}

        .container {{
            max-width: 100%;
            background: var(--theme-background);
            border-radius: 12px;
            padding: 20px;
            /* 移除邊框和陰影效果 */
        }}

        /* 增強標題樣式 */
        .theme-h1 {{
            color: var(--theme-primary);
            /* 移除 background-clip: text 以確保 PDF 兼容性 */
            background: transparent;
            border-bottom: 3px solid var(--theme-primary);
            padding-bottom: 15px;
            margin-bottom: 25px;
            position: relative;
            font-weight: 700;
        }}

        /* H1 標題裝飾線已移除 */

        .theme-h2 {{
            color: var(--theme-primary);
            border-left: 6px solid var(--theme-accent);
            padding-left: 20px;
            margin: 25px 0 15px 0;
            background: linear-gradient(90deg, var(--theme-border) 0%, transparent 100%);
            padding-top: 10px;
            padding-bottom: 10px;
            border-radius: 0 8px 8px 0;
            font-weight: 600;
        }}

        .theme-h3 {{
            color: var(--theme-secondary);
            border-bottom: 2px dotted var(--theme-accent);
            padding-bottom: 8px;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }}

        /* 增強列表樣式 */
        .theme-list {{
            list-style-type: none;
            padding-left: 0;
            margin: 15px 0;
        }}

        .theme-list li {{
            margin: 8px 0;
            padding-left: 35px;
            position: relative;
            line-height: 1.6;
        }}

        /* 多層級列表圖標 */
        .theme-list li::before {{
            position: absolute;
            left: 0;
            top: 0;
            font-size: 1.2em;
            line-height: 1.6;
        }}

        .theme-list li::before {{ content: "{theme['list_icons']['primary']} "; }}
        .theme-list .theme-list li::before {{ content: "{theme['list_icons']['secondary']} "; }}
        .theme-list .theme-list .theme-list li::before {{ content: "{theme['list_icons']['tertiary']} "; }}
        .theme-list .theme-list .theme-list .theme-list li::before {{ content: "{theme['list_icons']['quaternary']} "; }}
        .theme-list .theme-list .theme-list .theme-list .theme-list li::before {{ content: "{theme['list_icons']['quinary']} "; }}

        /* 檢查清單樣式 */
        .task-list {{
            list-style-type: none;
            padding-left: 0;
        }}

        .task-list-item {{
            margin: 8px 0;
            padding-left: 0 !important;
            position: relative;
        }}

        .task-list-item::before {{
            display: none !important; /* 隱藏主題圖標 */
        }}

        .task-list-checkbox {{
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: var(--theme-primary);
        }}

        /* 增強填空線樣式 */
        .fill-blank {{
            {fill_blank_border}
            display: inline-block;
            min-width: 120px;
            margin: 0 8px;
            padding: 2px 0;
            position: relative;
        }}

        /* 圖片增強 */
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: var(--theme-shadow-accent);
            transition: transform 0.3s ease;
        }}

        img:hover {{
            transform: scale(1.02);
        }}

        /* 增強表格樣式 */
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 25px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: var(--theme-shadow-primary);
        }}

        th, td {{
            border: 1px solid var(--theme-border);
            padding: 15px 12px;
            text-align: left;
        }}

        th {{
            background: var(--theme-gradient-primary);
            color: white;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }}

        tr:nth-child(even) {{
            background-color: var(--theme-border);
        }}

        /* 增強引用樣式 */
        blockquote {{
            border-left: 6px solid var(--theme-accent);
            margin: 25px 0;
            padding: 20px 25px;
            background: {theme['quote_bg_color']};
            border-radius: 0 8px 8px 0;
            box-shadow: var(--theme-shadow-accent);
            position: relative;
        }}

        blockquote::before {{
            content: '"';
            font-size: 4em;
            color: var(--theme-accent);
            position: absolute;
            top: -10px;
            left: 10px;
            opacity: 0.3;
        }}

        /* 增強代碼樣式 */
        code {{
            background-color: {theme['code_bg_color']};
            color: var(--theme-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.9em;
            border: 1px solid var(--theme-border);
        }}

        pre {{
            background-color: {theme['code_bg_color']};
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            border: 1px solid var(--theme-border);
            box-shadow: var(--theme-shadow-accent);
        }}

        /* 數學公式區域增強 */
        .MathJax {{
            background-color: {theme['math_bg_color']} !important;
            padding: 8px 12px !important;
            border-radius: 6px !important;
            border: 1px solid var(--theme-border) !important;
            margin: 5px 0 !important;
        }}

        /* 水平線增強 */
        hr {{
            border: none;
            height: 3px;
            background: var(--theme-gradient-accent);
            margin: 30px 0;
            border-radius: 2px;
        }}

        /* 響應式設計 */
        @media (max-width: 768px) {{
            body {{ padding: 20px 15px; }}
            .container {{ padding: 15px; }}
            .theme-list li {{ padding-left: 30px; }}
            table {{ font-size: 0.9em; }}
            th, td {{ padding: 10px 8px; }}
        }}

        {pdf_styles}
    </style>"""

    def _get_fill_blank_style(self, theme: Dict) -> str:
        """根據主題生成填空線樣式"""
        style_type = theme.get('fill_blank_style', 'solid')
        color = theme['primary_color']

        if style_type == 'solid':
            return f"border-bottom: 2px solid {color};"
        elif style_type == 'dotted':
            return f"border-bottom: 2px dotted {color};"
        elif style_type == 'dashed':
            return f"border-bottom: 2px dashed {color};"
        elif style_type == 'double':
            return f"border-bottom: 3px double {color};"
        else:
            return f"border-bottom: 2px solid {color};"

    def _generate_mathjax_config(self) -> str:
        """生成 MathJax 配置"""
        return """
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$']],
                displayMath: [['$$', '$$']],
                processEscapes: true,
                processEnvironments: true
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
            }
        };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>"""

    def _save_html(self, html_content: str, output_file: str):
        """保存 HTML 到檔案"""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            logger.info(f"HTML 已保存到: {output_path}")

        except Exception as e:
            error_msg = f"保存檔案錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)


# 便利函數
def convert_markdown_file(
    input_file: str,
    output_file: Optional[str] = None,
    config: Optional[ConversionConfig] = None
) -> ConversionResult:
    """
    轉換 Markdown 檔案到 HTML

    Args:
        input_file: 輸入的 Markdown 檔案路徑
        output_file: 輸出的 HTML 檔案路徑（可選）
        config: 轉換配置（可選）

    Returns:
        ConversionResult: 轉換結果
    """
    try:
        # 讀取 Markdown 檔案
        with open(input_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read()

        # 設置默認輸出檔案名
        if output_file is None:
            input_path = Path(input_file)
            output_file = str(input_path.with_suffix('.html'))

        # 創建轉換器並轉換
        converter = UnifiedMarkdownConverter(config)
        result = converter.convert(markdown_content, output_file)

        return result

    except Exception as e:
        logger.error(f"檔案轉換錯誤: {str(e)}")
        return ConversionResult(
            html_content="",
            title="轉換失敗",
            statistics={},
            warnings=[],
            errors=[f"檔案轉換錯誤: {str(e)}"]
        )


def convert_markdown_string(
    markdown_content: str,
    config: Optional[ConversionConfig] = None
) -> ConversionResult:
    """
    轉換 Markdown 字符串到 HTML

    Args:
        markdown_content: Markdown 文本內容
        config: 轉換配置（可選）

    Returns:
        ConversionResult: 轉換結果
    """
    converter = UnifiedMarkdownConverter(config)
    return converter.convert(markdown_content)


# 主程序入口
if __name__ == "__main__":
    import sys

    if len(sys.argv) < 2:
        print("使用方法: python markdown_to_html_unified.py <input.md> [output.html]")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None

    # 創建配置
    config = ConversionConfig(
        theme_name="default",
        enable_math=True,
        enable_images=True,
        enable_fill_blanks=True,
        include_mathjax=True,
        include_css=True
    )

    # 轉換檔案
    result = convert_markdown_file(input_file, output_file, config)

    # 顯示結果
    if result.success:
        print(f"✅ 轉換成功!")
        print(f"📄 標題: {result.title}")
        print(f"📊 統計: {result.statistics}")
        if result.warnings:
            print(f"⚠️  警告: {result.warnings}")
    else:
        print(f"❌ 轉換失敗!")
        print(f"🚫 錯誤: {result.errors}")
        sys.exit(1)
