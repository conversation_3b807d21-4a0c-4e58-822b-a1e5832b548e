#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育材料轉換系統 - 主程式入口

版本: 3.0.0
作者: 教育材料轉換系統開發團隊
日期: 2024-12-02

這是教育材料轉換系統的主程式入口，提供命令行接口和 Python API。
基於統一 Markdown 轉換器 (markdown_to_html_unified.py) 構建。

主要功能：
- 命令行工具：直接轉換 Markdown 檔案
- Python API：程式化調用轉換功能
- 批量處理：支援多檔案和資料夾處理
- 配置管理：靈活的配置選項
- 錯誤處理：完整的錯誤報告和日誌

使用範例：
    # 命令行使用
    python main_converter.py input.md
    python main_converter.py input.md -o output.html -t nature
    python main_converter.py folder/ -b -t tech
    
    # Python API 使用
    from main_converter import convert_file, convert_string
    result = convert_file("input.md", theme="nature")
"""

import argparse
import sys
import os
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
import time

# 導入核心轉換器
try:
    from markdown_to_html_unified import (
        UnifiedMarkdownConverter,
        ConversionConfig,
        ConversionResult,
        convert_markdown_file,
        convert_markdown_string
    )
except ImportError as e:
    print(f"錯誤: 無法導入核心轉換器 - {e}")
    print("請確保 markdown_to_html_unified.py 在同一目錄中")
    sys.exit(1)

# 配置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

# 版本信息
VERSION = "3.0.0"
PROGRAM_NAME = "教育材料轉換系統"


def create_config_from_args(args) -> ConversionConfig:
    """
    從命令行參數創建轉換配置
    
    Args:
        args: 解析後的命令行參數
        
    Returns:
        ConversionConfig: 轉換配置對象
    """
    return ConversionConfig(
        theme_name=args.theme,
        enable_math=not args.no_math,
        enable_images=not args.no_images,
        enable_fill_blanks=not args.no_fill_blanks,
        enable_themes=not args.no_themes,
        pdf_mode=args.pdf_mode,
        include_mathjax=not args.no_mathjax,
        include_css=not args.no_css,
        image_base_path=args.image_base_path or "",
        strict_mode=args.strict
    )


def convert_single_file(input_file: str, output_file: Optional[str], config: ConversionConfig) -> bool:
    """
    轉換單個檔案
    
    Args:
        input_file: 輸入檔案路徑
        output_file: 輸出檔案路徑（可選）
        config: 轉換配置
        
    Returns:
        bool: 轉換是否成功
    """
    try:
        input_path = Path(input_file)
        
        # 檢查輸入檔案
        if not input_path.exists():
            logger.error(f"輸入檔案不存在: {input_file}")
            return False
        
        if not input_path.is_file():
            logger.error(f"輸入路徑不是檔案: {input_file}")
            return False
        
        # 確定輸出檔案路徑
        if output_file is None:
            output_file = str(input_path.with_suffix('.html'))
        
        logger.info(f"轉換檔案: {input_file} -> {output_file}")
        
        # 執行轉換
        result = convert_markdown_file(input_file, output_file, config)
        
        if result.success:
            logger.info(f"✅ 轉換成功: {result.title}")
            logger.info(f"📊 統計: {result.statistics}")
            if result.has_warnings:
                logger.warning(f"⚠️  警告: {result.warnings}")
            return True
        else:
            logger.error(f"❌ 轉換失敗: {result.errors}")
            return False
            
    except Exception as e:
        logger.error(f"轉換檔案時發生錯誤: {e}")
        return False


def convert_batch(input_path: str, config: ConversionConfig) -> Dict[str, bool]:
    """
    批量轉換檔案
    
    Args:
        input_path: 輸入路徑（檔案或資料夾）
        config: 轉換配置
        
    Returns:
        Dict[str, bool]: 轉換結果字典，鍵為檔案路徑，值為是否成功
    """
    results = {}
    input_path_obj = Path(input_path)
    
    if input_path_obj.is_file():
        # 單個檔案
        if input_path_obj.suffix.lower() == '.md':
            success = convert_single_file(str(input_path_obj), None, config)
            results[str(input_path_obj)] = success
        else:
            logger.warning(f"跳過非 Markdown 檔案: {input_path}")
    
    elif input_path_obj.is_dir():
        # 資料夾
        md_files = list(input_path_obj.rglob('*.md'))
        
        if not md_files:
            logger.warning(f"在資料夾中未找到 Markdown 檔案: {input_path}")
            return results
        
        logger.info(f"找到 {len(md_files)} 個 Markdown 檔案")
        
        for md_file in md_files:
            success = convert_single_file(str(md_file), None, config)
            results[str(md_file)] = success
    
    else:
        logger.error(f"輸入路徑無效: {input_path}")
    
    return results


def print_batch_summary(results: Dict[str, bool]):
    """
    打印批量轉換結果摘要
    
    Args:
        results: 轉換結果字典
    """
    total = len(results)
    successful = sum(1 for success in results.values() if success)
    failed = total - successful
    
    print(f"\n📋 批量轉換摘要:")
    print(f"   總檔案數: {total}")
    print(f"   成功: {successful}")
    print(f"   失敗: {failed}")
    
    if failed > 0:
        print(f"\n❌ 失敗的檔案:")
        for file_path, success in results.items():
            if not success:
                print(f"   - {file_path}")


def setup_argument_parser() -> argparse.ArgumentParser:
    """
    設置命令行參數解析器
    
    Returns:
        argparse.ArgumentParser: 配置好的參數解析器
    """
    parser = argparse.ArgumentParser(
        prog='main_converter',
        description=f'{PROGRAM_NAME} v{VERSION} - Markdown 到 HTML 轉換工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  %(prog)s input.md                          # 基本轉換
  %(prog)s input.md -o output.html           # 指定輸出檔案
  %(prog)s input.md -t nature                # 使用自然主題
  %(prog)s folder/ -b                        # 批量轉換資料夾
  %(prog)s input.md --pdf-mode               # PDF 優化模式
  %(prog)s input.md --no-math --no-images    # 禁用特定功能

支援的主題: default, nature, tech, space, sport
        """
    )
    
    # 基本參數
    parser.add_argument('input', help='輸入 Markdown 檔案或資料夾路徑')
    parser.add_argument('-o', '--output', help='輸出 HTML 檔案路徑（僅適用於單檔案轉換）')
    parser.add_argument('-v', '--version', action='version', version=f'{PROGRAM_NAME} v{VERSION}')
    
    # 主題和樣式
    parser.add_argument('-t', '--theme', default='default', 
                       choices=['default', 'nature', 'tech', 'space', 'sport'],
                       help='選擇主題 (默認: default)')
    parser.add_argument('--pdf-mode', action='store_true', help='啟用 PDF 優化模式')
    
    # 功能開關
    parser.add_argument('--no-math', action='store_true', help='禁用數學公式處理')
    parser.add_argument('--no-images', action='store_true', help='禁用圖片處理')
    parser.add_argument('--no-fill-blanks', action='store_true', help='禁用填空線處理')
    parser.add_argument('--no-themes', action='store_true', help='禁用主題系統')
    parser.add_argument('--no-mathjax', action='store_true', help='不包含 MathJax 腳本')
    parser.add_argument('--no-css', action='store_true', help='不包含內建 CSS')
    
    # 高級選項
    parser.add_argument('-b', '--batch', action='store_true', help='批量處理模式')
    parser.add_argument('--image-base-path', help='圖片基礎路徑')
    parser.add_argument('--strict', action='store_true', help='啟用嚴格模式')
    parser.add_argument('--quiet', action='store_true', help='靜默模式（減少輸出）')
    parser.add_argument('--debug', action='store_true', help='啟用調試模式')
    
    return parser


def main():
    """主程式入口"""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # 設置日誌級別
    if args.quiet:
        logging.getLogger().setLevel(logging.WARNING)
    elif args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 顯示程式信息
    if not args.quiet:
        print(f"{PROGRAM_NAME} v{VERSION}")
        print("=" * 50)
    
    # 創建轉換配置
    config = create_config_from_args(args)
    
    start_time = time.time()
    
    try:
        if args.batch:
            # 批量處理模式
            logger.info("啟動批量處理模式")
            results = convert_batch(args.input, config)
            
            if not args.quiet:
                print_batch_summary(results)
            
            # 返回適當的退出碼
            failed_count = sum(1 for success in results.values() if not success)
            sys.exit(0 if failed_count == 0 else 1)
        
        else:
            # 單檔案處理模式
            success = convert_single_file(args.input, args.output, config)
            
            if not args.quiet:
                elapsed_time = time.time() - start_time
                print(f"\n⏱️  總處理時間: {elapsed_time:.2f} 秒")
            
            sys.exit(0 if success else 1)
    
    except KeyboardInterrupt:
        logger.info("用戶中斷操作")
        sys.exit(130)
    except Exception as e:
        logger.error(f"程式執行錯誤: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


# Python API 函數（供其他模組使用）
def convert_file(input_file: str, output_file: Optional[str] = None, **kwargs) -> ConversionResult:
    """
    Python API: 轉換單個檔案

    Args:
        input_file: 輸入檔案路徑
        output_file: 輸出檔案路徑（可選）
        **kwargs: 轉換配置參數
            theme: 主題名稱 (會自動轉換為 theme_name)
            pdf_mode: PDF 模式
            enable_math: 啟用數學公式
            enable_images: 啟用圖片處理
            等等...

    Returns:
        ConversionResult: 轉換結果
    """
    # 處理參數名稱映射
    if 'theme' in kwargs:
        kwargs['theme_name'] = kwargs.pop('theme')

    config = ConversionConfig(**kwargs)
    return convert_markdown_file(input_file, output_file, config)


def convert_string(markdown_content: str, **kwargs) -> ConversionResult:
    """
    Python API: 轉換 Markdown 字符串

    Args:
        markdown_content: Markdown 內容
        **kwargs: 轉換配置參數
            theme: 主題名稱 (會自動轉換為 theme_name)
            pdf_mode: PDF 模式
            enable_math: 啟用數學公式
            enable_images: 啟用圖片處理
            等等...

    Returns:
        ConversionResult: 轉換結果
    """
    # 處理參數名稱映射
    if 'theme' in kwargs:
        kwargs['theme_name'] = kwargs.pop('theme')

    config = ConversionConfig(**kwargs)
    return convert_markdown_string(markdown_content, config)


if __name__ == "__main__":
    main()
