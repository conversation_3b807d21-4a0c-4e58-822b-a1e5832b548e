# 教育材料轉換系統

針對香港10-17歲學生的教材轉換系統，將 Markdown 格式的教育材料轉換為 HTML 和 PDF 格式。

## 🎯 項目目標

- 支援 Markdown 檔案轉換為 HTML 和 PDF
- 處理相對路徑圖片引用
- 支援單檔案和資料夾上傳
- 提供多種主題選擇
- 優化教育材料的排版和顯示

## 🏗️ 系統架構

```
education-material-converter/
├── frontend/          # Next.js + TypeScript + Tailwind CSS
├── backend/           # FastAPI + Python
├── processor/         # 核心處理引擎
├── docs/             # 文檔
├── tests/            # 測試檔案
└── docker-compose.yml # 容器編排
```

## 🚀 快速開始

### 前端開發

```bash
cd frontend
npm install
npm run dev
```

前端將在 http://localhost:3000 啟動

### 後端開發

```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
python main.py
```

後端將在 http://localhost:8000 啟動

### 使用 Docker

```bash
docker-compose up -d
```

## 📋 功能特色

- ✅ Markdown 到 HTML 轉換
- ✅ HTML 到 PDF 轉換
- ✅ 多種主題支援
- ✅ 圖片路徑處理
- ✅ 數學公式渲染
- ✅ 響應式設計

## 🛠️ 技術棧

### 前端
- Next.js 15.3.3
- React 19
- TypeScript
- Tailwind CSS
- Axios
- React Dropzone

### 後端
- FastAPI
- Python 3.11+
- Uvicorn
- Pydantic
- Aiofiles

### 處理引擎
- Markdown-it-py
- WeasyPrint
- BeautifulSoup4
- Pillow

## 📖 API 文檔

啟動後端服務後，訪問：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 測試

```bash
# 前端測試
cd frontend
npm test

# 後端測試
cd backend
source venv/bin/activate
pytest
```

## 📝 開發進度

請參考 `教育材料轉換系統開發進度追蹤計劃.md` 文檔查看詳細的開發進度。

## 🤝 貢獻指南

1. Fork 本項目
2. 創建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

## 📄 授權

本項目採用 MIT 授權 - 詳見 [LICENSE](LICENSE) 文件

## 📞 聯絡方式

- 項目連結: [https://github.com/your-username/education-material-converter](https://github.com/your-username/education-material-converter)
- 問題回報: [Issues](https://github.com/your-username/education-material-converter/issues)

---

**版本**: 0.1.0  
**最後更新**: 2024年12月
