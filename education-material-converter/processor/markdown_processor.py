#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育材料轉換系統 - Markdown處理器核心模組
版本: 2.1.0
作者: 開發團隊
日期: 2024-12-02

此模組實現了完整的Markdown處理流程，包含：
- 預處理階段：文本清理和標準化
- 解析階段：Markdown語法解析
- 轉換階段：HTML轉換和特殊元素處理
- 後處理階段：HTML優化和驗證

特殊功能：
- 填空線處理（4+下劃線轉換為填空框）
- 練習區塊自動識別
- 表格空儲存格處理
- 數學公式預處理
"""

import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
from markdown_it import MarkdownIt
from markdown_it.renderer import RendererHTML
from bs4 import BeautifulSoup, Tag
import html

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class ProcessingConfig:
    """處理配置類"""
    enable_fill_blanks: bool = True
    enable_exercise_blocks: bool = True
    enable_table_enhancement: bool = True
    enable_math_preprocessing: bool = True
    preserve_line_breaks: bool = True
    strict_mode: bool = False


@dataclass
class ProcessingResult:
    """處理結果類"""
    html_content: str
    metadata: Dict[str, Any]
    statistics: Dict[str, int]
    warnings: List[str]
    errors: List[str]


class MarkdownProcessor:
    """
    Markdown處理器核心類
    
    負責將教育材料Markdown文檔轉換為結構化HTML，
    支援教育材料特有的格式和元素。
    """
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """
        初始化處理器
        
        Args:
            config: 處理配置，如果為None則使用默認配置
        """
        self.config = config or ProcessingConfig()
        self.md = self._setup_markdown_parser()
        self.statistics = {
            'total_lines': 0,
            'fill_blanks_count': 0,
            'exercise_blocks_count': 0,
            'tables_count': 0,
            'math_formulas_count': 0,
            'images_count': 0,
            'links_count': 0
        }
        self.warnings = []
        self.errors = []
        
        # 初始化特殊元素處理器
        self.fill_blank_processor = FillBlankProcessor()
        self.exercise_block_processor = ExerciseBlockProcessor()
        self.table_processor = TableProcessor()
        
        logger.info("MarkdownProcessor 初始化完成")
    
    def _setup_markdown_parser(self) -> MarkdownIt:
        """設置Markdown解析器"""
        md = MarkdownIt("commonmark", {
            "html": True,
            "linkify": True,
            "typographer": True,
        })

        # 啟用額外的插件（只啟用可用的）
        try:
            md.enable([
                "table",
                "strikethrough"
            ])
        except ValueError as e:
            logger.warning(f"部分Markdown插件無法啟用: {e}")

        return md
    
    def process(self, markdown_content: str) -> ProcessingResult:
        """
        處理Markdown內容的主要方法
        
        Args:
            markdown_content: 原始Markdown文本
            
        Returns:
            ProcessingResult: 處理結果
        """
        try:
            logger.info("開始處理Markdown內容")
            
            # 重置統計和錯誤信息
            self._reset_statistics()
            
            # 階段1: 預處理
            preprocessed_content = self._preprocess(markdown_content)
            
            # 階段2: 解析
            parsed_tokens = self._parse(preprocessed_content)
            
            # 階段3: 轉換
            html_content = self._convert_to_html(parsed_tokens)
            
            # 階段4: 後處理
            final_html = self._postprocess(html_content)
            
            # 生成元數據
            metadata = self._generate_metadata(preprocessed_content)
            
            logger.info("Markdown處理完成")
            
            return ProcessingResult(
                html_content=final_html,
                metadata=metadata,
                statistics=self.statistics.copy(),
                warnings=self.warnings.copy(),
                errors=self.errors.copy()
            )
            
        except Exception as e:
            error_msg = f"處理Markdown時發生錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            
            return ProcessingResult(
                html_content="",
                metadata={},
                statistics=self.statistics.copy(),
                warnings=self.warnings.copy(),
                errors=self.errors.copy()
            )
    
    def _reset_statistics(self):
        """重置統計信息"""
        self.statistics = {key: 0 for key in self.statistics}
        self.warnings.clear()
        self.errors.clear()
    
    def _preprocess(self, content: str) -> str:
        """
        預處理階段：清理和標準化文本
        
        Args:
            content: 原始Markdown內容
            
        Returns:
            str: 預處理後的內容
        """
        logger.debug("開始預處理階段")
        
        # 統計總行數
        self.statistics['total_lines'] = len(content.splitlines())
        
        # 1. 標準化換行符
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 2. 移除行尾空白
        lines = []
        for line in content.splitlines():
            lines.append(line.rstrip())
        content = '\n'.join(lines)
        
        # 3. 處理數學公式（如果啟用）
        if self.config.enable_math_preprocessing:
            content = self._preprocess_math_formulas(content)
        
        # 4. 處理填空線（如果啟用）
        if self.config.enable_fill_blanks:
            content = self._preprocess_fill_blanks(content)

        # 5. 統一化水平線數量
        content = self._normalize_horizontal_lines(content)

        # 6. 標準化圖片路徑
        content = self._normalize_image_paths(content)
        
        logger.debug("預處理階段完成")
        return content
    
    def _preprocess_math_formulas(self, content: str) -> str:
        """預處理數學公式"""
        # 統計數學公式數量
        inline_math_count = len(re.findall(r'\$[^$]+\$', content))
        display_math_count = len(re.findall(r'\$\$[^$]+\$\$', content))
        self.statistics['math_formulas_count'] = inline_math_count + display_math_count
        
        # 暫時保護數學公式不被其他處理影響
        # 這裡可以添加更複雜的數學公式預處理邏輯
        return content
    
    def _preprocess_fill_blanks(self, content: str) -> str:
        """預處理填空線"""
        # 識別填空線模式（4個或以上連續下劃線）
        fill_blank_pattern = r'_{4,}'
        matches = re.findall(fill_blank_pattern, content)
        self.statistics['fill_blanks_count'] = len(matches)
        
        return content

    def _normalize_horizontal_lines(self, content: str) -> str:
        """統一化連續水平線數量，保持單獨水平線不變"""
        lines = content.splitlines()
        processed_lines = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 檢查是否為水平線
            if line in ['---', '***', '___']:
                # 統計連續的水平線數量（不允許空行間隔）
                consecutive_count = 1
                j = i + 1

                # 查找緊接著的連續水平線
                while j < len(lines) and lines[j].strip() in ['---', '***', '___']:
                    consecutive_count += 1
                    j += 1

                # 只有當連續水平線數量大於1時才統一為4條
                if consecutive_count > 1:
                    # 添加統一的4條水平線
                    for _ in range(4):
                        processed_lines.append('---')

                    # 跳過原來的連續水平線
                    i = j
                    continue
                else:
                    # 單獨的水平線保持原樣
                    processed_lines.append(lines[i])
                    i += 1
                    continue

            # 普通行直接添加
            processed_lines.append(lines[i])
            i += 1

        return '\n'.join(processed_lines)

    def _normalize_image_paths(self, content: str) -> str:
        """標準化圖片路徑"""
        # 統計圖片數量
        image_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        matches = re.findall(image_pattern, content)
        self.statistics['images_count'] = len(matches)

        # 圖片路徑映射表（placeholder URL -> 實際檔案）
        image_mapping = {
            'https://placeholder-image.com/student-smile': '../../../TestFile/photo/Gemini_Generated_Image_1.png',
            'https://placeholder-image.com/positive-attitude': '../../../TestFile/photo/Gemini_Generated_Image_2.png',
            'https://placeholder-image.com/clear-confident-speech': '../../../TestFile/photo/Gemini_Generated_Image_3.png',
            'https://placeholder-image.com/sound-waves-comparison': '../../../TestFile/photo/Gemini_Generated_Image_1.png',
            'https://placeholder-image.com/pronunciation-qr': '../../../TestFile/photo/Gemini_Generated_Image_2.png',
            'https://placeholder-image.com/reading-progress': '../../../TestFile/photo/Gemini_Generated_Image_3.png',
            'https://placeholder-image.com/thinking-responding': '../../../TestFile/photo/Gemini_Generated_Image_1.png',
            # Lesson3 圖片映射
            'https://placeholder-image.com/observation-imagination': '../../../TestFile/photo/Gemini_Generated_Image_2.png',
            'https://placeholder-image.com/picture-story-structure': '../../../TestFile/photo/Gemini_Generated_Image_3.png',
            'https://placeholder-image.com/practice-image-1': '../../../TestFile/photo/Gemini_Generated_Image_1.png',
            'https://placeholder-image.com/practice-image-2': '../../../TestFile/photo/Gemini_Generated_Image_2.png',
        }

        # 替換圖片路徑
        def replace_image_path(match):
            alt_text = match.group(1)
            original_url = match.group(2)

            # 如果是placeholder URL，替換為實際路徑
            if original_url in image_mapping:
                new_url = image_mapping[original_url]
                logger.debug(f"圖片路徑映射: {original_url} -> {new_url}")
                return f'![{alt_text}]({new_url})'

            # 如果是相對路徑，處理路徑和尺寸參數
            elif not original_url.startswith(('http://', 'https://', '/')):
                # 分離路徑和參數
                if '|' in original_url:
                    path_part, params_part = original_url.split('|', 1)
                    # 處理圖片尺寸參數
                    processed_url = self._process_image_params(path_part, params_part)
                else:
                    path_part = original_url
                    processed_url = original_url

                # 修正路徑前綴
                if path_part.startswith('TestFile/'):
                    new_url = f'../../../{processed_url}'
                else:
                    new_url = f'../../../TestFile/{processed_url}'

                logger.debug(f"相對路徑轉換: {original_url} -> {new_url}")
                return f'![{alt_text}]({new_url})'

            # 其他情況保持原樣
            return match.group(0)

        content = re.sub(image_pattern, replace_image_path, content)
        return content

    def _process_image_params(self, path: str, params: str) -> str:
        """處理圖片尺寸和佈局參數"""
        # 這個方法暫時返回原始路徑，因為markdown-it-py不直接支援自定義圖片屬性
        # 實際的圖片尺寸處理將在後處理階段進行
        return f"{path}|{params}"

    def _parse(self, content: str) -> List[Dict]:
        """
        解析階段：將Markdown轉換為token
        
        Args:
            content: 預處理後的Markdown內容
            
        Returns:
            List[Dict]: 解析後的token列表
        """
        logger.debug("開始解析階段")
        
        try:
            tokens = self.md.parse(content)
            logger.debug(f"解析完成，生成 {len(tokens)} 個tokens")
            return tokens
        except Exception as e:
            error_msg = f"解析Markdown時發生錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return []
    
    def _convert_to_html(self, tokens: List[Dict]) -> str:
        """
        轉換階段：將tokens轉換為HTML
        
        Args:
            tokens: 解析後的token列表
            
        Returns:
            str: 轉換後的HTML內容
        """
        logger.debug("開始轉換階段")
        
        try:
            html_content = self.md.renderer.render(tokens, self.md.options, {})
            logger.debug("HTML轉換完成")
            return html_content
        except Exception as e:
            error_msg = f"轉換HTML時發生錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return ""
    
    def _postprocess(self, html_content: str) -> str:
        """
        後處理階段：優化和增強HTML
        
        Args:
            html_content: 原始HTML內容
            
        Returns:
            str: 後處理後的HTML內容
        """
        logger.debug("開始後處理階段")
        
        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 處理填空線
            if self.config.enable_fill_blanks:
                soup = self.fill_blank_processor.process(soup)
            
            # 處理練習區塊
            if self.config.enable_exercise_blocks:
                soup = self.exercise_block_processor.process(soup)
            
            # 處理表格
            if self.config.enable_table_enhancement:
                soup = self.table_processor.process(soup)

            # 處理圖片尺寸參數
            soup = self._process_image_attributes(soup)

            # 統計連結數量
            links = soup.find_all('a')
            self.statistics['links_count'] = len(links)
            
            # 統計表格數量
            tables = soup.find_all('table')
            self.statistics['tables_count'] = len(tables)
            
            logger.debug("後處理階段完成")
            return str(soup)
            
        except Exception as e:
            error_msg = f"後處理HTML時發生錯誤: {str(e)}"
            logger.error(error_msg)
            self.errors.append(error_msg)
            return html_content
    
    def _generate_metadata(self, content: str) -> Dict[str, Any]:
        """生成文檔元數據"""
        metadata = {
            'title': self._extract_title(content),
            'word_count': len(content.split()),
            'character_count': len(content),
            'processing_timestamp': None,  # 可以添加時間戳
            'config': {
                'fill_blanks_enabled': self.config.enable_fill_blanks,
                'exercise_blocks_enabled': self.config.enable_exercise_blocks,
                'table_enhancement_enabled': self.config.enable_table_enhancement,
                'math_preprocessing_enabled': self.config.enable_math_preprocessing
            }
        }
        return metadata
    
    def _extract_title(self, content: str) -> str:
        """提取文檔標題"""
        lines = content.splitlines()
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
        return "未命名文檔"

    def _process_image_attributes(self, soup: BeautifulSoup) -> BeautifulSoup:
        """處理圖片尺寸和佈局屬性"""
        import urllib.parse

        images = soup.find_all('img')

        for img in images:
            src = img.get('src', '')

            # 檢查是否包含URL編碼的參數 (%7C 是 | 的編碼)
            if '%7C' in src:
                # URL解碼
                decoded_src = urllib.parse.unquote(src)

                # 分離路徑和參數
                if '|' in decoded_src:
                    parts = decoded_src.split('|')
                    actual_src = parts[0]
                    params = parts[1:]

                    # 更新圖片src
                    img['src'] = actual_src

                    # 處理參數
                    style_parts = []
                    html_attrs = {}

                    for param in params:
                        param = param.strip()

                        if param.startswith('width='):
                            width = param.split('=')[1]
                            # 使用HTML屬性而不是CSS，更符合標準
                            html_attrs['width'] = width
                        elif param.startswith('height='):
                            height = param.split('=')[1]
                            html_attrs['height'] = height
                        elif param.startswith('size='):
                            size = param.split('=')[1]
                            style_parts.append(f'width: {size}')
                        elif param == 'left':
                            style_parts.append('float: left')
                            style_parts.append('margin: 0 15px 15px 0')
                        elif param == 'right':
                            style_parts.append('float: right')
                            style_parts.append('margin: 0 0 15px 15px')
                        elif param == 'center':
                            style_parts.append('display: block')
                            style_parts.append('margin: 0 auto')

                    # 應用HTML屬性
                    for attr, value in html_attrs.items():
                        img[attr] = value

                    # 應用樣式
                    if style_parts:
                        existing_style = img.get('style', '')
                        new_style = '; '.join(style_parts)
                        if existing_style:
                            img['style'] = f'{existing_style}; {new_style}'
                        else:
                            img['style'] = new_style

            # 也處理未編碼的情況（向後兼容）
            elif '|' in src:
                parts = src.split('|')
                actual_src = parts[0]
                params = parts[1:]

                img['src'] = actual_src

                style_parts = []
                html_attrs = {}

                for param in params:
                    param = param.strip()

                    if param.startswith('width='):
                        width = param.split('=')[1]
                        html_attrs['width'] = width
                    elif param.startswith('height='):
                        height = param.split('=')[1]
                        html_attrs['height'] = height
                    elif param.startswith('size='):
                        size = param.split('=')[1]
                        style_parts.append(f'width: {size}')
                    elif param == 'left':
                        style_parts.append('float: left')
                        style_parts.append('margin: 0 15px 15px 0')
                    elif param == 'right':
                        style_parts.append('float: right')
                        style_parts.append('margin: 0 0 15px 15px')
                    elif param == 'center':
                        style_parts.append('display: block')
                        style_parts.append('margin: 0 auto')

                for attr, value in html_attrs.items():
                    img[attr] = value

                if style_parts:
                    existing_style = img.get('style', '')
                    new_style = '; '.join(style_parts)
                    if existing_style:
                        img['style'] = f'{existing_style}; {new_style}'
                    else:
                        img['style'] = new_style

        return soup


class FillBlankProcessor:
    """填空線處理器"""

    def process(self, soup: BeautifulSoup) -> BeautifulSoup:
        """處理填空線"""
        logger.debug("處理填空線")

        # 查找所有包含下劃線的文本節點
        for element in soup.find_all(text=True):
            if '____' in element:
                # 替換填空線為靜態下劃線（適合PDF列印）
                new_text = re.sub(
                    r'_{4,}',
                    lambda m: self._create_fill_blank_html(len(m.group())),
                    element
                )

                if new_text != element:
                    # 創建新的HTML片段
                    new_soup = BeautifulSoup(new_text, 'html.parser')
                    element.replace_with(new_soup)

        return soup

    def _create_fill_blank_html(self, length: int) -> str:
        """創建靜態填空線HTML（適合PDF列印）"""
        # 根據原始下劃線長度決定填空線的視覺長度
        if length <= 6:
            # 短填空線：4-6個下劃線
            visual_length = "3em"
            underline_count = 15
        elif length <= 12:
            # 中等填空線：7-12個下劃線
            visual_length = "6em"
            underline_count = 25
        elif length <= 20:
            # 長填空線：13-20個下劃線
            visual_length = "9em"
            underline_count = 35
        else:
            # 超長填空線：20個以上下劃線
            visual_length = "12em"
            underline_count = 45

        # 創建靜態下劃線，使用CSS邊框底線
        return f'<span class="fill-blank-static" style="display: inline-block; width: {visual_length}; border-bottom: 1px solid #333; margin: 0 2px; height: 1.2em; vertical-align: baseline;"></span>'


class ExerciseBlockProcessor:
    """練習區塊處理器"""
    
    def process(self, soup: BeautifulSoup) -> BeautifulSoup:
        """處理練習區塊"""
        logger.debug("處理練習區塊")
        
        # 查找練習標題
        exercise_headers = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        
        for header in exercise_headers:
            header_text = header.get_text().strip()
            if self._is_exercise_header(header_text):
                # 為練習區塊添加特殊樣式
                header['class'] = header.get('class', []) + ['exercise-header']
                
                # 查找練習內容（下一個同級或更低級的標題之前的所有內容）
                exercise_content = self._collect_exercise_content(header)
                
                # 包裝練習區塊
                exercise_div = soup.new_tag('div', **{'class': 'exercise-block'})
                
                # 移動內容到練習區塊中
                current = header.next_sibling
                elements_to_move = [header]
                
                while current and not self._is_header_element(current):
                    if hasattr(current, 'name'):  # 是標籤元素
                        elements_to_move.append(current)
                    current = current.next_sibling
                
                # 在原位置插入練習區塊
                header.insert_before(exercise_div)
                
                # 移動元素到練習區塊中
                for element in elements_to_move:
                    exercise_div.append(element.extract())
        
        return soup
    
    def _is_exercise_header(self, text: str) -> bool:
        """判斷是否為練習標題"""
        exercise_keywords = ['練習', 'Exercise', '習題', '作業', '實踐', '活動']
        return any(keyword in text for keyword in exercise_keywords)
    
    def _is_header_element(self, element) -> bool:
        """判斷是否為標題元素"""
        return hasattr(element, 'name') and element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
    
    def _collect_exercise_content(self, header) -> List:
        """收集練習內容"""
        # 這個方法可以用來收集練習相關的內容
        # 目前簡化處理，實際可以根據需要擴展
        return []


class TableProcessor:
    """表格處理器"""
    
    def process(self, soup: BeautifulSoup) -> BeautifulSoup:
        """處理表格"""
        logger.debug("處理表格")
        
        tables = soup.find_all('table')
        
        for table in tables:
            # 添加表格樣式類
            table['class'] = table.get('class', []) + ['enhanced-table']
            
            # 處理空儲存格
            cells = table.find_all(['td', 'th'])
            for cell in cells:
                if not cell.get_text().strip():
                    # 為空儲存格添加特殊標記
                    cell['class'] = cell.get('class', []) + ['empty-cell']
                    cell.string = '\u00A0'  # 不間斷空格
            
            # 添加表格包裝器以支援響應式設計
            wrapper = soup.new_tag('div', **{'class': 'table-wrapper'})
            table.wrap(wrapper)
        
        return soup
