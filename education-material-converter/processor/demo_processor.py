#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育材料轉換系統 - Markdown處理器示例
版本: 2.1.0
作者: 開發團隊
日期: 2024-12-02

此腳本演示如何使用Markdown處理器處理TestFile中的教育材料。
"""

import sys
import os
from pathlib import Path
from typing import Union

# 添加processor目錄到Python路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from markdown_processor import MarkdownProcessor, ProcessingConfig
import re
import base64


def process_lesson_file(file_path: Path, output_dir: Path = None, output_mode: str = 'web'):
    """
    處理單個課程檔案

    Args:
        file_path: 課程檔案路徑
        output_dir: 輸出目錄，如果為None則輸出到同目錄
        output_mode: 輸出模式 ('web' 或 'pdf')
    """
    print(f"\n📖 正在處理: {file_path.name} (模式: {output_mode})")

    # 讀取檔案內容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 讀取檔案失敗: {e}")
        return False

    # 創建處理器
    config = ProcessingConfig(
        enable_fill_blanks=True,
        enable_exercise_blocks=True,
        enable_table_enhancement=True,
        enable_math_preprocessing=True
    )
    processor = MarkdownProcessor(config)

    # 處理內容
    result = processor.process(content)

    # 檢查錯誤
    if result.errors:
        print(f"❌ 處理過程中發生錯誤:")
        for error in result.errors:
            print(f"   - {error}")
        return False

    # 顯示警告
    if result.warnings:
        print(f"⚠️  警告:")
        for warning in result.warnings:
            print(f"   - {warning}")

    # 顯示統計信息
    print(f"📊 處理統計:")
    print(f"   - 總行數: {result.statistics['total_lines']}")
    print(f"   - 填空線: {result.statistics['fill_blanks_count']}")
    print(f"   - 練習區塊: {result.statistics['exercise_blocks_count']}")
    print(f"   - 表格: {result.statistics['tables_count']}")
    print(f"   - 圖片: {result.statistics['images_count']}")
    print(f"   - 連結: {result.statistics['links_count']}")
    print(f"   - 數學公式: {result.statistics['math_formulas_count']}")

    # 顯示元數據
    print(f"📋 文檔信息:")
    print(f"   - 標題: {result.metadata['title']}")
    print(f"   - 字數: {result.metadata['word_count']}")
    print(f"   - 字符數: {result.metadata['character_count']}")

    # 保存HTML輸出
    if output_dir:
        output_dir.mkdir(exist_ok=True)
        suffix = '_pdf' if output_mode == 'pdf' else ''
        output_file = output_dir / f"{file_path.stem}{suffix}.html"
    else:
        suffix = '_pdf' if output_mode == 'pdf' else '_processed'
        output_file = file_path.parent / f"{file_path.stem}{suffix}.html"

    try:
        # 創建完整的HTML文檔
        full_html = create_full_html_document(result.html_content, result.metadata['title'], output_mode)

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(full_html)

        print(f"✅ HTML輸出已保存到: {output_file}")
        return True

    except Exception as e:
        print(f"❌ 保存HTML檔案失敗: {e}")
        return False


def fix_image_paths_for_pdf(content: str) -> str:
    """
    修復HTML內容中的圖片路徑，將相對路徑轉換為絕對路徑
    解決Puppeteer PDF轉換時圖片無法顯示的問題

    基於網路搜尋的最佳實踐：
    1. 使用絕對路徑替代相對路徑
    2. 採用file://協議確保本地檔案正確載入
    3. 驗證檔案存在性避免404錯誤

    Args:
        content: HTML內容字符串

    Returns:
        修復後的HTML內容字符串
    """
    # 獲取當前工作目錄的絕對路徑
    current_dir = Path(__file__).parent.resolve()

    # 擴展的圖片格式支援
    img_extensions = r'(?:png|jpg|jpeg|gif|svg|webp|bmp|tiff|ico)'

    # 多種路徑模式匹配
    patterns = [
        # 相對路徑：../../../TestFile/photo/xxx.png
        (r'src="(\.\.\/.*?\.' + img_extensions + r')"', 'relative'),
        # 相對路徑：./images/xxx.png
        (r'src="(\.\/.*?\.' + img_extensions + r')"', 'current'),
        # 簡單相對路徑：images/xxx.png
        (r'src="([^http][^:]*\.' + img_extensions + r')"', 'simple'),
    ]

    def replace_path(match, path_type):
        relative_path = match.group(1)

        try:
            # 根據路徑類型處理
            if path_type == 'relative':
                # 處理 ../../../TestFile/photo/xxx.png
                absolute_path = (current_dir / relative_path).resolve()
            elif path_type == 'current':
                # 處理 ./images/xxx.png
                clean_path = relative_path[2:]  # 移除 './'
                absolute_path = (current_dir / clean_path).resolve()
            elif path_type == 'simple':
                # 處理 images/xxx.png
                absolute_path = (current_dir / relative_path).resolve()
            else:
                absolute_path = (current_dir / relative_path).resolve()

            # 檢查檔案是否存在
            if absolute_path.exists():
                # 使用file://協議 - 這是Puppeteer推薦的本地檔案載入方式
                file_url = f'file://{absolute_path}'
                print(f"🔧 路徑修復: {relative_path} -> {file_url}")
                return f'src="{file_url}"'
            else:
                print(f"⚠️  圖片檔案不存在: {absolute_path}")
                # 嘗試在常見目錄中查找（增強路徑搜尋）
                possible_paths = [
                    # 正確的TestFile路徑
                    current_dir.parent.parent / "TestFile" / "photo" / Path(relative_path).name,
                    current_dir.parent / "TestFile" / "photo" / Path(relative_path).name,
                    # 其他可能的路徑
                    current_dir / "TestFile" / "photo" / Path(relative_path).name,
                    current_dir / "images" / Path(relative_path).name,
                    current_dir / Path(relative_path).name,
                    # 絕對路徑嘗試
                    Path("/home/<USER>/文件/Second_Rebuild_Plan/TestFile/photo") / Path(relative_path).name,
                ]

                for possible_path in possible_paths:
                    if possible_path.exists():
                        file_url = f'file://{possible_path.resolve()}'
                        print(f"🔧 找到替代路徑: {relative_path} -> {file_url}")
                        return f'src="{file_url}"'

                # 如果都找不到，嘗試創建預設圖片或使用Base64佔位符
                print(f"❌ 無法找到圖片檔案: {relative_path}")

                # 選項1：使用Base64編碼的預設佔位圖片
                placeholder_base64 = create_placeholder_image_base64(Path(relative_path).name)
                if placeholder_base64:
                    print(f"🔄 使用預設佔位圖片: {Path(relative_path).name}")
                    return f'src="{placeholder_base64}" alt="圖片載入失敗: {Path(relative_path).name}"'

                # 選項2：保持原始路徑但添加警告標記
                return f'src="{relative_path}" data-missing="true" alt="圖片載入失敗: {Path(relative_path).name}"'

        except Exception as e:
            print(f"❌ 路徑轉換失敗: {relative_path}, 錯誤: {e}")
            return match.group(0)  # 保持原始匹配

    # 執行多種模式的路徑替換
    fixed_content = content
    total_fixes = 0

    for pattern, path_type in patterns:
        matches = re.findall(pattern, fixed_content, flags=re.IGNORECASE)
        if matches:
            print(f"🔍 發現 {len(matches)} 個 {path_type} 類型的圖片路徑")
            fixed_content = re.sub(
                pattern,
                lambda m: replace_path(m, path_type),
                fixed_content,
                flags=re.IGNORECASE
            )
            total_fixes += len(matches)

    if total_fixes > 0:
        print(f"✅ 總共修復了 {total_fixes} 個圖片路徑")
    else:
        print("ℹ️  未發現需要修復的圖片路徑")

    return fixed_content


def create_placeholder_image_base64(image_name: str) -> str:
    """
    創建預設佔位圖片的Base64編碼
    當原始圖片找不到時使用

    Args:
        image_name: 圖片檔案名稱

    Returns:
        Base64編碼的佔位圖片數據URL，如果創建失敗則返回空字符串
    """
    try:
        # 創建一個簡單的SVG佔位圖片
        svg_content = f'''<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc" stroke-width="2"/>
            <text x="50%" y="40%" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
                圖片載入失敗
            </text>
            <text x="50%" y="60%" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#999">
                {image_name}
            </text>
        </svg>'''

        # 轉換為Base64
        svg_base64 = base64.b64encode(svg_content.encode('utf-8')).decode('utf-8')
        return f"data:image/svg+xml;base64,{svg_base64}"

    except Exception as e:
        print(f"⚠️  創建佔位圖片失敗: {e}")
        return ""


def convert_image_to_base64(image_path: Path) -> str:
    """
    將圖片檔案轉換為Base64編碼

    Args:
        image_path: 圖片檔案路徑

    Returns:
        Base64編碼的圖片數據URL，如果轉換失敗則返回空字符串
    """
    try:
        if not image_path.exists():
            return ""

        # 讀取圖片檔案
        with open(image_path, 'rb') as f:
            image_data = f.read()

        # 根據檔案副檔名確定MIME類型
        ext = image_path.suffix.lower()
        mime_types = {
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp'
        }

        mime_type = mime_types.get(ext, 'image/png')

        # 轉換為Base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        return f"data:{mime_type};base64,{image_base64}"

    except Exception as e:
        print(f"⚠️  圖片Base64轉換失敗: {image_path}, 錯誤: {e}")
        return ""


def create_full_html_document(content: str, title: str, output_mode: str = 'web') -> str:
    """創建完整的HTML文檔

    Args:
        content: HTML內容
        title: 文檔標題
        output_mode: 輸出模式 ('web' 或 'pdf')

    Returns:
        完整的HTML文檔字符串
    """
    if output_mode == 'pdf':
        return create_pdf_optimized_html(content, title)
    else:
        return create_web_optimized_html(content, title)


def create_web_optimized_html(content: str, title: str) -> str:
    """創建網頁優化的HTML文檔（保持原有視覺效果）"""
    return f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }}

        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}

        h1, h2, h3, h4, h5, h6 {{
            color: #333;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }}

        h1 {{
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }}
        
        .fill-blank-static {{
            display: inline-block;
            border-bottom: 1px solid #333;
            margin: 0 2px;
            height: 1.2em;
            vertical-align: baseline;
            min-height: 1em;
        }}

        /* 水平線樣式 - 用於書寫空間 */
        hr {{
            border: none;
            border-top: 2px solid #333;
            margin: 2.5em 0;
            height: 0;
            clear: both;
        }}

        /* 隱藏包含填空線或水平線的列表項目符號 */
        li:has(.fill-blank-static),
        li:has(hr) {{
            list-style: none;
            margin-left: -30px;
            padding-left: 0;
        }}

        /* 為PDF列印優化的線條樣式 */
        @media print {{
            .fill-blank-static {{
                border-bottom: 1px solid #000;
                page-break-inside: avoid;
                margin: 0 3px;
            }}

            hr {{
                border-top: 2px solid #000;
                margin: 3.5em 0;
                page-break-inside: avoid;
            }}

            /* 填空線與水平線組合的特殊間距 */
            .fill-blank-static + ul li hr,
            li:has(.fill-blank-static) + li hr {{
                margin: 4em 0;
            }}

            /* 列印時也隱藏列表符號 */
            li:has(.fill-blank-static),
            li:has(hr) {{
                list-style: none;
                margin-left: -30px;
                padding-left: 0;
            }}
        }}
        
        .exercise-block {{
            /* 移除特殊樣式，改為普通文字格式 */
            background-color: transparent;
            border: none;
            padding: 0;
            margin: 20px 0;
            border-radius: 0;
        }}

        .exercise-header {{
            /* 移除特殊顏色，使用普通標題樣式 */
            color: #333;
            margin-top: 0;
        }}
        
        .enhanced-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        
        .enhanced-table th,
        .enhanced-table td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        
        .enhanced-table th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        
        .enhanced-table tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .empty-cell {{
            background-color: #fff3cd;
            color: #856404;
            font-style: italic;
        }}
        
        .table-wrapper {{
            overflow-x: auto;
            margin: 20px 0;
        }}
        
        blockquote {{
            /* 移除特殊樣式，改為普通文字格式 */
            border: none;
            margin: 20px 0;
            padding: 0;
            background-color: transparent;
            font-style: normal;
        }}
        
        code {{
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }}
        
        pre {{
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }}
        
        ul, ol {{
            padding-left: 30px;
        }}
        
        li {{
            margin-bottom: 5px;
        }}
        
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        
        a {{
            color: #007acc;
            text-decoration: none;
        }}
        
        a:hover {{
            text-decoration: underline;
        }}
        
        .processing-info {{
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #6c757d;
        }}

        /* MathJax 公式容器優化 - 確保完整顯示 */
        .MathJax_Display, mjx-container[display="true"] {{
            overflow: visible !important;
            max-width: 100% !important;
            width: auto !important;
            margin: 1em auto !important;
            padding: 0.5em 0 !important;
            text-align: center !important;
            box-sizing: border-box !important;
            transform-origin: center center !important;
        }}

        .MathJax, mjx-container {{
            max-width: 100% !important;
            overflow: visible !important;
            font-size: 1em !important;
            line-height: 1.3 !important;
        }}

        /* 行內公式優化 */
        mjx-container:not([display="true"]) {{
            display: inline-block !important;
            vertical-align: middle !important;
            margin: 0 0.1em !important;
            max-width: 100% !important;
        }}

        /* 化學公式特殊處理 */
        mjx-container .mjx-chtml .mjx-mrow .mjx-mi[mathvariant="normal"] {{
            font-family: 'Times New Roman', serif !important;
        }}

        /* 確保長公式不會溢出 */
        mjx-container {{
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
        }}

        /* PDF打印優化 */
        @media print {{
            .MathJax_Display, mjx-container[display="true"] {{
                page-break-inside: avoid !important;
                margin: 0.8em auto !important;
                font-size: 0.9em !important;
            }}

            .MathJax, mjx-container {{
                font-size: 0.85em !important;
            }}

            /* 確保長公式在PDF中可見 */
            mjx-container {{
                max-width: 100% !important;
                overflow: visible !important;
                word-wrap: break-word !important;
            }}
        }}

        /* 響應式數學公式 */
        @media (max-width: 768px) {{
            mjx-container[display="true"] {{
                font-size: 0.9em !important;
                max-width: 98% !important;
            }}

            mjx-container:not([display="true"]) {{
                font-size: 0.85em !important;
            }}
        }}

        @media (max-width: 480px) {{
            mjx-container[display="true"] {{
                font-size: 0.8em !important;
                max-width: 100% !important;
            }}

            mjx-container:not([display="true"]) {{
                font-size: 0.75em !important;
            }}
        }}
    </style>

    <!-- MathJax 配置 -->
    <script>
        window.MathJax = {{
            tex: {{
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true,
                packages: {{'[+]': ['mhchem', 'ams', 'newcommand']}},
                tags: 'ams',
                tagSide: 'right',
                tagIndent: '0.8em'
            }},
            loader: {{
                load: ['[tex]/mhchem', '[tex]/ams', '[tex]/newcommand']
            }},
            chtml: {{
                scale: 1.0,                    // 標準縮放比例
                minScale: 0.2,                 // 允許更小的縮放以適應長公式
                maxScale: 2.0,                 // 允許更大的縮放
                matchFontHeight: false,
                displayAlign: 'center',
                displayIndent: '0',
                adaptiveCSS: true,
                fontURL: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2'
            }},
            svg: {{
                scale: 1.0,
                minScale: 0.2,
                maxScale: 2.0,
                fontCache: 'global'
            }},
            options: {{
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
                ignoreHtmlClass: 'tex2jax_ignore',
                processHtmlClass: 'tex2jax_process'
            }},
            startup: {{
                ready() {{
                    MathJax.startup.defaultReady();

                    // 自動調整公式尺寸以適應容器
                    function adjustMathSize() {{
                        const mathElements = document.querySelectorAll('.MathJax, mjx-container');
                        mathElements.forEach(element => {{
                            const container = element.closest('.container, body');
                            const containerWidth = container.offsetWidth;
                            const elementWidth = element.scrollWidth;

                            if (elementWidth > containerWidth * 0.95) {{
                                const scale = Math.max((containerWidth * 0.9) / elementWidth, 0.3);
                                element.style.transform = `scale(${{scale}})`;
                                element.style.transformOrigin = 'center';
                                element.style.margin = '10px auto';
                                element.style.display = 'block';
                            }}
                        }});
                    }}

                    // 延遲執行調整
                    setTimeout(() => {{
                        adjustMathSize();
                        // 監聽窗口大小變化
                        window.addEventListener('resize', () => {{
                            setTimeout(adjustMathSize, 100);
                        }});
                    }}, 1000);
                }}
            }}
        }};
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
    </script>
</head>
<body>
    <div class="container">
        <div class="processing-info">
            📄 此文檔由教育材料轉換系統自動處理生成 | 處理器版本: 2.1.0
        </div>
        {content}
    </div>
</body>
</html>"""


def preprocess_html_for_pdf(content: str, use_base64_fallback: bool = True) -> str:
    """
    完整的HTML預處理功能 - 方案五的核心實現

    這個函數實現了完整的HTML預處理，解決PDF轉換中的所有資源載入問題：
    1. 圖片路徑修復（相對路徑 -> 絕對路徑 -> Base64備選）
    2. CSS資源處理
    3. 字體資源處理
    4. 其他媒體資源處理

    Args:
        content: 原始HTML內容
        use_base64_fallback: 是否使用Base64作為備選方案

    Returns:
        完全預處理後的HTML內容，確保在PDF轉換中所有資源都能正確載入
    """
    print("🔄 開始HTML預處理（方案五：完整預處理）...")

    # 第一步：修復圖片路徑
    content = fix_image_paths_for_pdf(content)

    # 第二步：如果啟用Base64備選，將小圖片轉換為Base64
    if use_base64_fallback:
        content = convert_small_images_to_base64(content)

    # 第三步：處理其他資源（CSS、字體等）
    content = fix_other_resources_for_pdf(content)

    print("✅ HTML預處理完成")
    return content


def convert_small_images_to_base64(content: str, max_size_kb: int = 100) -> str:
    """
    將小於指定大小的圖片轉換為Base64編碼

    Args:
        content: HTML內容
        max_size_kb: 最大檔案大小（KB），超過此大小的圖片不轉換

    Returns:
        轉換後的HTML內容
    """
    file_pattern = r'src="(file://[^"]+\.(?:png|jpg|jpeg|gif|svg|webp|bmp))"'

    def convert_if_small(match):
        file_url = match.group(1)
        file_path = Path(file_url.replace('file://', ''))

        try:
            if file_path.exists():
                file_size_kb = file_path.stat().st_size / 1024
                if file_size_kb <= max_size_kb:
                    base64_data = convert_image_to_base64(file_path)
                    if base64_data:
                        print(f"🔄 轉換為Base64: {file_path.name} ({file_size_kb:.1f}KB)")
                        return f'src="{base64_data}"'
                else:
                    print(f"ℹ️  保持檔案路徑: {file_path.name} ({file_size_kb:.1f}KB > {max_size_kb}KB)")
        except Exception as e:
            print(f"⚠️  檢查檔案大小失敗: {file_path}, {e}")

        return match.group(0)  # 保持原樣

    return re.sub(file_pattern, convert_if_small, content, flags=re.IGNORECASE)


def fix_other_resources_for_pdf(content: str) -> str:
    """
    修復HTML中的其他資源路徑（CSS、字體等）

    Args:
        content: HTML內容

    Returns:
        修復後的HTML內容
    """
    # 這裡可以添加CSS、字體等其他資源的路徑修復邏輯
    # 目前主要處理圖片，未來可以擴展

    # 示例：修復CSS中的背景圖片路徑
    css_bg_pattern = r'background-image:\s*url\(["\']?([^"\']+)["\']?\)'

    def fix_css_background(match):
        bg_path = match.group(1)
        if bg_path.startswith('../') or not bg_path.startswith(('http', 'data:', 'file:')):
            # 這裡可以添加CSS背景圖片的路徑修復邏輯
            print(f"🔍 發現CSS背景圖片: {bg_path}")
        return match.group(0)

    content = re.sub(css_bg_pattern, fix_css_background, content, flags=re.IGNORECASE)

    return content


def create_pdf_optimized_html(content: str, title: str) -> str:
    """創建PDF優化的HTML文檔（無背景、最大化空間利用）"""
    # 使用完整的HTML預處理功能（方案五）
    content = preprocess_html_for_pdf(content, use_base64_fallback=True)

    return f"""<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        /* PDF優化樣式 - 最大化A4頁面空間利用 */
        body {{
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 15px;
            background: transparent;
            color: #000;
            font-size: 14px;
        }}

        /* 移除所有裝飾性容器 */
        .container {{
            background: transparent;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
            border-radius: 0;
        }}

        /* 隱藏處理信息 */
        .processing-info {{
            display: none;
        }}

        /* 標題樣式優化 */
        h1, h2, h3, h4, h5, h6 {{
            color: #000;
            margin: 1em 0 0.5em 0;
            page-break-after: avoid;
        }}

        h1 {{
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
            font-size: 1.8em;
        }}

        h2 {{
            font-size: 1.5em;
            border-bottom: 1px solid #666;
            padding-bottom: 3px;
        }}

        h3 {{
            font-size: 1.3em;
        }}

        /* 填空線樣式 - PDF優化 */
        .fill-blank-static {{
            display: inline-block;
            border-bottom: 1px solid #000;
            margin: 0 2px;
            height: 1.2em;
            vertical-align: baseline;
            min-height: 1em;
        }}

        /* 水平線樣式 - PDF優化 */
        hr {{
            border: none;
            border-top: 1px solid #000;
            margin: 1.5em 0;
            height: 0;
            clear: both;
            page-break-inside: avoid;
        }}

        /* 列表樣式優化 */
        ul, ol {{
            padding-left: 20px;
            margin: 0.5em 0;
        }}

        li {{
            margin-bottom: 0.3em;
            page-break-inside: avoid;
        }}

        /* 隱藏包含填空線或水平線的列表項目符號 */
        li:has(.fill-blank-static),
        li:has(hr) {{
            list-style: none;
            margin-left: -20px;
            padding-left: 0;
        }}

        /* 表格樣式 - PDF優化 */
        .enhanced-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
            page-break-inside: avoid;
        }}

        .enhanced-table th,
        .enhanced-table td {{
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            font-size: 0.9em;
        }}

        .enhanced-table th {{
            background-color: #f0f0f0;
            font-weight: bold;
        }}

        /* 移除表格斑馬紋 */
        .enhanced-table tr:nth-child(even) {{
            background-color: transparent;
        }}

        /* 圖片樣式 - PDF優化 */
        img {{
            max-width: 100%;
            height: auto;
            border: none;
            box-shadow: none;
            border-radius: 0;
            page-break-inside: avoid;
        }}

        /* 代碼樣式 - PDF優化 */
        code {{
            background-color: #f5f5f5;
            padding: 1px 3px;
            border: 1px solid #ddd;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }}

        pre {{
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            padding: 10px;
            overflow: visible;
            font-size: 0.85em;
            page-break-inside: avoid;
        }}

        /* 鏈接樣式 */
        a {{
            color: #000;
            text-decoration: underline;
        }}

        /* 段落間距優化 */
        p {{
            margin: 0.5em 0;
            text-align: justify;
        }}

        /* 引用樣式 - 簡化 */
        blockquote {{
            border-left: 2px solid #666;
            margin: 1em 0;
            padding-left: 10px;
            font-style: normal;
            background: transparent;
        }}

        /* 練習區塊 - 簡化 */
        .exercise-block {{
            background: transparent;
            border: none;
            padding: 0;
            margin: 1em 0;
        }}

        .exercise-header {{
            color: #000;
            font-weight: bold;
            margin: 0.5em 0;
        }}

        /* MathJax 公式 - PDF優化 */
        .MathJax_Display, mjx-container[display="true"] {{
            margin: 0.8em auto !important;
            text-align: center !important;
            page-break-inside: avoid !important;
            font-size: 1em !important;
        }}

        .MathJax, mjx-container {{
            font-size: 1em !important;
            line-height: 1.2 !important;
        }}

        mjx-container:not([display="true"]) {{
            display: inline !important;
            vertical-align: middle !important;
            margin: 0 0.1em !important;
        }}

        /* 分頁控制 */
        @page {{
            size: A4;
            margin: 1.5cm 1.5cm 2cm 1.5cm;
        }}

        /* 避免孤行和寡行 */
        p, li {{
            orphans: 2;
            widows: 2;
        }}

        /* 標題不要單獨在頁面底部 */
        h1, h2, h3, h4, h5, h6 {{
            page-break-after: avoid;
            orphans: 3;
            widows: 3;
        }}
    </style>

    <!-- MathJax 配置 - PDF優化版 -->
    <script>
        window.MathJax = {{
            tex: {{
                inlineMath: [['$', '$'], ['\\(', '\\)']],
                displayMath: [['$$', '$$'], ['\\[', '\\]']],
                processEscapes: true,
                processEnvironments: true,
                packages: {{'[+]': ['mhchem', 'ams', 'newcommand']}},
                tags: 'none'  // PDF中不顯示公式編號
            }},
            loader: {{
                load: ['[tex]/mhchem', '[tex]/ams', '[tex]/newcommand']
            }},
            chtml: {{
                scale: 1.0,
                minScale: 0.5,
                maxScale: 1.5,
                matchFontHeight: false,
                displayAlign: 'center',
                displayIndent: '0',
                adaptiveCSS: false,  // PDF中禁用自適應CSS
                fontURL: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2'
            }},
            options: {{
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
                ignoreHtmlClass: 'tex2jax_ignore',
                processHtmlClass: 'tex2jax_process'
            }},
            startup: {{
                ready() {{
                    MathJax.startup.defaultReady();
                    // PDF模式下不需要動態調整
                }}
            }}
        }};
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
    </script>
</head>
<body>
    {content}
</body>
</html>"""


def main():
    """主函數"""
    print("🎓 教育材料轉換系統 - Markdown處理器示例")
    print("=" * 50)
    
    # 設置路徑
    current_dir = Path(__file__).parent
    test_files_dir = current_dir.parent.parent / "TestFile" / "Lesson MD"
    output_dir = current_dir / "output"
    
    # 檢查測試檔案目錄
    if not test_files_dir.exists():
        print(f"❌ 測試檔案目錄不存在: {test_files_dir}")
        print("請確保TestFile/Lesson MD目錄存在並包含課程檔案。")
        return
    
    # 處理指定檔案
    target_files = [
        ("Lesson3_Student.md", test_files_dir),  # 在Lesson MD目錄中
        ("test_complex_math.md", test_files_dir.parent)  # 在TestFile目錄中
    ]

    lesson_files = []
    for filename, directory in target_files:
        file_path = directory / filename
        if file_path.exists():
            lesson_files.append(file_path)
        else:
            print(f"⚠️  檔案不存在: {file_path}")

    if not lesson_files:
        print(f"❌ 沒有找到任何指定檔案")
        return

    print(f"📁 找到 {len(lesson_files)} 個指定檔案:")
    for file in lesson_files:
        print(f"   - {file.name} ({file.parent.name})")
    
    # 創建輸出目錄
    output_dir.mkdir(exist_ok=True)
    
    # 處理檔案 - 生成兩種模式的HTML
    success_count = 0
    total_count = len(lesson_files) * 2  # 每個檔案生成兩種模式

    for lesson_file in lesson_files:
        # 生成網頁版HTML
        if process_lesson_file(lesson_file, output_dir, 'web'):
            success_count += 1

        # 生成PDF版HTML
        if process_lesson_file(lesson_file, output_dir, 'pdf'):
            success_count += 1

    # 顯示總結
    print("\n" + "=" * 50)
    print(f"📊 處理完成: {success_count}/{total_count} 個檔案成功處理")
    print(f"   - 網頁版HTML: {success_count // 2} 個")
    print(f"   - PDF版HTML: {success_count // 2} 個")

    if success_count > 0:
        print(f"✅ HTML檔案已保存到: {output_dir}")
        print("\n💡 提示:")
        print("   - 網頁版HTML: 適合瀏覽器查看，包含完整視覺效果")
        print("   - PDF版HTML: 適合PDF轉換，最大化頁面空間利用")

    if success_count < total_count:
        print(f"⚠️  有 {total_count - success_count} 個檔案處理失敗")


# PDF 轉換功能已移除，將使用新的 Playwright 系統重新實現




if __name__ == '__main__':
    main()
