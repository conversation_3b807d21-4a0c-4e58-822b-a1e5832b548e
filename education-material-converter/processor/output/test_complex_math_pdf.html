<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>複雜數學公式測試檔案</title>
    <style>
        /* PDF優化樣式 - 最大化A4頁面空間利用 */
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 15px;
            background: transparent;
            color: #000;
            font-size: 14px;
        }

        /* 移除所有裝飾性容器 */
        .container {
            background: transparent;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
            border-radius: 0;
        }

        /* 隱藏處理信息 */
        .processing-info {
            display: none;
        }

        /* 標題樣式優化 */
        h1, h2, h3, h4, h5, h6 {
            color: #000;
            margin: 1em 0 0.5em 0;
            page-break-after: avoid;
        }

        h1 {
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
            font-size: 1.8em;
        }

        h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #666;
            padding-bottom: 3px;
        }

        h3 {
            font-size: 1.3em;
        }

        /* 填空線樣式 - PDF優化 */
        .fill-blank-static {
            display: inline-block;
            border-bottom: 1px solid #000;
            margin: 0 2px;
            height: 1.2em;
            vertical-align: baseline;
            min-height: 1em;
        }

        /* 水平線樣式 - PDF優化 */
        hr {
            border: none;
            border-top: 1px solid #000;
            margin: 1.5em 0;
            height: 0;
            clear: both;
            page-break-inside: avoid;
        }

        /* 列表樣式優化 */
        ul, ol {
            padding-left: 20px;
            margin: 0.5em 0;
        }

        li {
            margin-bottom: 0.3em;
            page-break-inside: avoid;
        }

        /* 隱藏包含填空線或水平線的列表項目符號 */
        li:has(.fill-blank-static),
        li:has(hr) {
            list-style: none;
            margin-left: -20px;
            padding-left: 0;
        }

        /* 表格樣式 - PDF優化 */
        .enhanced-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        .enhanced-table th,
        .enhanced-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            font-size: 0.9em;
        }

        .enhanced-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        /* 移除表格斑馬紋 */
        .enhanced-table tr:nth-child(even) {
            background-color: transparent;
        }

        /* 圖片樣式 - PDF優化 */
        img {
            max-width: 100%;
            height: auto;
            border: none;
            box-shadow: none;
            border-radius: 0;
            page-break-inside: avoid;
        }

        /* 代碼樣式 - PDF優化 */
        code {
            background-color: #f5f5f5;
            padding: 1px 3px;
            border: 1px solid #ddd;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        pre {
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            padding: 10px;
            overflow: visible;
            font-size: 0.85em;
            page-break-inside: avoid;
        }

        /* 鏈接樣式 */
        a {
            color: #000;
            text-decoration: underline;
        }

        /* 段落間距優化 */
        p {
            margin: 0.5em 0;
            text-align: justify;
        }

        /* 引用樣式 - 簡化 */
        blockquote {
            border-left: 2px solid #666;
            margin: 1em 0;
            padding-left: 10px;
            font-style: normal;
            background: transparent;
        }

        /* 練習區塊 - 簡化 */
        .exercise-block {
            background: transparent;
            border: none;
            padding: 0;
            margin: 1em 0;
        }

        .exercise-header {
            color: #000;
            font-weight: bold;
            margin: 0.5em 0;
        }

        /* MathJax 公式 - PDF優化 */
        .MathJax_Display, mjx-container[display="true"] {
            margin: 0.8em auto !important;
            text-align: center !important;
            page-break-inside: avoid !important;
            font-size: 1em !important;
        }

        .MathJax, mjx-container {
            font-size: 1em !important;
            line-height: 1.2 !important;
        }

        mjx-container:not([display="true"]) {
            display: inline !important;
            vertical-align: middle !important;
            margin: 0 0.1em !important;
        }

        /* 分頁控制 */
        @page {
            size: A4;
            margin: 1.5cm 1.5cm 2cm 1.5cm;
        }

        /* 避免孤行和寡行 */
        p, li {
            orphans: 2;
            widows: 2;
        }

        /* 標題不要單獨在頁面底部 */
        h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
            orphans: 3;
            widows: 3;
        }
    </style>

    <!-- MathJax 配置 - PDF優化版 -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\(', '\)']],
                displayMath: [['$$', '$$'], ['\[', '\]']],
                processEscapes: true,
                processEnvironments: true,
                packages: {'[+]': ['mhchem', 'ams', 'newcommand']},
                tags: 'none'  // PDF中不顯示公式編號
            },
            loader: {
                load: ['[tex]/mhchem', '[tex]/ams', '[tex]/newcommand']
            },
            chtml: {
                scale: 1.0,
                minScale: 0.5,
                maxScale: 1.5,
                matchFontHeight: false,
                displayAlign: 'center',
                displayIndent: '0',
                adaptiveCSS: false,  // PDF中禁用自適應CSS
                fontURL: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2'
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
                ignoreHtmlClass: 'tex2jax_ignore',
                processHtmlClass: 'tex2jax_process'
            },
            startup: {
                ready() {
                    MathJax.startup.defaultReady();
                    // PDF模式下不需要動態調整
                }
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
    </script>
</head>
<body>
    <h1>複雜數學公式測試檔案</h1>
<p>本檔案用於測試MathJax對極度複雜數學公式的渲染能力。</p>
<h2>1. 基本行內公式測試</h2>
<p>這是一個簡單的行內公式：$E = mc^2$，以及更複雜的：$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$。</p>
<p>複雜的行內公式：$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$ 和 $\lim_{x \to 0} \frac{\sin x}{x} = 1$。</p>
<h2>2. 微積分複雜公式</h2>
<h3>2.1 多重積分（高斯散度定理）</h3>
<p>$$\iiint_V \nabla \cdot \mathbf{F} , dV = \iint_{\partial V} \mathbf{F} \cdot \mathbf{n} , dS$$</p>
<h3>2.2 複雜的積分變換</h3>
<p>拉普拉斯變換：
$$\mathcal{L}{f(t)} = F(s) = \int_0^{\infty} f(t) e^{-st} dt$$</p>
<p>傅立葉變換：
$$\mathcal{F}{f(x)} = \hat{f}(\xi) = \int_{-\infty}^{\infty} f(x) e^{-2\pi i x \xi} dx$$</p>
<h3>2.3 極度複雜的積分</h3>
<p>$$\int_0^{\infty} \frac{\sin^3(x) \cos^2(x)}{x^2 + a^2} e^{-bx} dx = \frac{\pi}{8a} \left(1 - e^{-ab}\right) \left(3 + e^{-ab}\right)$$</p>
<h2>3. 線性代數複雜公式</h2>
<h3>3.1 矩陣指數</h3>
<p>$$e^{\mathbf{A}t} = \sum_{n=0}^{\infty} \frac{(\mathbf{A}t)^n}{n!} = \mathbf{I} + \mathbf{A}t + \frac{(\mathbf{A}t)^2}{2!} + \frac{(\mathbf{A}t)^3}{3!} + \cdots$$</p>
<h3>3.2 複雜矩陣運算</h3>
<p>$$\begin{vmatrix}
a_{11} &amp; a_{12} &amp; \cdots &amp; a_{1n} \
a_{21} &amp; a_{22} &amp; \cdots &amp; a_{2n} \
\vdots &amp; \vdots &amp; \ddots &amp; \vdots \
a_{n1} &amp; a_{n2} &amp; \cdots &amp; a_{nn}
\end{vmatrix} = \sum_{\sigma \in S_n} \text{sgn}(\sigma) \prod_{i=1}^n a_{i,\sigma(i)}$$</p>
<h3>3.3 特徵值分解</h3>
<p>$$\mathbf{A} = \mathbf{Q} \boldsymbol{\Lambda} \mathbf{Q}^{-1} = \sum_{i=1}^n \lambda_i \mathbf{q}_i \mathbf{q}_i^T$$</p>
<h2>4. 統計學複雜公式</h2>
<h3>4.1 多元正態分佈</h3>
<p>$$f(\mathbf{x}) = \frac{1}{(2\pi)^{k/2}|\boldsymbol{\Sigma}|^{1/2}} \exp\left(-\frac{1}{2}(\mathbf{x}-\boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{x}-\boldsymbol{\mu})\right)$$</p>
<h3>4.2 貝葉斯定理的複雜形式</h3>
<p>$$P(\theta|\mathbf{x}) = \frac{P(\mathbf{x}|\theta)P(\theta)}{\int_{\Theta} P(\mathbf{x}|\theta')P(\theta') d\theta'} = \frac{\mathcal{L}(\theta|\mathbf{x})\pi(\theta)}{m(\mathbf{x})}$$</p>
<h3>4.3 最大似然估計</h3>
<p>$$\hat{\theta}<em>{MLE} = \underset{\theta}{\arg\max} \mathcal{L}(\theta|\mathbf{x}) = \underset{\theta}{\arg\max} \prod</em>{i=1}^n f(x_i|\theta)$$</p>
<h2>5. 量子力學複雜公式</h2>
<h3>5.1 薛丁格方程</h3>
<p>$$i\hbar\frac{\partial}{\partial t}\Psi(\mathbf{r},t) = \hat{H}\Psi(\mathbf{r},t) = \left[-\frac{\hbar^2}{2m}\nabla^2 + V(\mathbf{r},t)\right]\Psi(\mathbf{r},t)$$</p>
<h3>5.2 量子場論</h3>
<p>$$\mathcal{L} = \bar{\psi}(i\gamma^\mu D_\mu - m)\psi - \frac{1}{4}F_{\mu\nu}F^{\mu\nu}$$</p>
<p>其中 $D_\mu = \partial_\mu + ieA_\mu$ 和 $F_{\mu\nu} = \partial_\mu A_\nu - \partial_\nu A_\mu$。</p>
<h3>5.3 路徑積分</h3>
<p>$$\langle q_f, t_f | q_i, t_i \rangle = \int \mathcal{D}[q(t)] \exp\left(\frac{i}{\hbar}\int_{t_i}^{t_f} L[q(t), \dot{q}(t), t] dt\right)$$</p>
<h2>6. 極度複雜的組合公式</h2>
<h3>6.1 拉馬努金的無窮級數</h3>
<p>$$\frac{1}{\pi} = \frac{2\sqrt{2}}{9801} \sum_{k=0}^{\infty} \frac{(4k)!(1103+26390k)}{(k!)^4 396^{4k}}$$</p>
<h3>6.2 黎曼ζ函數</h3>
<p>$$\zeta(s) = \sum_{n=1}^{\infty} \frac{1}{n^s} = \prod_{p \text{ prime}} \frac{1}{1-p^{-s}} = \frac{1}{\Gamma(s)} \int_0^{\infty} \frac{t^{s-1}}{e^t - 1} dt$$</p>
<h3>6.3 歐拉恆等式的推廣</h3>
<p>$$e^{i\theta} = \cos\theta + i\sin\theta = \sum_{n=0}^{\infty} \frac{(i\theta)^n}{n!}$$</p>
<h2>7. 超複雜的物理公式</h2>
<h3>7.1 愛因斯坦場方程</h3>
<p>$$G_{\mu\nu} + \Lambda g_{\mu\nu} = \frac{8\pi G}{c^4} T_{\mu\nu}$$</p>
<p>其中 $G_{\mu\nu} = R_{\mu\nu} - \frac{1}{2}g_{\mu\nu}R$。</p>
<h3>7.2 麥克斯韋方程組（張量形式）</h3>
<p>$$\partial_\mu F^{\mu\nu} = \mu_0 J^\nu$$
$$\partial_{[\mu} F_{\nu\rho]} = 0$$</p>
<h3>7.3 標準模型拉格朗日量（簡化版）</h3>
<p>$$\mathcal{L}<em>{SM} = \mathcal{L}</em>{\text{gauge}} + \mathcal{L}<em>{\text{fermion}} + \mathcal{L}</em>{\text{Higgs}} + \mathcal{L}_{\text{Yukawa}}$$</p>
<h2>8. 極限和級數的複雜形式</h2>
<h3>8.1 複雜極限</h3>
<p>$$\lim_{n \to \infty} \left(1 + \frac{x}{n}\right)^n = e^x$$</p>
<p>$$\lim_{x \to 0} \frac{\sin x - x + \frac{x^3}{6} - \frac{x^5}{120}}{x^7} = \frac{1}{5040}$$</p>
<h3>8.2 無窮乘積</h3>
<p>$$\prod_{n=1}^{\infty} \left(1 + \frac{x^2}{n^2\pi^2}\right) = \frac{\sinh x}{x}$$</p>
<h3>8.3 連分數</h3>
<p>$$e = 2 + \cfrac{1}{1 + \cfrac{1}{2 + \cfrac{1}{1 + \cfrac{1}{1 + \cfrac{1}{4 + \cfrac{1}{1 + \cfrac{1}{1 + \cfrac{1}{6 + \ddots}}}}}}}}$$</p>
<h2>9. 分段函數和條件表達式</h2>
<h3>9.1 複雜分段函數</h3>
<p>$$f(x) = \begin{cases}
\frac{\sin(\pi x)}{x} &amp; \text{if } x \neq 0 \
\pi &amp; \text{if } x = 0 \
\int_0^x e^{-t^2} dt &amp; \text{if } x &gt; 1 \
\sum_{n=0}^{\infty} \frac{(-1)^n x^{2n+1}}{(2n+1)!} &amp; \text{if } |x| \leq 1
\end{cases}$$</p>
<h3>9.2 狄拉克δ函數</h3>
<p>$$\delta(x) = \lim_{\epsilon \to 0} \frac{1}{\sqrt{2\pi\epsilon^2}} e^{-\frac{x^2}{2\epsilon^2}} = \lim_{n \to \infty} \frac{n}{\pi} \frac{1}{1 + n^2x^2}$$</p>
<h2>10. 超級複雜的組合公式</h2>
<h3>10.1 斯特林數和貝爾數</h3>
<p>$$B_n = \sum_{k=0}^n S(n,k) = \frac{1}{e} \sum_{k=0}^{\infty} \frac{k^n}{k!}$$</p>
<p>其中 $S(n,k)$ 是第二類斯特林數。</p>
<h3>10.2 卡塔蘭數的生成函數</h3>
<p>$$C(x) = \sum_{n=0}^{\infty} C_n x^n = \frac{1 - \sqrt{1-4x}}{2x} = \frac{1}{1-x-\cfrac{x}{1-x-\cfrac{2x}{1-x-\cfrac{3x}{1-x-\ddots}}}}$$</p>
<h2>11. 複雜化學公式測試</h2>
<h3>11.1 基本化學反應</h3>
<p>$$\ce{2H2 + O2 -&gt; 2H2O}$$</p>
<p>$$\ce{CaCO3 + 2HCl -&gt; CaCl2 + H2O + CO2 ^}$$</p>
<h3>11.2 複雜有機化學反應</h3>
<p>$$\ce{C6H5-CHO + HCN -&gt;[OH-] C6H5-CH(OH)-CN}$$</p>
<p>$$\ce{CH3-CO-CH3 + NH2OH -&gt;[H+] CH3-C(=NOH)-CH3 + H2O}$$</p>
<h3>11.3 生化反應</h3>
<p>葡萄糖代謝：
$$\ce{C6H12O6 + 6O2 -&gt; 6CO2 + 6H2O + ATP}$$</p>
<p>蛋白質合成：
$$\ce{amino acid + tRNA -&gt;[aminoacyl-tRNA synthetase] aminoacyl-tRNA + AMP + PPi}$$</p>
<h3>11.4 配位化合物</h3>
<p>$$\ce{[Cu(NH3)4]^2+ + 4H2O &lt;=&gt; [Cu(H2O)4]^2+ + 4NH3}$$</p>
<p>$$\ce{[Fe(CN)6]^3- + e- &lt;=&gt; [Fe(CN)6]^4-}$$</p>
<h3>11.5 電化學反應</h3>
<p>$$\ce{Zn^2+ + 2e- -&gt; Zn} \quad E° = -0.76 \text{ V}$$</p>
<p>$$\ce{2H+ + 2e- -&gt; H2} \quad E° = 0.00 \text{ V}$$</p>
<h3>11.6 複雜的有機合成</h3>
<p>$$\ce{R-CH=CH2 + HBr -&gt;[peroxides] R-CH2-CH2Br}$$</p>
<p>$$\ce{R-C#C-H + Na+ NH2- -&gt;[NH3(l)] R-C#C- Na+ + NH3}$$</p>
<h3>11.7 核化學反應</h3>
<p>$$\ce{^{235}U + ^1n -&gt; ^{141}Ba + ^{92}Kr + 3^1n + energy}$$</p>
<p>$$\ce{^{14}C -&gt; ^{14}N + e- + \bar{\nu}<em>e} \quad t</em>{1/2} = 5730 \text{ years}$$</p>
<h3>11.8 複雜的無機化學</h3>
<p>$$\ce{K2Cr2O7 + 14HCl -&gt; 2KCl + 2CrCl3 + 3Cl2 + 7H2O}$$</p>
<p>$$\ce{MnO4- + 8H+ + 5e- -&gt; Mn^2+ + 4H2O}$$</p>
<h3>11.9 聚合反應</h3>
<p>$$\ce{n CH2=CH2 -&gt;[catalyst] -(CH2-CH2)_n-}$$</p>
<p>$$\ce{H2N-(CH2)6-NH2 + HOOC-(CH2)4-COOH -&gt; [-NH-(CH2)6-NH-CO-(CH2)4-CO-]_n + nH2O}$$</p>
<h3>11.10 複雜的生物化學路徑</h3>
<p>檸檬酸循環中的一步：
$$\ce{Isocitrate + NAD+ -&gt;[isocitrate dehydrogenase][Mg^2+] \alpha-Ketoglutarate + NADH + H+ + CO2}$$</p>
<p>ATP水解：
$$\ce{ATP + H2O -&gt;[ATPase] ADP + Pi + energy}$$</p>
<h3>11.11 物態變化化學公式</h3>
<h4>11.11.1 基本物態變化</h4>
<p>水的三態變化：
$$\ce{H2O(s) &lt;=&gt;[\Delta H_{fus}][\Delta] H2O(l) &lt;=&gt;[\Delta H_{vap}][\Delta] H2O(g)}$$</p>
<p>昇華過程：
$$\ce{CO2(s) -&gt;[昇華] CO2(g)} \quad \Delta H_{sub} = 25.2 \text{ kJ/mol}$$</p>
<h4>11.11.2 相平衡方程</h4>
<p>克拉佩龍方程：
$$\frac{dP}{dT} = \frac{\Delta H}{T \Delta V}$$</p>
<p>對於液-氣平衡：
$$\ln P = -\frac{\Delta H_{vap}}{RT} + C$$</p>
<h4>11.11.3 溶解度與物態</h4>
<p>鹽類的溶解平衡：
$$\ce{NaCl(s) &lt;=&gt; Na+(aq) + Cl-(aq)} \quad K_{sp} = [\ce{Na+}][\ce{Cl-}]$$</p>
<p>氣體溶解度（亨利定律）：
$$\ce{O2(g) &lt;=&gt; O2(aq)} \quad C = k_H \cdot P$$</p>
<h4>11.11.4 複雜的相變過程</h4>
<p>多晶型轉變：
$$\ce{CaCO3(\alpha-calcite) &lt;=&gt;[\Delta T, P] CaCO3(\beta-aragonite)}$$</p>
<p>水合物形成：
$$\ce{CuSO4(s) + 5H2O(l) &lt;=&gt; CuSO4 * 5H2O(s)} \quad \Delta H &lt; 0$$</p>
<h4>11.11.5 臨界現象</h4>
<p>臨界點附近的狀態方程：
$$\ce{H2O(l) &lt;=&gt;[\text{臨界點}][T_c = 647K, P_c = 221 bar] H2O(g)}$$</p>
<p>超臨界流體：
$$\ce{CO2(l) -&gt;[T &gt; T_c, P &gt; P_c] CO2(\text{超臨界})}$$</p>
<h4>11.11.6 固態相變</h4>
<p>金屬的同素異形體轉變：
$$\ce{Fe(\alpha-\text{體心立方}) &lt;=&gt;[\Delta T = 912°C] Fe(\gamma-\text{面心立方})}$$</p>
<p>陶瓷材料的相變：
$$\ce{SiO2(\alpha-\text{石英}) &lt;=&gt;[\Delta T = 573°C] SiO2(\beta-\text{石英})}$$</p>
<h4>11.11.7 複雜的多相系統</h4>
<p>三相點：
$$\ce{H2O(s) &lt;=&gt; H2O(l) &lt;=&gt; H2O(g)} \quad T = 273.16K, P = 611.657Pa$$</p>
<p>共晶系統：
$$\ce{Pb(l) + Sn(l) &lt;=&gt;[\text{冷卻}] Pb(s) + Sn(s)} \quad T_{eutectic} = 183°C$$</p>
<h4>11.11.8 生物系統中的相變</h4>
<p>蛋白質變性：
$$\ce{Protein(\text{天然態}) &lt;=&gt;[\Delta T] Protein(\text{變性態})} \quad \Delta G = \Delta H - T\Delta S$$</p>
<p>脂質相變：
$$\ce{Lipid(\text{凝膠態}) &lt;=&gt;[\Delta T] Lipid(\text{液晶態})} \quad T_m = \text{相變溫度}$$</p>
<hr/>
<p><strong>測試說明</strong>：以上公式涵蓋了從基礎到極度複雜的各種數學和化學表達式，用於全面測試MathJax的渲染能力和兼容性。</p>

</body>
</html>