<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>複雜數學公式測試檔案</title>
    <style>
        body {
            font-family: 'Microsoft JhengHei', <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1, h2, h3, h4, h5, h6 {
            color: #333;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 {
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        
        .fill-blank-static {
            display: inline-block;
            border-bottom: 1px solid #333;
            margin: 0 2px;
            height: 1.2em;
            vertical-align: baseline;
            min-height: 1em;
        }

        /* 水平線樣式 - 用於書寫空間 */
        hr {
            border: none;
            border-top: 2px solid #333;
            margin: 2.5em 0;
            height: 0;
            clear: both;
        }

        /* 隱藏包含填空線或水平線的列表項目符號 */
        li:has(.fill-blank-static),
        li:has(hr) {
            list-style: none;
            margin-left: -30px;
            padding-left: 0;
        }

        /* 為PDF列印優化的線條樣式 */
        @media print {
            .fill-blank-static {
                border-bottom: 1px solid #000;
                page-break-inside: avoid;
                margin: 0 3px;
            }

            hr {
                border-top: 2px solid #000;
                margin: 3.5em 0;
                page-break-inside: avoid;
            }

            /* 填空線與水平線組合的特殊間距 */
            .fill-blank-static + ul li hr,
            li:has(.fill-blank-static) + li hr {
                margin: 4em 0;
            }

            /* 列印時也隱藏列表符號 */
            li:has(.fill-blank-static),
            li:has(hr) {
                list-style: none;
                margin-left: -30px;
                padding-left: 0;
            }
        }
        
        .exercise-block {
            /* 移除特殊樣式，改為普通文字格式 */
            background-color: transparent;
            border: none;
            padding: 0;
            margin: 20px 0;
            border-radius: 0;
        }

        .exercise-header {
            /* 移除特殊顏色，使用普通標題樣式 */
            color: #333;
            margin-top: 0;
        }
        
        .enhanced-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .enhanced-table th,
        .enhanced-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .enhanced-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .enhanced-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .empty-cell {
            background-color: #fff3cd;
            color: #856404;
            font-style: italic;
        }
        
        .table-wrapper {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        blockquote {
            /* 移除特殊樣式，改為普通文字格式 */
            border: none;
            margin: 20px 0;
            padding: 0;
            background-color: transparent;
            font-style: normal;
        }
        
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        
        pre {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
        }
        
        ul, ol {
            padding-left: 30px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        a {
            color: #007acc;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .processing-info {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 20px;
            font-size: 0.9em;
            color: #6c757d;
        }

        /* MathJax 公式容器優化 - 確保完整顯示 */
        .MathJax_Display, mjx-container[display="true"] {
            overflow: visible !important;
            max-width: 100% !important;
            width: auto !important;
            margin: 1em auto !important;
            padding: 0.5em 0 !important;
            text-align: center !important;
            box-sizing: border-box !important;
            transform-origin: center center !important;
        }

        .MathJax, mjx-container {
            max-width: 100% !important;
            overflow: visible !important;
            font-size: 1em !important;
            line-height: 1.3 !important;
        }

        /* 行內公式優化 */
        mjx-container:not([display="true"]) {
            display: inline-block !important;
            vertical-align: middle !important;
            margin: 0 0.1em !important;
            max-width: 100% !important;
        }

        /* 化學公式特殊處理 */
        mjx-container .mjx-chtml .mjx-mrow .mjx-mi[mathvariant="normal"] {
            font-family: 'Times New Roman', serif !important;
        }

        /* 確保長公式不會溢出 */
        mjx-container {
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
        }

        /* PDF打印優化 */
        @media print {
            .MathJax_Display, mjx-container[display="true"] {
                page-break-inside: avoid !important;
                margin: 0.8em auto !important;
                font-size: 0.9em !important;
            }

            .MathJax, mjx-container {
                font-size: 0.85em !important;
            }

            /* 確保長公式在PDF中可見 */
            mjx-container {
                max-width: 100% !important;
                overflow: visible !important;
                word-wrap: break-word !important;
            }
        }

        /* 響應式數學公式 */
        @media (max-width: 768px) {
            mjx-container[display="true"] {
                font-size: 0.9em !important;
                max-width: 98% !important;
            }

            mjx-container:not([display="true"]) {
                font-size: 0.85em !important;
            }
        }

        @media (max-width: 480px) {
            mjx-container[display="true"] {
                font-size: 0.8em !important;
                max-width: 100% !important;
            }

            mjx-container:not([display="true"]) {
                font-size: 0.75em !important;
            }
        }
    </style>

    <!-- MathJax 配置 -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\(', '\)']],
                displayMath: [['$$', '$$'], ['\[', '\]']],
                processEscapes: true,
                processEnvironments: true,
                packages: {'[+]': ['mhchem', 'ams', 'newcommand']},
                tags: 'ams',
                tagSide: 'right',
                tagIndent: '0.8em'
            },
            loader: {
                load: ['[tex]/mhchem', '[tex]/ams', '[tex]/newcommand']
            },
            chtml: {
                scale: 1.0,                    // 標準縮放比例
                minScale: 0.2,                 // 允許更小的縮放以適應長公式
                maxScale: 2.0,                 // 允許更大的縮放
                matchFontHeight: false,
                displayAlign: 'center',
                displayIndent: '0',
                adaptiveCSS: true,
                fontURL: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2'
            },
            svg: {
                scale: 1.0,
                minScale: 0.2,
                maxScale: 2.0,
                fontCache: 'global'
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
                ignoreHtmlClass: 'tex2jax_ignore',
                processHtmlClass: 'tex2jax_process'
            },
            startup: {
                ready() {
                    MathJax.startup.defaultReady();

                    // 自動調整公式尺寸以適應容器
                    function adjustMathSize() {
                        const mathElements = document.querySelectorAll('.MathJax, mjx-container');
                        mathElements.forEach(element => {
                            const container = element.closest('.container, body');
                            const containerWidth = container.offsetWidth;
                            const elementWidth = element.scrollWidth;

                            if (elementWidth > containerWidth * 0.95) {
                                const scale = Math.max((containerWidth * 0.9) / elementWidth, 0.3);
                                element.style.transform = `scale(${scale})`;
                                element.style.transformOrigin = 'center';
                                element.style.margin = '10px auto';
                                element.style.display = 'block';
                            }
                        });
                    }

                    // 延遲執行調整
                    setTimeout(() => {
                        adjustMathSize();
                        // 監聽窗口大小變化
                        window.addEventListener('resize', () => {
                            setTimeout(adjustMathSize, 100);
                        });
                    }, 1000);
                }
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
    </script>
</head>
<body>
    <div class="container">
        <div class="processing-info">
            📄 此文檔由教育材料轉換系統自動處理生成 | 處理器版本: 2.1.0
        </div>
        <h1>複雜數學公式測試檔案</h1>
<p>本檔案用於測試MathJax對極度複雜數學公式的渲染能力。</p>
<h2>1. 基本行內公式測試</h2>
<p>這是一個簡單的行內公式：$E = mc^2$，以及更複雜的：$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$。</p>
<p>複雜的行內公式：$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$ 和 $\lim_{x \to 0} \frac{\sin x}{x} = 1$。</p>
<h2>2. 微積分複雜公式</h2>
<h3>2.1 多重積分（高斯散度定理）</h3>
<p>$$\iiint_V \nabla \cdot \mathbf{F} , dV = \iint_{\partial V} \mathbf{F} \cdot \mathbf{n} , dS$$</p>
<h3>2.2 複雜的積分變換</h3>
<p>拉普拉斯變換：
$$\mathcal{L}{f(t)} = F(s) = \int_0^{\infty} f(t) e^{-st} dt$$</p>
<p>傅立葉變換：
$$\mathcal{F}{f(x)} = \hat{f}(\xi) = \int_{-\infty}^{\infty} f(x) e^{-2\pi i x \xi} dx$$</p>
<h3>2.3 極度複雜的積分</h3>
<p>$$\int_0^{\infty} \frac{\sin^3(x) \cos^2(x)}{x^2 + a^2} e^{-bx} dx = \frac{\pi}{8a} \left(1 - e^{-ab}\right) \left(3 + e^{-ab}\right)$$</p>
<h2>3. 線性代數複雜公式</h2>
<h3>3.1 矩陣指數</h3>
<p>$$e^{\mathbf{A}t} = \sum_{n=0}^{\infty} \frac{(\mathbf{A}t)^n}{n!} = \mathbf{I} + \mathbf{A}t + \frac{(\mathbf{A}t)^2}{2!} + \frac{(\mathbf{A}t)^3}{3!} + \cdots$$</p>
<h3>3.2 複雜矩陣運算</h3>
<p>$$\begin{vmatrix}
a_{11} &amp; a_{12} &amp; \cdots &amp; a_{1n} \
a_{21} &amp; a_{22} &amp; \cdots &amp; a_{2n} \
\vdots &amp; \vdots &amp; \ddots &amp; \vdots \
a_{n1} &amp; a_{n2} &amp; \cdots &amp; a_{nn}
\end{vmatrix} = \sum_{\sigma \in S_n} \text{sgn}(\sigma) \prod_{i=1}^n a_{i,\sigma(i)}$$</p>
<h3>3.3 特徵值分解</h3>
<p>$$\mathbf{A} = \mathbf{Q} \boldsymbol{\Lambda} \mathbf{Q}^{-1} = \sum_{i=1}^n \lambda_i \mathbf{q}_i \mathbf{q}_i^T$$</p>
<h2>4. 統計學複雜公式</h2>
<h3>4.1 多元正態分佈</h3>
<p>$$f(\mathbf{x}) = \frac{1}{(2\pi)^{k/2}|\boldsymbol{\Sigma}|^{1/2}} \exp\left(-\frac{1}{2}(\mathbf{x}-\boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{x}-\boldsymbol{\mu})\right)$$</p>
<h3>4.2 貝葉斯定理的複雜形式</h3>
<p>$$P(\theta|\mathbf{x}) = \frac{P(\mathbf{x}|\theta)P(\theta)}{\int_{\Theta} P(\mathbf{x}|\theta')P(\theta') d\theta'} = \frac{\mathcal{L}(\theta|\mathbf{x})\pi(\theta)}{m(\mathbf{x})}$$</p>
<h3>4.3 最大似然估計</h3>
<p>$$\hat{\theta}<em>{MLE} = \underset{\theta}{\arg\max} \mathcal{L}(\theta|\mathbf{x}) = \underset{\theta}{\arg\max} \prod</em>{i=1}^n f(x_i|\theta)$$</p>
<h2>5. 量子力學複雜公式</h2>
<h3>5.1 薛丁格方程</h3>
<p>$$i\hbar\frac{\partial}{\partial t}\Psi(\mathbf{r},t) = \hat{H}\Psi(\mathbf{r},t) = \left[-\frac{\hbar^2}{2m}\nabla^2 + V(\mathbf{r},t)\right]\Psi(\mathbf{r},t)$$</p>
<h3>5.2 量子場論</h3>
<p>$$\mathcal{L} = \bar{\psi}(i\gamma^\mu D_\mu - m)\psi - \frac{1}{4}F_{\mu\nu}F^{\mu\nu}$$</p>
<p>其中 $D_\mu = \partial_\mu + ieA_\mu$ 和 $F_{\mu\nu} = \partial_\mu A_\nu - \partial_\nu A_\mu$。</p>
<h3>5.3 路徑積分</h3>
<p>$$\langle q_f, t_f | q_i, t_i \rangle = \int \mathcal{D}[q(t)] \exp\left(\frac{i}{\hbar}\int_{t_i}^{t_f} L[q(t), \dot{q}(t), t] dt\right)$$</p>
<h2>6. 極度複雜的組合公式</h2>
<h3>6.1 拉馬努金的無窮級數</h3>
<p>$$\frac{1}{\pi} = \frac{2\sqrt{2}}{9801} \sum_{k=0}^{\infty} \frac{(4k)!(1103+26390k)}{(k!)^4 396^{4k}}$$</p>
<h3>6.2 黎曼ζ函數</h3>
<p>$$\zeta(s) = \sum_{n=1}^{\infty} \frac{1}{n^s} = \prod_{p \text{ prime}} \frac{1}{1-p^{-s}} = \frac{1}{\Gamma(s)} \int_0^{\infty} \frac{t^{s-1}}{e^t - 1} dt$$</p>
<h3>6.3 歐拉恆等式的推廣</h3>
<p>$$e^{i\theta} = \cos\theta + i\sin\theta = \sum_{n=0}^{\infty} \frac{(i\theta)^n}{n!}$$</p>
<h2>7. 超複雜的物理公式</h2>
<h3>7.1 愛因斯坦場方程</h3>
<p>$$G_{\mu\nu} + \Lambda g_{\mu\nu} = \frac{8\pi G}{c^4} T_{\mu\nu}$$</p>
<p>其中 $G_{\mu\nu} = R_{\mu\nu} - \frac{1}{2}g_{\mu\nu}R$。</p>
<h3>7.2 麥克斯韋方程組（張量形式）</h3>
<p>$$\partial_\mu F^{\mu\nu} = \mu_0 J^\nu$$
$$\partial_{[\mu} F_{\nu\rho]} = 0$$</p>
<h3>7.3 標準模型拉格朗日量（簡化版）</h3>
<p>$$\mathcal{L}<em>{SM} = \mathcal{L}</em>{\text{gauge}} + \mathcal{L}<em>{\text{fermion}} + \mathcal{L}</em>{\text{Higgs}} + \mathcal{L}_{\text{Yukawa}}$$</p>
<h2>8. 極限和級數的複雜形式</h2>
<h3>8.1 複雜極限</h3>
<p>$$\lim_{n \to \infty} \left(1 + \frac{x}{n}\right)^n = e^x$$</p>
<p>$$\lim_{x \to 0} \frac{\sin x - x + \frac{x^3}{6} - \frac{x^5}{120}}{x^7} = \frac{1}{5040}$$</p>
<h3>8.2 無窮乘積</h3>
<p>$$\prod_{n=1}^{\infty} \left(1 + \frac{x^2}{n^2\pi^2}\right) = \frac{\sinh x}{x}$$</p>
<h3>8.3 連分數</h3>
<p>$$e = 2 + \cfrac{1}{1 + \cfrac{1}{2 + \cfrac{1}{1 + \cfrac{1}{1 + \cfrac{1}{4 + \cfrac{1}{1 + \cfrac{1}{1 + \cfrac{1}{6 + \ddots}}}}}}}}$$</p>
<h2>9. 分段函數和條件表達式</h2>
<h3>9.1 複雜分段函數</h3>
<p>$$f(x) = \begin{cases}
\frac{\sin(\pi x)}{x} &amp; \text{if } x \neq 0 \
\pi &amp; \text{if } x = 0 \
\int_0^x e^{-t^2} dt &amp; \text{if } x &gt; 1 \
\sum_{n=0}^{\infty} \frac{(-1)^n x^{2n+1}}{(2n+1)!} &amp; \text{if } |x| \leq 1
\end{cases}$$</p>
<h3>9.2 狄拉克δ函數</h3>
<p>$$\delta(x) = \lim_{\epsilon \to 0} \frac{1}{\sqrt{2\pi\epsilon^2}} e^{-\frac{x^2}{2\epsilon^2}} = \lim_{n \to \infty} \frac{n}{\pi} \frac{1}{1 + n^2x^2}$$</p>
<h2>10. 超級複雜的組合公式</h2>
<h3>10.1 斯特林數和貝爾數</h3>
<p>$$B_n = \sum_{k=0}^n S(n,k) = \frac{1}{e} \sum_{k=0}^{\infty} \frac{k^n}{k!}$$</p>
<p>其中 $S(n,k)$ 是第二類斯特林數。</p>
<h3>10.2 卡塔蘭數的生成函數</h3>
<p>$$C(x) = \sum_{n=0}^{\infty} C_n x^n = \frac{1 - \sqrt{1-4x}}{2x} = \frac{1}{1-x-\cfrac{x}{1-x-\cfrac{2x}{1-x-\cfrac{3x}{1-x-\ddots}}}}$$</p>
<h2>11. 複雜化學公式測試</h2>
<h3>11.1 基本化學反應</h3>
<p>$$\ce{2H2 + O2 -&gt; 2H2O}$$</p>
<p>$$\ce{CaCO3 + 2HCl -&gt; CaCl2 + H2O + CO2 ^}$$</p>
<h3>11.2 複雜有機化學反應</h3>
<p>$$\ce{C6H5-CHO + HCN -&gt;[OH-] C6H5-CH(OH)-CN}$$</p>
<p>$$\ce{CH3-CO-CH3 + NH2OH -&gt;[H+] CH3-C(=NOH)-CH3 + H2O}$$</p>
<h3>11.3 生化反應</h3>
<p>葡萄糖代謝：
$$\ce{C6H12O6 + 6O2 -&gt; 6CO2 + 6H2O + ATP}$$</p>
<p>蛋白質合成：
$$\ce{amino acid + tRNA -&gt;[aminoacyl-tRNA synthetase] aminoacyl-tRNA + AMP + PPi}$$</p>
<h3>11.4 配位化合物</h3>
<p>$$\ce{[Cu(NH3)4]^2+ + 4H2O &lt;=&gt; [Cu(H2O)4]^2+ + 4NH3}$$</p>
<p>$$\ce{[Fe(CN)6]^3- + e- &lt;=&gt; [Fe(CN)6]^4-}$$</p>
<h3>11.5 電化學反應</h3>
<p>$$\ce{Zn^2+ + 2e- -&gt; Zn} \quad E° = -0.76 \text{ V}$$</p>
<p>$$\ce{2H+ + 2e- -&gt; H2} \quad E° = 0.00 \text{ V}$$</p>
<h3>11.6 複雜的有機合成</h3>
<p>$$\ce{R-CH=CH2 + HBr -&gt;[peroxides] R-CH2-CH2Br}$$</p>
<p>$$\ce{R-C#C-H + Na+ NH2- -&gt;[NH3(l)] R-C#C- Na+ + NH3}$$</p>
<h3>11.7 核化學反應</h3>
<p>$$\ce{^{235}U + ^1n -&gt; ^{141}Ba + ^{92}Kr + 3^1n + energy}$$</p>
<p>$$\ce{^{14}C -&gt; ^{14}N + e- + \bar{\nu}<em>e} \quad t</em>{1/2} = 5730 \text{ years}$$</p>
<h3>11.8 複雜的無機化學</h3>
<p>$$\ce{K2Cr2O7 + 14HCl -&gt; 2KCl + 2CrCl3 + 3Cl2 + 7H2O}$$</p>
<p>$$\ce{MnO4- + 8H+ + 5e- -&gt; Mn^2+ + 4H2O}$$</p>
<h3>11.9 聚合反應</h3>
<p>$$\ce{n CH2=CH2 -&gt;[catalyst] -(CH2-CH2)_n-}$$</p>
<p>$$\ce{H2N-(CH2)6-NH2 + HOOC-(CH2)4-COOH -&gt; [-NH-(CH2)6-NH-CO-(CH2)4-CO-]_n + nH2O}$$</p>
<h3>11.10 複雜的生物化學路徑</h3>
<p>檸檬酸循環中的一步：
$$\ce{Isocitrate + NAD+ -&gt;[isocitrate dehydrogenase][Mg^2+] \alpha-Ketoglutarate + NADH + H+ + CO2}$$</p>
<p>ATP水解：
$$\ce{ATP + H2O -&gt;[ATPase] ADP + Pi + energy}$$</p>
<h3>11.11 物態變化化學公式</h3>
<h4>11.11.1 基本物態變化</h4>
<p>水的三態變化：
$$\ce{H2O(s) &lt;=&gt;[\Delta H_{fus}][\Delta] H2O(l) &lt;=&gt;[\Delta H_{vap}][\Delta] H2O(g)}$$</p>
<p>昇華過程：
$$\ce{CO2(s) -&gt;[昇華] CO2(g)} \quad \Delta H_{sub} = 25.2 \text{ kJ/mol}$$</p>
<h4>11.11.2 相平衡方程</h4>
<p>克拉佩龍方程：
$$\frac{dP}{dT} = \frac{\Delta H}{T \Delta V}$$</p>
<p>對於液-氣平衡：
$$\ln P = -\frac{\Delta H_{vap}}{RT} + C$$</p>
<h4>11.11.3 溶解度與物態</h4>
<p>鹽類的溶解平衡：
$$\ce{NaCl(s) &lt;=&gt; Na+(aq) + Cl-(aq)} \quad K_{sp} = [\ce{Na+}][\ce{Cl-}]$$</p>
<p>氣體溶解度（亨利定律）：
$$\ce{O2(g) &lt;=&gt; O2(aq)} \quad C = k_H \cdot P$$</p>
<h4>11.11.4 複雜的相變過程</h4>
<p>多晶型轉變：
$$\ce{CaCO3(\alpha-calcite) &lt;=&gt;[\Delta T, P] CaCO3(\beta-aragonite)}$$</p>
<p>水合物形成：
$$\ce{CuSO4(s) + 5H2O(l) &lt;=&gt; CuSO4 * 5H2O(s)} \quad \Delta H &lt; 0$$</p>
<h4>11.11.5 臨界現象</h4>
<p>臨界點附近的狀態方程：
$$\ce{H2O(l) &lt;=&gt;[\text{臨界點}][T_c = 647K, P_c = 221 bar] H2O(g)}$$</p>
<p>超臨界流體：
$$\ce{CO2(l) -&gt;[T &gt; T_c, P &gt; P_c] CO2(\text{超臨界})}$$</p>
<h4>11.11.6 固態相變</h4>
<p>金屬的同素異形體轉變：
$$\ce{Fe(\alpha-\text{體心立方}) &lt;=&gt;[\Delta T = 912°C] Fe(\gamma-\text{面心立方})}$$</p>
<p>陶瓷材料的相變：
$$\ce{SiO2(\alpha-\text{石英}) &lt;=&gt;[\Delta T = 573°C] SiO2(\beta-\text{石英})}$$</p>
<h4>11.11.7 複雜的多相系統</h4>
<p>三相點：
$$\ce{H2O(s) &lt;=&gt; H2O(l) &lt;=&gt; H2O(g)} \quad T = 273.16K, P = 611.657Pa$$</p>
<p>共晶系統：
$$\ce{Pb(l) + Sn(l) &lt;=&gt;[\text{冷卻}] Pb(s) + Sn(s)} \quad T_{eutectic} = 183°C$$</p>
<h4>11.11.8 生物系統中的相變</h4>
<p>蛋白質變性：
$$\ce{Protein(\text{天然態}) &lt;=&gt;[\Delta T] Protein(\text{變性態})} \quad \Delta G = \Delta H - T\Delta S$$</p>
<p>脂質相變：
$$\ce{Lipid(\text{凝膠態}) &lt;=&gt;[\Delta T] Lipid(\text{液晶態})} \quad T_m = \text{相變溫度}$$</p>
<hr/>
<p><strong>測試說明</strong>：以上公式涵蓋了從基礎到極度複雜的各種數學和化學表達式，用於全面測試MathJax的渲染能力和兼容性。</p>

    </div>
</body>
</html>