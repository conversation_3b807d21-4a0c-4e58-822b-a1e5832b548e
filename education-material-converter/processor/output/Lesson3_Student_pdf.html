<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>第三課：看圖說故事 - 展現"觀察力"與"想像力"</title>
    <style>
        /* PDF優化樣式 - 最大化A4頁面空間利用 */
        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            line-height: 1.5;
            margin: 0;
            padding: 15px;
            background: transparent;
            color: #000;
            font-size: 14px;
        }

        /* 移除所有裝飾性容器 */
        .container {
            background: transparent;
            padding: 0;
            margin: 0;
            border: none;
            box-shadow: none;
            border-radius: 0;
        }

        /* 隱藏處理信息 */
        .processing-info {
            display: none;
        }

        /* 標題樣式優化 */
        h1, h2, h3, h4, h5, h6 {
            color: #000;
            margin: 1em 0 0.5em 0;
            page-break-after: avoid;
        }

        h1 {
            border-bottom: 2px solid #000;
            padding-bottom: 5px;
            font-size: 1.8em;
        }

        h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #666;
            padding-bottom: 3px;
        }

        h3 {
            font-size: 1.3em;
        }

        /* 填空線樣式 - PDF優化 */
        .fill-blank-static {
            display: inline-block;
            border-bottom: 1px solid #000;
            margin: 0 2px;
            height: 1.2em;
            vertical-align: baseline;
            min-height: 1em;
        }

        /* 水平線樣式 - PDF優化 */
        hr {
            border: none;
            border-top: 1px solid #000;
            margin: 1.5em 0;
            height: 0;
            clear: both;
            page-break-inside: avoid;
        }

        /* 列表樣式優化 */
        ul, ol {
            padding-left: 20px;
            margin: 0.5em 0;
        }

        li {
            margin-bottom: 0.3em;
            page-break-inside: avoid;
        }

        /* 隱藏包含填空線或水平線的列表項目符號 */
        li:has(.fill-blank-static),
        li:has(hr) {
            list-style: none;
            margin-left: -20px;
            padding-left: 0;
        }

        /* 表格樣式 - PDF優化 */
        .enhanced-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        .enhanced-table th,
        .enhanced-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
            font-size: 0.9em;
        }

        .enhanced-table th {
            background-color: #f0f0f0;
            font-weight: bold;
        }

        /* 移除表格斑馬紋 */
        .enhanced-table tr:nth-child(even) {
            background-color: transparent;
        }

        /* 圖片樣式 - PDF優化 */
        img {
            max-width: 100%;
            height: auto;
            border: none;
            box-shadow: none;
            border-radius: 0;
            page-break-inside: avoid;
        }

        /* 代碼樣式 - PDF優化 */
        code {
            background-color: #f5f5f5;
            padding: 1px 3px;
            border: 1px solid #ddd;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }

        pre {
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            padding: 10px;
            overflow: visible;
            font-size: 0.85em;
            page-break-inside: avoid;
        }

        /* 鏈接樣式 */
        a {
            color: #000;
            text-decoration: underline;
        }

        /* 段落間距優化 */
        p {
            margin: 0.5em 0;
            text-align: justify;
        }

        /* 引用樣式 - 簡化 */
        blockquote {
            border-left: 2px solid #666;
            margin: 1em 0;
            padding-left: 10px;
            font-style: normal;
            background: transparent;
        }

        /* 練習區塊 - 簡化 */
        .exercise-block {
            background: transparent;
            border: none;
            padding: 0;
            margin: 1em 0;
        }

        .exercise-header {
            color: #000;
            font-weight: bold;
            margin: 0.5em 0;
        }

        /* MathJax 公式 - PDF優化 */
        .MathJax_Display, mjx-container[display="true"] {
            margin: 0.8em auto !important;
            text-align: center !important;
            page-break-inside: avoid !important;
            font-size: 1em !important;
        }

        .MathJax, mjx-container {
            font-size: 1em !important;
            line-height: 1.2 !important;
        }

        mjx-container:not([display="true"]) {
            display: inline !important;
            vertical-align: middle !important;
            margin: 0 0.1em !important;
        }

        /* 分頁控制 */
        @page {
            size: A4;
            margin: 1.5cm 1.5cm 2cm 1.5cm;
        }

        /* 避免孤行和寡行 */
        p, li {
            orphans: 2;
            widows: 2;
        }

        /* 標題不要單獨在頁面底部 */
        h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
            orphans: 3;
            widows: 3;
        }
    </style>

    <!-- MathJax 配置 - PDF優化版 -->
    <script>
        window.MathJax = {
            tex: {
                inlineMath: [['$', '$'], ['\(', '\)']],
                displayMath: [['$$', '$$'], ['\[', '\]']],
                processEscapes: true,
                processEnvironments: true,
                packages: {'[+]': ['mhchem', 'ams', 'newcommand']},
                tags: 'none'  // PDF中不顯示公式編號
            },
            loader: {
                load: ['[tex]/mhchem', '[tex]/ams', '[tex]/newcommand']
            },
            chtml: {
                scale: 1.0,
                minScale: 0.5,
                maxScale: 1.5,
                matchFontHeight: false,
                displayAlign: 'center',
                displayIndent: '0',
                adaptiveCSS: false,  // PDF中禁用自適應CSS
                fontURL: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/output/chtml/fonts/woff-v2'
            },
            options: {
                skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre'],
                ignoreHtmlClass: 'tex2jax_ignore',
                processHtmlClass: 'tex2jax_process'
            },
            startup: {
                ready() {
                    MathJax.startup.defaultReady();
                    // PDF模式下不需要動態調整
                }
            }
        };
    </script>
    <script type="text/javascript" id="MathJax-script" async
        src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js">
    </script>
</head>
<body>
    <h1>第三課：看圖說故事 - 展現"觀察力"與"想像力"</h1>
<p><img alt="觀察與想像力" src="../../../TestFile/photo/Gemini_Generated_Image_2.png"/></p>
<h2>學習目標與導入</h2>
<h3>學習目標</h3>
<p>在本課程中，你將會：</p>
<ul>
<li>理解看圖說故事在面試中的目的和重要性</li>
<li>學習如何細緻觀察圖片中的細節</li>
<li>培養合理且豐富的想像力</li>
<li>練習有條理地表達觀察和聯想</li>
<li>掌握看圖說故事的基本結構和技巧</li>
</ul>
<h3>引言</h3>
<p>在前兩堂課中，我們學習了如何展現真誠、積極的態度，以及清晰、自信的口語表達。今天，我們將專注於看圖說故事這一面試環節，學習如何通過細緻的觀察和豐富的想像力，展現你的思考能力和創造力。</p>
<h2>看圖說故事的目的與結構</h2>
<h3>看圖說故事評估的特質</h3>
<ul>
<li><strong>觀察力</strong>：你能否注意到圖片中的細節和元素</li>
<li><strong>想像力</strong>：你能否從圖片內容進行合理且豐富的聯想</li>
<li><strong>描述能力</strong>：你能否清晰地描述所見所想</li>
<li><strong>連貫性</strong>：你的描述和聯想是否有邏輯性和連貫性</li>
</ul>
<h3>基本結構</h3>
<p><img alt="看圖說故事結構流程圖" src="../../../TestFile/photo/Gemini_Generated_Image_3.png"/></p>
<ol>
<li>
<p><strong>描述場景</strong>（看見什麼）</p>
<ul>
<li>人物：誰在圖片中？他們的表情、動作如何？</li>
<li>物件：有哪些重要的物品或元素？</li>
<li>場景：在什麼地方？環境如何？</li>
<li>動作：正在發生什麼事情？</li>
</ul>
</li>
<li>
<p><strong>詮釋與推斷</strong>（認為發生什麼事）</p>
<ul>
<li>人物可能的情緒和想法</li>
<li>圖片可能表達的故事或情境</li>
<li>事件的前因後果</li>
</ul>
</li>
<li>
<p><strong>以感受或領悟作結</strong>（圖片讓你感受到或想到什麼）</p>
<ul>
<li>個人感受或反思</li>
<li>可能的啟示或教訓</li>
<li>與生活經驗的聯繫</li>
</ul>
</li>
</ol>
<h2>培養細緻的觀察技巧</h2>
<h3>系統性觀察方法</h3>
<p>觀察圖片時，可以遵循以下步驟：</p>
<ol>
<li><strong>整體印象</strong>：先快速掃視整張圖片，獲取整體感覺</li>
<li><strong>主要元素</strong>：識別圖片中的主要人物或物件</li>
<li><strong>細節觀察</strong>：仔細觀察細節，如表情、姿勢、環境特徵</li>
<li><strong>前景到背景</strong>：從前景觀察到背景，確保不遺漏重要元素</li>
<li><strong>關係分析</strong>：思考各元素之間的關係和互動</li>
</ol>
<h3>運用多重感官描述</h3>
<p>除了視覺描述，還可以想像並描述：</p>
<ul>
<li>可能聽到的聲音（人聲、自然聲、音樂等）</li>
<li>可能聞到的氣味（食物、自然、環境等）</li>
<li>可能感受到的溫度或觸感（溫暖、寒冷、柔軟、堅硬等）</li>
</ul>
<h3>描述詞彙庫</h3>
<h4>描述人物的詞彙</h4>
<ul>
<li><strong>表情</strong>：微笑的、皺眉的、驚訝的、專注的、困惑的、興奮的</li>
<li><strong>肢體語言</strong>：挺直的、放鬆的、緊張的、活躍的、疲倦的、熱情的</li>
<li><strong>動作</strong>：走路、跑步、跳躍、坐著、站立、彎腰、指著、握著</li>
</ul>
<h4>描述場景的詞彙</h4>
<ul>
<li><strong>環境</strong>：繁忙的、寧靜的、混亂的、整潔的、自然的、都市的</li>
<li><strong>氛圍</strong>：歡樂的、嚴肅的、神秘的、溫馨的、緊張的、輕鬆的</li>
<li><strong>天氣/時間</strong>：晴朗的、多雲的、雨天、黃昏、清晨、夜晚</li>
</ul>
<h4>描述物品的詞彙</h4>
<ul>
<li><strong>大小</strong>：巨大的、微小的、中等的、寬闊的、狹窄的</li>
<li><strong>形狀</strong>：圓形的、方形的、不規則的、彎曲的、直的</li>
<li><strong>顏色</strong>：鮮豔的、暗淡的、明亮的、柔和的、對比強烈的</li>
</ul>
<h2>培養豐富且相關的想像力</h2>
<h3>引導問題</h3>
<p>當你觀察完圖片後，可以問自己以下問題來激發想像力：</p>
<ul>
<li>
<p><strong>這場景之前發生了什麼？</strong></p>
<ul>
<li>人物為什麼會在這裡？</li>
<li>是什麼事件導致了圖片中的情境？</li>
</ul>
</li>
<li>
<p><strong>接下來可能發生什麼？</strong></p>
<ul>
<li>人物下一步可能做什麼？</li>
<li>情境會如何發展？</li>
</ul>
</li>
<li>
<p><strong>角色們在想什麼/感受如何？</strong></p>
<ul>
<li>他們的表情和姿勢透露了什麼情緒？</li>
<li>他們可能有什麼想法或目標？</li>
</ul>
</li>
<li>
<p><strong>潛在的訊息或故事是什麼？</strong></p>
<ul>
<li>圖片可能想表達什麼主題或訊息？</li>
<li>有沒有隱含的故事情節？</li>
</ul>
</li>
</ul>
<h3>將圖片與個人經歷或更廣泛的主題聯繫</h3>
<ul>
<li>圖片中的情境是否讓你想起自己的經歷？</li>
<li>圖片是否反映了某些社會現象或普遍價值觀？</li>
<li>你能從圖片中學到什麼，或得到什麼啟示？</li>
</ul>
<h3>平衡想像力與合理性</h3>
<ul>
<li>想像要基於圖片中的視覺線索</li>
<li>避免過度發揮，脫離圖片實際內容</li>
<li>確保你的聯想與圖片的整體氛圍和元素相符</li>
</ul>
<h3>激發想像的技巧</h3>
<ul>
<li><strong>角色代入</strong>：想像自己是圖片中的人物，你會有什麼感受和想法</li>
<li><strong>故事延伸</strong>：構思圖片前後可能發生的事情</li>
<li><strong>多角度思考</strong>：從不同人物的視角理解圖片</li>
<li><strong>假設情境</strong>：「如果...會怎樣？」的思考方式</li>
</ul>
<h2>連貫地表達觀察與想像</h2>
<h3>有邏輯地組織思緒</h3>
<ul>
<li>按照「描述→詮釋→感受」的基本結構組織內容</li>
<li>從大到小，從整體到細節</li>
<li>從明顯特徵到隱含信息</li>
<li>確保各部分之間有自然過渡</li>
</ul>
<h3>運用連接詞和片語使過渡流暢</h3>
<ul>
<li><strong>添加信息</strong>：此外、同時、不僅如此、另外</li>
<li><strong>表示因果</strong>：因此、由於、這導致、結果是</li>
<li><strong>表示對比</strong>：然而、相反、儘管如此、與此不同</li>
<li><strong>表示時間順序</strong>：首先、接著、然後、最後</li>
<li><strong>總結觀點</strong>：總的來說、綜上所述、這表明</li>
</ul>
<h3>看圖說故事的常用句式</h3>
<h4>描述場景</h4>
<ul>
<li>「在這張圖片中，我看到...」</li>
<li>「圖中展示了...」</li>
<li>「這是一個...的場景」</li>
<li>「主要人物/物體是...」</li>
</ul>
<h4>詮釋與推斷</h4>
<ul>
<li>「從他們的表情看來，他們似乎...」</li>
<li>「這可能是...的情境」</li>
<li>「我認為圖片想表達...」</li>
<li>「這個場景可能發生在...」</li>
</ul>
<h4>個人感受與聯想</h4>
<ul>
<li>「這讓我想起...」</li>
<li>「這張圖片傳達了...的訊息」</li>
<li>「從中我們可以學到...」</li>
<li>「這個情境反映了現實生活中的...」</li>
</ul>
<div class="exercise-block"><h2 class="exercise-header">看圖說故事練習（圖片一）</h2>
<p><img alt="練習圖片一" src="../../../TestFile/photo/Gemini_Generated_Image_1.png"/></p>
<p><em>圖片描述：一個小男孩在公園裡幫助一位老人撿起掉落的物品，周圍有其他人在散步。</em></p>
</div><h3>引導提示</h3>
<h4>你看到了什麼（人物、地點、動作）？</h4>
<ul>
<li>主要人物是誰？他們在做什麼？</li>
<li>場景在哪裡？環境有什麼特點？</li>
<li>注意人物的表情和肢體語言</li>
<li>有哪些重要的物品或細節？</li>
</ul>
<h4>你認為這張圖片想表達什麼？</h4>
<ul>
<li>人物之間可能有什麼關係？</li>
<li>這個情境之前可能發生了什麼？</li>
<li>接下來可能會發生什麼？</li>
<li>人物可能有什麼感受或想法？</li>
</ul>
<h4>這張圖片讓你聯想到什麼故事或經歷？</h4>
<ul>
<li>這個場景是否讓你想起自己的經歷？</li>
<li>圖片傳達了什麼價值觀或訊息？</li>
<li>你從中得到什麼啟示或感受？</li>
</ul>
<h3>我的觀察與想像（筆記空間）</h3>
<h2><strong>描述場景</strong>：</h2>
<hr/>
<hr/>
<hr/>
<h2><strong>詮釋與推斷</strong>：</h2>
<hr/>
<hr/>
<hr/>
<h2><strong>個人感受與聯想</strong>：</h2>
<hr/>
<hr/>
<hr/>
<div class="exercise-block"><h2 class="exercise-header">看圖說故事練習（圖片二）</h2>
<p><img alt="練習圖片二" src="../../../TestFile/photo/Gemini_Generated_Image_2.png"/></p>
<p><em>圖片描述：一個教室場景，學生們正在進行小組活動，有的在討論，有的在製作海報，老師在旁邊指導。</em></p>
</div><h3>引導提示</h3>
<h4>你看到了什麼（人物、地點、動作）？</h4>
<ul>
<li>教室裡有哪些人？他們在做什麼？</li>
<li>環境有什麼特點？注意教室的佈置</li>
<li>學生們的表情和互動如何？</li>
<li>老師在做什麼？他/她的角色是什麼？</li>
</ul>
<h4>你認為這張圖片想表達什麼？</h4>
<ul>
<li>這可能是什麼類型的課堂活動？</li>
<li>學生們可能在學習什麼或討論什麼？</li>
<li>這種教學方式有什麼特點或優點？</li>
<li>師生之間的關係如何？</li>
</ul>
<h4>這張圖片讓你聯想到什麼故事或經歷？</h4>
<ul>
<li>這是否讓你想起自己的學校經歷？</li>
<li>這種學習方式對學生有什麼好處？</li>
<li>你認為理想的課堂應該是什麼樣子？</li>
</ul>
<h3>我的觀察與想像（筆記空間）</h3>
<h2><strong>描述場景</strong>：</h2>
<hr/>
<hr/>
<hr/>
<h2><strong>詮釋與推斷</strong>：</h2>
<hr/>
<hr/>
<hr/>
<h2><strong>個人感受與聯想</strong>：</h2>
<hr/>
<hr/>
<hr/>
<div class="exercise-block"><h2 class="exercise-header">看圖說故事練習記錄表</h2>
<div class="table-wrapper"><table class="enhanced-table">
<thead>
<tr>
<th>日期</th>
<th>圖片描述</th>
<th>主要觀察點</th>
<th>想像詮釋</th>
<th>自我評估</th>
</tr>
</thead>
<tbody>
<tr>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td>觀察力：□優 □良 □可 □待改進<br/>想像力：□優 □良 □可 □待改進<br/>表達能力：□優 □良 □可 □待改進</td>
</tr>
<tr>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td>觀察力：□優 □良 □可 □待改進<br/>想像力：□優 □良 □可 □待改進<br/>表達能力：□優 □良 □可 □待改進</td>
</tr>
<tr>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td class="empty-cell"> </td>
<td>觀察力：□優 □良 □可 □待改進<br/>想像力：□優 □良 □可 □待改進<br/>表達能力：□優 □良 □可 □待改進</td>
</tr>
</tbody>
</table></div>
</div><h2>個人筆記與反思</h2>
<h3>今天課堂的重點筆記</h3>
<hr/>
<hr/>
<hr/>
<hr/>
<h3>我在觀察力方面的優勢</h3>
<hr/>
<hr/>
<hr/>
<hr/>
<h3>我在想像力方面的優勢</h3>
<hr/>
<hr/>
<hr/>
<hr/>
<h3>我需要改進的地方</h3>
<hr/>
<hr/>
<hr/>
<hr/>
<div class="exercise-block"><h3 class="exercise-header">我的練習計劃</h3>
<hr/>
<hr/>
<hr/>
<hr/>
<hr/>
<p><strong>記住</strong>：細心觀察，讓你的想像力無限延伸！</p>
</div>
</body>
</html>