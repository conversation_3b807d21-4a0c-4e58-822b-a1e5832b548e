'use client';

import Link from "next/link";
import { AppProvider } from "@/contexts/AppContext";

export default function Home() {
  return (
    <AppProvider>
      <div className="px-4 py-8">
        {/* 英雄區域 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            教育材料轉換系統
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            專為香港10-17歲學生設計的教材轉換工具，將 Markdown 格式的教育材料轉換為精美的 HTML 和 PDF 文件
          </p>
          <div className="flex gap-4 justify-center">
            <Link
              href="/convert"
              className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              開始轉換
            </Link>
            <Link
              href="/help"
              className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            >
              使用說明
            </Link>
          </div>
        </div>

        {/* 功能特色 */}
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <div className="text-center p-6 bg-white rounded-lg shadow-sm border">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Markdown 轉換</h3>
            <p className="text-gray-600">
              支援完整的 Markdown 語法，包括數學公式、表格、圖片等教育材料常用元素
            </p>
          </div>

          <div className="text-center p-6 bg-white rounded-lg shadow-sm border">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17v4a2 2 0 002 2h4M13 13h4a2 2 0 012 2v4a2 2 0 01-2 2h-4" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">多種主題</h3>
            <p className="text-gray-600">
              提供5種精美主題，包括預設、自然、太空、科技和運動主題，適合不同學科需求
            </p>
          </div>

          <div className="text-center p-6 bg-white rounded-lg shadow-sm border">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">批量處理</h3>
            <p className="text-gray-600">
              支援單檔案和資料夾批量上傳，一次性處理多個教材檔案，提高工作效率
            </p>
          </div>
        </div>

        {/* 使用流程 */}
        <div className="bg-white rounded-lg shadow-sm border p-8">
          <h2 className="text-2xl font-bold text-center mb-8">使用流程</h2>
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-semibold">
                1
              </div>
              <h3 className="font-semibold mb-2">上傳檔案</h3>
              <p className="text-sm text-gray-600">
                拖拽或點擊上傳 Markdown 檔案
              </p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-semibold">
                2
              </div>
              <h3 className="font-semibold mb-2">選擇主題</h3>
              <p className="text-sm text-gray-600">
                從5種主題中選擇適合的樣式
              </p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-semibold">
                3
              </div>
              <h3 className="font-semibold mb-2">配置選項</h3>
              <p className="text-sm text-gray-600">
                設定分頁模式和處理選項
              </p>
            </div>
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mx-auto mb-3 font-semibold">
                4
              </div>
              <h3 className="font-semibold mb-2">下載結果</h3>
              <p className="text-sm text-gray-600">
                獲取 HTML 和 PDF 格式檔案
              </p>
            </div>
          </div>
        </div>
      </div>
    </AppProvider>
  );
}
