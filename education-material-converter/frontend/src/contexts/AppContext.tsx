'use client';

/**
 * 應用程式全局狀態管理
 */

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { FileInfo, TaskStatus, ThemeInfo } from '@/lib/api';

// 狀態類型定義
export interface AppState {
  // 檔案相關
  uploadedFiles: FileInfo[];
  isUploading: boolean;
  
  // 轉換相關
  currentTask: TaskStatus | null;
  conversionHistory: TaskStatus[];
  
  // 主題相關
  availableThemes: ThemeInfo[];
  selectedTheme: string;
  
  // UI 狀態
  isLoading: boolean;
  error: string | null;
  
  // 配置選項
  conversionOptions: {
    paginationMode: 'balanced' | 'compact' | 'integrity';
    includeToc: boolean;
    mathRendering: boolean;
    exerciseProcessing: boolean;
    tableEnhancement: boolean;
    imageOptimization: boolean;
  };
}

// 動作類型定義
export type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_UPLOADED_FILES'; payload: FileInfo[] }
  | { type: 'ADD_UPLOADED_FILES'; payload: FileInfo[] }
  | { type: 'SET_UPLOADING'; payload: boolean }
  | { type: 'SET_CURRENT_TASK'; payload: TaskStatus | null }
  | { type: 'UPDATE_TASK_STATUS'; payload: TaskStatus }
  | { type: 'ADD_TO_HISTORY'; payload: TaskStatus }
  | { type: 'SET_THEMES'; payload: ThemeInfo[] }
  | { type: 'SET_SELECTED_THEME'; payload: string }
  | { type: 'UPDATE_CONVERSION_OPTIONS'; payload: Partial<AppState['conversionOptions']> }
  | { type: 'RESET_STATE' };

// 初始狀態
const initialState: AppState = {
  uploadedFiles: [],
  isUploading: false,
  currentTask: null,
  conversionHistory: [],
  availableThemes: [],
  selectedTheme: 'default',
  isLoading: false,
  error: null,
  conversionOptions: {
    paginationMode: 'balanced',
    includeToc: true,
    mathRendering: true,
    exerciseProcessing: true,
    tableEnhancement: true,
    imageOptimization: true,
  },
};

// Reducer 函數
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'SET_UPLOADED_FILES':
      return { ...state, uploadedFiles: action.payload };
    
    case 'ADD_UPLOADED_FILES':
      return { 
        ...state, 
        uploadedFiles: [...state.uploadedFiles, ...action.payload] 
      };
    
    case 'SET_UPLOADING':
      return { ...state, isUploading: action.payload };
    
    case 'SET_CURRENT_TASK':
      return { ...state, currentTask: action.payload };
    
    case 'UPDATE_TASK_STATUS':
      return { 
        ...state, 
        currentTask: action.payload,
        conversionHistory: state.conversionHistory.map(task =>
          task.task_id === action.payload.task_id ? action.payload : task
        )
      };
    
    case 'ADD_TO_HISTORY':
      return { 
        ...state, 
        conversionHistory: [action.payload, ...state.conversionHistory] 
      };
    
    case 'SET_THEMES':
      return { ...state, availableThemes: action.payload };
    
    case 'SET_SELECTED_THEME':
      return { ...state, selectedTheme: action.payload };
    
    case 'UPDATE_CONVERSION_OPTIONS':
      return { 
        ...state, 
        conversionOptions: { ...state.conversionOptions, ...action.payload } 
      };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
}

// Context 創建
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider 組件
export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook 用於使用 Context
export function useAppContext() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
}

// 便利的 Hook 用於特定狀態
export function useUploadedFiles() {
  const { state, dispatch } = useAppContext();
  return {
    files: state.uploadedFiles,
    isUploading: state.isUploading,
    setFiles: (files: FileInfo[]) => dispatch({ type: 'SET_UPLOADED_FILES', payload: files }),
    addFiles: (files: FileInfo[]) => dispatch({ type: 'ADD_UPLOADED_FILES', payload: files }),
    setUploading: (uploading: boolean) => dispatch({ type: 'SET_UPLOADING', payload: uploading }),
  };
}

export function useConversionTask() {
  const { state, dispatch } = useAppContext();
  return {
    currentTask: state.currentTask,
    history: state.conversionHistory,
    setCurrentTask: (task: TaskStatus | null) => dispatch({ type: 'SET_CURRENT_TASK', payload: task }),
    updateTaskStatus: (task: TaskStatus) => dispatch({ type: 'UPDATE_TASK_STATUS', payload: task }),
    addToHistory: (task: TaskStatus) => dispatch({ type: 'ADD_TO_HISTORY', payload: task }),
  };
}

export function useThemes() {
  const { state, dispatch } = useAppContext();
  return {
    themes: state.availableThemes,
    selectedTheme: state.selectedTheme,
    setThemes: (themes: ThemeInfo[]) => dispatch({ type: 'SET_THEMES', payload: themes }),
    setSelectedTheme: (theme: string) => dispatch({ type: 'SET_SELECTED_THEME', payload: theme }),
  };
}

export function useConversionOptions() {
  const { state, dispatch } = useAppContext();
  return {
    options: state.conversionOptions,
    updateOptions: (options: Partial<AppState['conversionOptions']>) => 
      dispatch({ type: 'UPDATE_CONVERSION_OPTIONS', payload: options }),
  };
}
