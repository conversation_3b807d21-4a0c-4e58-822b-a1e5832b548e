/**
 * API 通信層
 * 處理與後端 API 的所有通信
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API 基礎配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// 創建 axios 實例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在這裡添加認證 token 等
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 響應攔截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 類型定義
export interface ConvertRequest {
  files: string[];
  theme: string;
  options: Record<string, unknown>;
}

export interface TaskStatus {
  task_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result_files?: Array<{ format: string; path: string }>;
  error_message?: string;
}

export interface ConversionResponse {
  task_id: string;
  message: string;
  status: string;
}

export interface ThemeInfo {
  id: string;
  name: string;
  description: string;
  preview_url: string;
}

export interface FileInfo {
  file_id: string;
  original_name: string;
  size: number;
  upload_time?: string;
}

// API 方法
export const api = {
  // 健康檢查
  async healthCheck(): Promise<{ status: string; message: string }> {
    const response = await apiClient.get('/health');
    return response.data;
  },

  // 檔案上傳
  async uploadFiles(files: File[]): Promise<{
    message: string;
    files: FileInfo[];
    total_files: number;
  }> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await apiClient.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 啟動轉換任務
  async convertFiles(request: ConvertRequest): Promise<ConversionResponse> {
    const response = await apiClient.post('/api/convert', request);
    return response.data;
  },

  // 查詢任務狀態
  async getTaskStatus(taskId: string): Promise<TaskStatus> {
    const response = await apiClient.get(`/api/status/${taskId}`);
    return response.data;
  },

  // 下載檔案
  async downloadFile(taskId: string, format: 'html' | 'pdf'): Promise<Blob> {
    const response = await apiClient.get(`/api/download/${taskId}/${format}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // 獲取主題列表
  async getThemes(): Promise<ThemeInfo[]> {
    const response = await apiClient.get('/api/themes');
    return response.data;
  },
};

// 錯誤處理工具
export const handleApiError = (error: unknown): string => {
  if (error && typeof error === 'object') {
    const err = error as { response?: { data?: { message?: string } }; message?: string };
    if (err.response?.data?.message) {
      return err.response.data.message;
    }
    if (err.message) {
      return err.message;
    }
  }
  return '發生未知錯誤';
};

export default api;
