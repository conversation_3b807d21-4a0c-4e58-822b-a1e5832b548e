#!/bin/bash

# 整合測試模組

test_integration() {
    echo "執行前後端整合測試..."
    
    # 檢查前後端是否都在運行
    local frontend_running=false
    local backend_running=false
    
    if [ -n "$FRONTEND_PID" ] && kill -0 "$FRONTEND_PID" 2>/dev/null; then
        frontend_running=true
    fi
    
    if [ -n "$BACKEND_PID" ] && kill -0 "$BACKEND_PID" 2>/dev/null; then
        backend_running=true
    fi
    
    if [ "$frontend_running" = true ] && [ "$backend_running" = true ]; then
        record_test_result "前後端同時運行測試" "PASS" "前端和後端服務器都在運行"
    else
        record_test_result "前後端同時運行測試" "FAIL" "前端或後端服務器未運行"
        return
    fi
    
    # 測試端口衝突
    if nc -z localhost 3000 && nc -z localhost 8000; then
        record_test_result "端口衝突檢查" "PASS" "前端(3000)和後端(8000)端口都可用"
    else
        record_test_result "端口衝突檢查" "FAIL" "端口不可用或衝突"
    fi
    
    # 測試 CORS 配置
    echo "測試 CORS 配置..."
    local cors_test_result=$(curl -s -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: Content-Type" \
        -X OPTIONS "http://localhost:8000/health" \
        -w "%{http_code}" -o /dev/null)
    
    if [ "$cors_test_result" = "200" ]; then
        record_test_result "CORS 配置測試" "PASS" "CORS 預檢請求成功"
    else
        record_test_result "CORS 配置測試" "FAIL" "CORS 預檢請求失敗，HTTP 狀態碼: $cors_test_result"
    fi
    
    # 測試前端到後端的 API 調用
    echo "測試前端到後端的 API 調用..."
    
    # 創建測試用的 JavaScript 檔案
    cat > "$TEMP_DIR/api_test.js" << 'EOF'
const axios = require('axios');

async function testApiCall() {
    try {
        const response = await axios.get('http://localhost:8000/health', {
            headers: {
                'Origin': 'http://localhost:3000'
            }
        });
        console.log('API_CALL_SUCCESS');
        console.log(JSON.stringify(response.data));
    } catch (error) {
        console.log('API_CALL_FAILED');
        console.log(error.message);
    }
}

testApiCall();
EOF
    
    # 檢查是否有 Node.js 和 axios
    if check_command "node" && [ -d "$PROJECT_ROOT/frontend/node_modules/axios" ]; then
        cd "$PROJECT_ROOT/frontend"
        local api_test_output=$(node "$TEMP_DIR/api_test.js" 2>&1)
        
        if echo "$api_test_output" | grep -q "API_CALL_SUCCESS"; then
            record_test_result "前端 API 調用測試" "PASS" "前端可以成功調用後端 API"
        else
            record_test_result "前端 API 調用測試" "FAIL" "前端無法調用後端 API: $api_test_output"
        fi
        cd "$PROJECT_ROOT"
    else
        record_test_result "前端 API 調用測試" "SKIP" "缺少 Node.js 或 axios 依賴"
    fi
    
    # 測試 API 響應格式
    echo "測試 API 響應格式..."
    local health_response=$(curl -s "http://localhost:8000/health")
    
    if echo "$health_response" | python3 -m json.tool >/dev/null 2>&1; then
        record_test_result "API 響應格式測試" "PASS" "API 返回有效的 JSON 格式"
        
        # 檢查響應內容結構
        if echo "$health_response" | grep -q '"status"' && echo "$health_response" | grep -q '"message"'; then
            record_test_result "API 響應結構測試" "PASS" "響應包含必要的欄位"
        else
            record_test_result "API 響應結構測試" "FAIL" "響應缺少必要的欄位"
        fi
    else
        record_test_result "API 響應格式測試" "FAIL" "API 返回無效的 JSON 格式"
    fi
    
    # 測試主題 API
    echo "測試主題 API..."
    local themes_response=$(curl -s "http://localhost:8000/api/themes")
    
    if echo "$themes_response" | python3 -m json.tool >/dev/null 2>&1; then
        record_test_result "主題 API 格式測試" "PASS" "主題 API 返回有效的 JSON"
        
        # 檢查是否返回陣列
        if echo "$themes_response" | python3 -c "import sys, json; data=json.load(sys.stdin); exit(0 if isinstance(data, list) else 1)" 2>/dev/null; then
            record_test_result "主題 API 結構測試" "PASS" "返回主題陣列"
            
            # 檢查主題數量
            local theme_count=$(echo "$themes_response" | python3 -c "import sys, json; print(len(json.load(sys.stdin)))")
            if [ "$theme_count" -ge 5 ]; then
                record_test_result "主題數量測試" "PASS" "包含 $theme_count 個主題"
            else
                record_test_result "主題數量測試" "FAIL" "主題數量不足，只有 $theme_count 個"
            fi
        else
            record_test_result "主題 API 結構測試" "FAIL" "返回的不是陣列格式"
        fi
    else
        record_test_result "主題 API 格式測試" "FAIL" "主題 API 返回無效的 JSON"
    fi
    
    # 測試錯誤處理
    echo "測試錯誤處理..."
    local error_response=$(curl -s -w "%{http_code}" "http://localhost:8000/api/nonexistent" -o "$TEMP_DIR/error_response.json")
    
    if [ "$error_response" = "404" ]; then
        record_test_result "404 錯誤處理測試" "PASS" "不存在的端點返回 404 狀態碼"
    else
        record_test_result "404 錯誤處理測試" "FAIL" "不存在的端點返回狀態碼: $error_response"
    fi
    
    # 測試服務器穩定性
    echo "測試服務器穩定性..."
    local stability_test_passed=true
    
    for i in {1..5}; do
        if ! curl -s -f "http://localhost:8000/health" >/dev/null; then
            stability_test_passed=false
            break
        fi
        sleep 1
    done
    
    if [ "$stability_test_passed" = true ]; then
        record_test_result "服務器穩定性測試" "PASS" "服務器在連續請求下保持穩定"
    else
        record_test_result "服務器穩定性測試" "FAIL" "服務器在連續請求下不穩定"
    fi
    
    # 測試資源使用情況
    if [ -n "$BACKEND_PID" ]; then
        local backend_memory=$(ps -o rss= -p "$BACKEND_PID" 2>/dev/null || echo "0")
        if [ "$backend_memory" -gt 0 ] && [ "$backend_memory" -lt 500000 ]; then  # 500MB
            record_test_result "後端記憶體使用測試" "PASS" "記憶體使用: ${backend_memory}KB"
        else
            record_test_result "後端記憶體使用測試" "FAIL" "記憶體使用過高: ${backend_memory}KB"
        fi
    fi
    
    if [ -n "$FRONTEND_PID" ]; then
        local frontend_memory=$(ps -o rss= -p "$FRONTEND_PID" 2>/dev/null || echo "0")
        if [ "$frontend_memory" -gt 0 ] && [ "$frontend_memory" -lt 1000000 ]; then  # 1GB
            record_test_result "前端記憶體使用測試" "PASS" "記憶體使用: ${frontend_memory}KB"
        else
            record_test_result "前端記憶體使用測試" "FAIL" "記憶體使用過高: ${frontend_memory}KB"
        fi
    fi
}
