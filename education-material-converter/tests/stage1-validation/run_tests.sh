#!/bin/bash

# 教育材料轉換系統 - 第一階段基礎架構驗證測試腳本
# 版本: 1.0
# 作者: 開發團隊
# 日期: 2024-12-02

set -e  # 遇到錯誤時停止執行

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局變量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
TEST_RESULTS_DIR="$SCRIPT_DIR/results"
TEST_LOG="$TEST_RESULTS_DIR/test.log"
TEST_REPORT="$TEST_RESULTS_DIR/test_report.md"
TEMP_DIR="$TEST_RESULTS_DIR/temp"

# 測試統計
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 進程 PID 記錄
FRONTEND_PID=""
BACKEND_PID=""

# 初始化測試環境
init_test_environment() {
    echo -e "${BLUE}🚀 初始化測試環境...${NC}"
    
    # 創建測試結果目錄
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$TEMP_DIR"
    
    # 清理舊的日誌檔案
    > "$TEST_LOG"
    
    # 記錄測試開始時間
    echo "測試開始時間: $(date)" >> "$TEST_LOG"
    echo "項目根目錄: $PROJECT_ROOT" >> "$TEST_LOG"
    echo "測試腳本目錄: $SCRIPT_DIR" >> "$TEST_LOG"
    echo "" >> "$TEST_LOG"
    
    echo -e "${GREEN}✅ 測試環境初始化完成${NC}"
}

# 日誌記錄函數
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$TEST_LOG"
}

# 測試結果記錄函數
record_test_result() {
    local test_name="$1"
    local status="$2"
    local details="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    case "$status" in
        "PASS")
            PASSED_TESTS=$((PASSED_TESTS + 1))
            echo -e "${GREEN}✅ $test_name${NC}"
            log_message "PASS" "$test_name: $details"
            ;;
        "FAIL")
            FAILED_TESTS=$((FAILED_TESTS + 1))
            echo -e "${RED}❌ $test_name${NC}"
            echo -e "${RED}   錯誤: $details${NC}"
            log_message "FAIL" "$test_name: $details"
            ;;
        "SKIP")
            SKIPPED_TESTS=$((SKIPPED_TESTS + 1))
            echo -e "${YELLOW}⏭️  $test_name (跳過)${NC}"
            log_message "SKIP" "$test_name: $details"
            ;;
    esac
}

# 檢查命令是否存在
check_command() {
    local cmd="$1"
    if command -v "$cmd" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 等待端口可用
wait_for_port() {
    local port="$1"
    local timeout="$2"
    local count=0
    
    while [ $count -lt $timeout ]; do
        if nc -z localhost "$port" 2>/dev/null; then
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    return 1
}

# 清理函數
cleanup() {
    echo -e "\n${YELLOW}🧹 清理測試環境...${NC}"
    
    # 停止前端進程
    if [ -n "$FRONTEND_PID" ] && kill -0 "$FRONTEND_PID" 2>/dev/null; then
        echo "停止前端服務器 (PID: $FRONTEND_PID)"
        kill "$FRONTEND_PID" 2>/dev/null || true
        wait "$FRONTEND_PID" 2>/dev/null || true
    fi
    
    # 停止後端進程
    if [ -n "$BACKEND_PID" ] && kill -0 "$BACKEND_PID" 2>/dev/null; then
        echo "停止後端服務器 (PID: $BACKEND_PID)"
        kill "$BACKEND_PID" 2>/dev/null || true
        wait "$BACKEND_PID" 2>/dev/null || true
    fi
    
    # 清理臨時檔案
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi
    
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 設置信號處理
trap cleanup EXIT INT TERM

# 顯示測試標題
show_test_header() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    教育材料轉換系統 - 第一階段基礎架構驗證測試                    ║"
    echo "║                                                                              ║"
    echo "║  測試範圍: 項目結構、前端環境、後端環境、整合測試                                ║"
    echo "║  執行時間: $(date '+%Y-%m-%d %H:%M:%S')                                        ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

# 主函數
main() {
    show_test_header
    init_test_environment
    
    # 載入測試模組
    source "$SCRIPT_DIR/test_project_structure.sh"
    source "$SCRIPT_DIR/test_frontend.sh"
    source "$SCRIPT_DIR/test_backend.sh"
    source "$SCRIPT_DIR/test_integration.sh"
    
    echo -e "${CYAN}📋 開始執行測試...${NC}\n"
    
    # 執行測試
    echo -e "${BLUE}🏗️  第一部分: 項目結構驗證${NC}"
    test_project_structure
    
    echo -e "\n${BLUE}🎨 第二部分: 前端環境測試${NC}"
    test_frontend_environment
    
    echo -e "\n${BLUE}⚙️  第三部分: 後端環境測試${NC}"
    test_backend_environment
    
    echo -e "\n${BLUE}🔗 第四部分: 整合測試${NC}"
    test_integration
    
    # 生成測試報告
    generate_test_report
    
    # 顯示測試結果摘要
    show_test_summary
    
    # 根據測試結果決定退出碼
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "\n${GREEN}🎉 所有測試通過！第一階段基礎架構建立完成。${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ 有 $FAILED_TESTS 個測試失敗，請檢查錯誤信息並修復問題。${NC}"
        exit 1
    fi
}

# 生成測試報告
generate_test_report() {
    echo -e "${CYAN}📝 生成測試報告...${NC}"

    cat > "$TEST_REPORT" << EOF
# 教育材料轉換系統 - 第一階段基礎架構驗證測試報告

**測試執行時間**: $(date '+%Y-%m-%d %H:%M:%S')
**測試腳本版本**: 1.0
**項目根目錄**: $PROJECT_ROOT

## 📊 測試結果摘要

- **總測試數**: $TOTAL_TESTS
- **通過**: $PASSED_TESTS ✅
- **失敗**: $FAILED_TESTS ❌
- **跳過**: $SKIPPED_TESTS ⏭️
- **成功率**: $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%

## 📋 詳細測試結果

### 第一部分: 項目結構驗證
EOF

    # 從日誌中提取項目結構測試結果
    grep "\[PASS\]\|\[FAIL\]\|\[SKIP\]" "$TEST_LOG" | grep -E "目錄存在|檔案存在|README|docker-compose|權限|gitignore" | while read line; do
        local status=$(echo "$line" | grep -o "\[PASS\]\|\[FAIL\]\|\[SKIP\]")
        local test_name=$(echo "$line" | sed 's/.*\] \[.*\] \([^:]*\):.*/\1/')
        local details=$(echo "$line" | sed 's/.*: //')

        case "$status" in
            "[PASS]") echo "- ✅ $test_name" >> "$TEST_REPORT" ;;
            "[FAIL]") echo "- ❌ $test_name: $details" >> "$TEST_REPORT" ;;
            "[SKIP]") echo "- ⏭️ $test_name: $details" >> "$TEST_REPORT" ;;
        esac
    done

    cat >> "$TEST_REPORT" << EOF

### 第二部分: 前端環境測試
EOF

    # 從日誌中提取前端測試結果
    grep "\[PASS\]\|\[FAIL\]\|\[SKIP\]" "$TEST_LOG" | grep -E "Node.js|npm|package.json|依賴包|TypeScript|Tailwind|前端" | while read line; do
        local status=$(echo "$line" | grep -o "\[PASS\]\|\[FAIL\]\|\[SKIP\]")
        local test_name=$(echo "$line" | sed 's/.*\] \[.*\] \([^:]*\):.*/\1/')
        local details=$(echo "$line" | sed 's/.*: //')

        case "$status" in
            "[PASS]") echo "- ✅ $test_name" >> "$TEST_REPORT" ;;
            "[FAIL]") echo "- ❌ $test_name: $details" >> "$TEST_REPORT" ;;
            "[SKIP]") echo "- ⏭️ $test_name: $details" >> "$TEST_REPORT" ;;
        esac
    done

    cat >> "$TEST_REPORT" << EOF

### 第三部分: 後端環境測試
EOF

    # 從日誌中提取後端測試結果
    grep "\[PASS\]\|\[FAIL\]\|\[SKIP\]" "$TEST_LOG" | grep -E "Python|pip|虛擬環境|requirements|FastAPI|Uvicorn|後端|API" | while read line; do
        local status=$(echo "$line" | grep -o "\[PASS\]\|\[FAIL\]\|\[SKIP\]")
        local test_name=$(echo "$line" | sed 's/.*\] \[.*\] \([^:]*\):.*/\1/')
        local details=$(echo "$line" | sed 's/.*: //')

        case "$status" in
            "[PASS]") echo "- ✅ $test_name" >> "$TEST_REPORT" ;;
            "[FAIL]") echo "- ❌ $test_name: $details" >> "$TEST_REPORT" ;;
            "[SKIP]") echo "- ⏭️ $test_name: $details" >> "$TEST_REPORT" ;;
        esac
    done

    cat >> "$TEST_REPORT" << EOF

### 第四部分: 整合測試
EOF

    # 從日誌中提取整合測試結果
    grep "\[PASS\]\|\[FAIL\]\|\[SKIP\]" "$TEST_LOG" | grep -E "整合|CORS|API調用|響應|穩定性|記憶體" | while read line; do
        local status=$(echo "$line" | grep -o "\[PASS\]\|\[FAIL\]\|\[SKIP\]")
        local test_name=$(echo "$line" | sed 's/.*\] \[.*\] \([^:]*\):.*/\1/')
        local details=$(echo "$line" | sed 's/.*: //')

        case "$status" in
            "[PASS]") echo "- ✅ $test_name" >> "$TEST_REPORT" ;;
            "[FAIL]") echo "- ❌ $test_name: $details" >> "$TEST_REPORT" ;;
            "[SKIP]") echo "- ⏭️ $test_name: $details" >> "$TEST_REPORT" ;;
        esac
    done

    cat >> "$TEST_REPORT" << EOF

## 🎯 結論

EOF

    if [ $FAILED_TESTS -eq 0 ]; then
        cat >> "$TEST_REPORT" << EOF
🎉 **所有測試通過！第一階段基礎架構建立完成。**

✅ 項目結構完整
✅ 前端環境正常
✅ 後端環境正常
✅ 整合測試通過

**第一階段驗收標準**: 全部滿足 ✅

可以開始進行第二階段：核心處理引擎開發。
EOF
    else
        cat >> "$TEST_REPORT" << EOF
❌ **測試未完全通過，需要修復以下問題：**

EOF
        grep "\[FAIL\]" "$TEST_LOG" | while read line; do
            local test_name=$(echo "$line" | sed 's/.*\] \[FAIL\] \([^:]*\):.*/\1/')
            local details=$(echo "$line" | sed 's/.*: //')
            echo "- $test_name: $details" >> "$TEST_REPORT"
        done

        cat >> "$TEST_REPORT" << EOF

**建議修復步驟**:
1. 檢查失敗的測試項目
2. 根據錯誤信息進行修復
3. 重新執行測試腳本
4. 確保所有測試通過後再進入下一階段
EOF
    fi

    cat >> "$TEST_REPORT" << EOF

## 📁 測試檔案

- 測試日誌: \`$TEST_LOG\`
- 測試報告: \`$TEST_REPORT\`
- 臨時檔案: \`$TEMP_DIR\`

---
*此報告由自動化測試腳本生成*
EOF

    echo -e "${GREEN}✅ 測試報告已生成: $TEST_REPORT${NC}"
}

# 顯示測試結果摘要
show_test_summary() {
    echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                              測試結果摘要                                    ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"

    echo -e "\n${CYAN}📊 統計信息:${NC}"
    echo -e "   總測試數: ${BLUE}$TOTAL_TESTS${NC}"
    echo -e "   通過: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "   失敗: ${RED}$FAILED_TESTS${NC}"
    echo -e "   跳過: ${YELLOW}$SKIPPED_TESTS${NC}"

    if [ $TOTAL_TESTS -gt 0 ]; then
        local success_rate=$(( PASSED_TESTS * 100 / TOTAL_TESTS ))
        echo -e "   成功率: ${BLUE}$success_rate%${NC}"
    fi

    echo -e "\n${CYAN}📁 檔案位置:${NC}"
    echo -e "   測試報告: ${BLUE}$TEST_REPORT${NC}"
    echo -e "   詳細日誌: ${BLUE}$TEST_LOG${NC}"
}

# 如果直接執行此腳本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
