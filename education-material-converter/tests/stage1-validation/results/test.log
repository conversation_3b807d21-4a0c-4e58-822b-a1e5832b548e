測試開始時間: 西元2025年06月02日 (週一) 18時29分16秒 HKT
項目根目錄: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter
測試腳本目錄: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/tests/stage1-validation

[2025-06-02 18:29:16] [PASS] 目錄存在: frontend: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend
[2025-06-02 18:29:16] [PASS] 目錄存在: backend: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend
[2025-06-02 18:29:16] [PASS] 目錄存在: processor: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/processor
[2025-06-02 18:29:16] [PASS] 目錄存在: docs: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/docs
[2025-06-02 18:29:16] [PASS] 目錄存在: tests: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/tests
[2025-06-02 18:29:16] [PASS] 目錄存在: frontend/src: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src
[2025-06-02 18:29:16] [PASS] 目錄存在: frontend/src/app: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/app
[2025-06-02 18:29:16] [PASS] 目錄存在: frontend/src/lib: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/lib
[2025-06-02 18:29:16] [PASS] 目錄存在: frontend/src/contexts: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/contexts
[2025-06-02 18:29:16] [PASS] 目錄存在: backend/api: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/api
[2025-06-02 18:29:16] [PASS] 目錄存在: backend/models: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/models
[2025-06-02 18:29:16] [PASS] 目錄存在: backend/utils: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/utils
[2025-06-02 18:29:16] [PASS] 目錄存在: backend/venv: 目錄路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/venv
[2025-06-02 18:29:16] [PASS] 檔案存在: README.md: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/README.md
[2025-06-02 18:29:17] [PASS] 檔案存在: docker-compose.yml: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/docker-compose.yml
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/package.json: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/package.json
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/next.config.ts: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/next.config.ts
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/tailwind.config.js: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/tailwind.config.js
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/tsconfig.json: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/tsconfig.json
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/src/app/layout.tsx: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/app/layout.tsx
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/src/app/page.tsx: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/app/page.tsx
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/src/lib/api.ts: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/lib/api.ts
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/src/lib/utils.ts: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/lib/utils.ts
[2025-06-02 18:29:17] [PASS] 檔案存在: frontend/src/contexts/AppContext.tsx: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/frontend/src/contexts/AppContext.tsx
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/main.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/main.py
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/requirements.txt: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/requirements.txt
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/pyproject.toml: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/pyproject.toml
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/api/__init__.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/api/__init__.py
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/api/routes.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/api/routes.py
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/models/__init__.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/models/__init__.py
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/models/schemas.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/models/schemas.py
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/utils/__init__.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/utils/__init__.py
[2025-06-02 18:29:17] [PASS] 檔案存在: backend/utils/error_handlers.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/utils/error_handlers.py
[2025-06-02 18:29:17] [PASS] 檔案存在: processor/__init__.py: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/processor/__init__.py
[2025-06-02 18:29:17] [PASS] 檔案存在: processor/requirements.txt: 檔案路徑: /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/processor/requirements.txt
[2025-06-02 18:29:17] [PASS] README.md 內容檢查: 包含項目標題
[2025-06-02 18:29:17] [PASS] docker-compose.yml 內容檢查: 包含前後端服務配置
[2025-06-02 18:29:17] [PASS] 項目目錄權限檢查: 具有寫入權限
[2025-06-02 18:29:17] [SKIP] .gitignore 檔案檢查: 檔案不存在（可選）
[2025-06-02 18:29:17] [PASS] Node.js 安裝檢查: 版本: v18.19.1
[2025-06-02 18:29:17] [PASS] Node.js 版本檢查: 版本 v18.19.1 符合要求 (>= 18)
[2025-06-02 18:29:18] [PASS] npm 安裝檢查: 版本: 9.2.0
[2025-06-02 18:29:18] [PASS] package.json 檢查: 檔案存在
[2025-06-02 18:29:18] [PASS] 依賴包檢查: next: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] 依賴包檢查: react: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] 依賴包檢查: react-dom: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] 依賴包檢查: typescript: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] 依賴包檢查: tailwindcss: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] 依賴包檢查: axios: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] 依賴包檢查: react-dropzone: 已在 package.json 中定義
[2025-06-02 18:29:18] [PASS] node_modules 檢查: 依賴包已安裝
[2025-06-02 18:29:18] [PASS] 模組安裝檢查: next: 模組已安裝
[2025-06-02 18:29:18] [PASS] 模組安裝檢查: react: 模組已安裝
[2025-06-02 18:29:18] [PASS] 模組安裝檢查: typescript: 模組已安裝
[2025-06-02 18:29:18] [PASS] TypeScript 配置檢查: tsconfig.json 存在
[2025-06-02 18:29:46] [FAIL] TypeScript 編譯檢查: 編譯失敗: 49:27  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
135:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/utils.ts
112:46  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
112:56  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
138:46  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
138:56  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules
[2025-06-02 18:29:46] [PASS] Tailwind CSS 配置檢查: 配置檔案存在
[2025-06-02 18:29:46] [PASS] 前端服務器啟動測試: 服務器在端口 3000 成功啟動
[2025-06-02 18:29:53] [FAIL] 前端頁面訪問測試: 無法訪問首頁
[2025-06-02 18:29:53] [PASS] Python 安裝檢查: 版本: Python 3.12.3
[2025-06-02 18:29:53] [PASS] Python 版本檢查: 版本 Python 3.12.3 符合要求 (>= 3.11)
[2025-06-02 18:29:54] [PASS] pip 安裝檢查: 版本: pip 24.0 from /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/backend/venv/lib/python3.12/site-packages/pip (python 3.12)
[2025-06-02 18:29:54] [PASS] 虛擬環境檢查: venv 目錄存在
[2025-06-02 18:29:54] [PASS] 虛擬環境配置檢查: pyvenv.cfg 存在
[2025-06-02 18:29:54] [PASS] requirements.txt 檢查: 檔案存在
[2025-06-02 18:29:54] [PASS] 依賴包定義檢查: fastapi: 已在 requirements.txt 中定義
[2025-06-02 18:29:54] [PASS] 依賴包定義檢查: uvicorn: 已在 requirements.txt 中定義
[2025-06-02 18:29:54] [PASS] 依賴包定義檢查: python-multipart: 已在 requirements.txt 中定義
[2025-06-02 18:29:54] [PASS] 依賴包定義檢查: pydantic: 已在 requirements.txt 中定義
[2025-06-02 18:29:54] [PASS] 依賴包定義檢查: aiofiles: 已在 requirements.txt 中定義
[2025-06-02 18:29:54] [PASS] FastAPI 安裝檢查: FastAPI 已安裝在虛擬環境中
[2025-06-02 18:29:54] [PASS] Uvicorn 安裝檢查: Uvicorn 已安裝在虛擬環境中
[2025-06-02 18:29:54] [PASS] main.py 檢查: 主程式檔案存在
[2025-06-02 18:29:54] [PASS] main.py 語法檢查: 語法正確
[2025-06-02 18:29:54] [PASS] API 路由檔案檢查: routes.py 存在
[2025-06-02 18:29:54] [PASS] API 路由語法檢查: 語法正確
[2025-06-02 18:29:54] [PASS] 數據模型檔案檢查: schemas.py 存在
[2025-06-02 18:29:55] [PASS] 數據模型語法檢查: 語法正確
[2025-06-02 18:29:57] [PASS] 後端服務器啟動測試: 服務器在端口 8000 成功啟動
[2025-06-02 18:29:57] [PASS] 健康檢查端點測試: /health 端點可以正常訪問
[2025-06-02 18:29:57] [PASS] 健康檢查響應內容: 響應包含正確的狀態
[2025-06-02 18:29:57] [PASS] API 文檔端點測試: /docs 端點可以正常訪問
[2025-06-02 18:29:57] [PASS] API 端點測試: /api/themes: 端點可以正常訪問
[2025-06-02 18:29:57] [PASS] 前後端同時運行測試: 前端和後端服務器都在運行
[2025-06-02 18:29:57] [PASS] 端口衝突檢查: 前端(3000)和後端(8000)端口都可用
[2025-06-02 18:29:57] [PASS] CORS 配置測試: CORS 預檢請求成功
[2025-06-02 18:29:57] [FAIL] 前端 API 調用測試: 前端無法調用後端 API: node:internal/modules/cjs/loader:1137
  throw err;
  ^

Error: Cannot find module 'axios'
Require stack:
- /home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/tests/stage1-validation/results/temp/api_test.js
    at Module._resolveFilename (node:internal/modules/cjs/loader:1134:15)
    at Module._load (node:internal/modules/cjs/loader:975:27)
    at Module.require (node:internal/modules/cjs/loader:1225:19)
    at require (node:internal/modules/helpers:177:18)
    at Object.<anonymous> (/home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/tests/stage1-validation/results/temp/api_test.js:1:15)
    at Module._compile (node:internal/modules/cjs/loader:1356:14)
    at Module._extensions..js (node:internal/modules/cjs/loader:1414:10)
    at Module.load (node:internal/modules/cjs/loader:1197:32)
    at Module._load (node:internal/modules/cjs/loader:1013:12)
    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12) {
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/文件/Second_Rebuild_Plan/education-material-converter/tests/stage1-validation/results/temp/api_test.js'
  ]
}

Node.js v18.19.1
[2025-06-02 18:29:57] [PASS] API 響應格式測試: API 返回有效的 JSON 格式
[2025-06-02 18:29:57] [PASS] API 響應結構測試: 響應包含必要的欄位
[2025-06-02 18:29:57] [PASS] 主題 API 格式測試: 主題 API 返回有效的 JSON
[2025-06-02 18:29:57] [PASS] 主題 API 結構測試: 返回主題陣列
[2025-06-02 18:29:57] [PASS] 主題數量測試: 包含 5 個主題
[2025-06-02 18:29:57] [PASS] 404 錯誤處理測試: 不存在的端點返回 404 狀態碼
[2025-06-02 18:30:03] [PASS] 服務器穩定性測試: 服務器在連續請求下保持穩定
[2025-06-02 18:30:03] [PASS] 後端記憶體使用測試: 記憶體使用: 52792KB
[2025-06-02 18:30:03] [PASS] 前端記憶體使用測試: 記憶體使用: 78248KB
