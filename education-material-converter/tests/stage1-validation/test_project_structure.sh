#!/bin/bash

# 項目結構驗證測試模組

test_project_structure() {
    echo "檢查項目目錄結構..."
    
    # 必需的目錄列表
    local required_dirs=(
        "frontend"
        "backend"
        "processor"
        "docs"
        "tests"
        "frontend/src"
        "frontend/src/app"
        "frontend/src/lib"
        "frontend/src/contexts"
        "backend/api"
        "backend/models"
        "backend/utils"
        "backend/venv"
    )
    
    # 必需的檔案列表
    local required_files=(
        "README.md"
        "docker-compose.yml"
        "frontend/package.json"
        "frontend/next.config.ts"
        "frontend/tailwind.config.js"
        "frontend/tsconfig.json"
        "frontend/src/app/layout.tsx"
        "frontend/src/app/page.tsx"
        "frontend/src/lib/api.ts"
        "frontend/src/lib/utils.ts"
        "frontend/src/contexts/AppContext.tsx"
        "backend/main.py"
        "backend/requirements.txt"
        "backend/pyproject.toml"
        "backend/api/__init__.py"
        "backend/api/routes.py"
        "backend/models/__init__.py"
        "backend/models/schemas.py"
        "backend/utils/__init__.py"
        "backend/utils/error_handlers.py"
        "processor/__init__.py"
        "processor/requirements.txt"
    )
    
    # 檢查目錄
    for dir in "${required_dirs[@]}"; do
        local full_path="$PROJECT_ROOT/$dir"
        if [ -d "$full_path" ]; then
            record_test_result "目錄存在: $dir" "PASS" "目錄路徑: $full_path"
        else
            record_test_result "目錄存在: $dir" "FAIL" "目錄不存在: $full_path"
        fi
    done
    
    # 檢查檔案
    for file in "${required_files[@]}"; do
        local full_path="$PROJECT_ROOT/$file"
        if [ -f "$full_path" ]; then
            record_test_result "檔案存在: $file" "PASS" "檔案路徑: $full_path"
        else
            record_test_result "檔案存在: $file" "FAIL" "檔案不存在: $full_path"
        fi
    done
    
    # 檢查 README.md 內容
    if [ -f "$PROJECT_ROOT/README.md" ]; then
        local readme_content=$(cat "$PROJECT_ROOT/README.md")
        if [[ "$readme_content" == *"教育材料轉換系統"* ]]; then
            record_test_result "README.md 內容檢查" "PASS" "包含項目標題"
        else
            record_test_result "README.md 內容檢查" "FAIL" "缺少項目標題"
        fi
    fi
    
    # 檢查 docker-compose.yml 內容
    if [ -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        local compose_content=$(cat "$PROJECT_ROOT/docker-compose.yml")
        if [[ "$compose_content" == *"frontend"* ]] && [[ "$compose_content" == *"backend"* ]]; then
            record_test_result "docker-compose.yml 內容檢查" "PASS" "包含前後端服務配置"
        else
            record_test_result "docker-compose.yml 內容檢查" "FAIL" "缺少服務配置"
        fi
    fi
    
    # 檢查目錄權限
    if [ -w "$PROJECT_ROOT" ]; then
        record_test_result "項目目錄權限檢查" "PASS" "具有寫入權限"
    else
        record_test_result "項目目錄權限檢查" "FAIL" "缺少寫入權限"
    fi
    
    # 檢查 .gitignore 檔案（如果存在）
    if [ -f "$PROJECT_ROOT/.gitignore" ]; then
        local gitignore_content=$(cat "$PROJECT_ROOT/.gitignore")
        if [[ "$gitignore_content" == *"node_modules"* ]] && [[ "$gitignore_content" == *"venv"* ]]; then
            record_test_result ".gitignore 內容檢查" "PASS" "包含必要的忽略規則"
        else
            record_test_result ".gitignore 內容檢查" "FAIL" "缺少必要的忽略規則"
        fi
    else
        record_test_result ".gitignore 檔案檢查" "SKIP" "檔案不存在（可選）"
    fi
}
