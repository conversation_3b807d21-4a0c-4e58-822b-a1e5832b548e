#!/bin/bash

# 測試配置檔案

# 測試超時設置（秒）
FRONTEND_STARTUP_TIMEOUT=60
BACKEND_STARTUP_TIMEOUT=30
API_RESPONSE_TIMEOUT=10

# 端口配置
FRONTEND_PORT=3000
BACKEND_PORT=8000

# 記憶體限制（KB）
MAX_BACKEND_MEMORY=500000   # 500MB
MAX_FRONTEND_MEMORY=1000000 # 1GB

# 必要的 Node.js 版本
MIN_NODE_VERSION=18

# 必要的 Python 版本
MIN_PYTHON_MAJOR=3
MIN_PYTHON_MINOR=11

# 測試重試次數
MAX_RETRIES=3

# 必要的前端依賴包
REQUIRED_FRONTEND_DEPS=(
    "next"
    "react"
    "react-dom"
    "typescript"
    "tailwindcss"
    "axios"
    "react-dropzone"
    "clsx"
    "tailwindcss-merge"
)

# 必要的後端依賴包
REQUIRED_BACKEND_DEPS=(
    "fastapi"
    "uvicorn"
    "python-multipart"
    "pydantic"
    "aiofiles"
    "markdown-it-py"
    "weasyprint"
    "beautifulsoup4"
    "pillow"
)

# API 端點列表
API_ENDPOINTS=(
    "/health"
    "/docs"
    "/api/themes"
)

# 測試檔案路徑
TEST_FILES=(
    "frontend/package.json"
    "frontend/next.config.ts"
    "frontend/tailwind.config.js"
    "frontend/tsconfig.json"
    "backend/main.py"
    "backend/requirements.txt"
    "backend/pyproject.toml"
)

# 測試目錄路徑
TEST_DIRECTORIES=(
    "frontend/src/app"
    "frontend/src/lib"
    "frontend/src/contexts"
    "backend/api"
    "backend/models"
    "backend/utils"
    "backend/venv"
    "processor"
    "docs"
    "tests"
)

# 錯誤信息模板
ERROR_MESSAGES=(
    "NODE_NOT_FOUND:Node.js 未安裝。請安裝 Node.js 18 或更高版本。"
    "PYTHON_NOT_FOUND:Python3 未安裝。請安裝 Python 3.11 或更高版本。"
    "FRONTEND_BUILD_FAILED:前端編譯失敗。請檢查 TypeScript 錯誤並修復。"
    "BACKEND_START_FAILED:後端服務器啟動失敗。請檢查 Python 依賴包和配置。"
    "API_NOT_ACCESSIBLE:API 端點無法訪問。請檢查服務器狀態和網路連接。"
    "CORS_CONFIG_ERROR:CORS 配置錯誤。請檢查後端 CORS 設置。"
)

# 修復建議
FIX_SUGGESTIONS=(
    "INSTALL_NODE:curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
    "INSTALL_PYTHON:sudo apt-get update && sudo apt-get install -y python3.11 python3.11-venv python3-pip"
    "INSTALL_FRONTEND_DEPS:cd frontend && npm install"
    "INSTALL_BACKEND_DEPS:cd backend && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    "FIX_PERMISSIONS:chmod +x tests/stage1-validation/*.sh"
)

# 測試環境變量
export NODE_ENV=test
export PYTHONPATH="$PROJECT_ROOT/backend"
export NEXT_PUBLIC_API_URL="http://localhost:$BACKEND_PORT"
