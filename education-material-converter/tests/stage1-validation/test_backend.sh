#!/bin/bash

# 後端環境測試模組

test_backend_environment() {
    echo "檢查後端開發環境..."
    
    local backend_dir="$PROJECT_ROOT/backend"
    
    # 檢查 Python 是否安裝
    if check_command "python3"; then
        local python_version=$(python3 --version)
        record_test_result "Python 安裝檢查" "PASS" "版本: $python_version"
        
        # 檢查 Python 版本是否符合要求 (>= 3.11)
        local python_version_num=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 11) else 1)"; then
            record_test_result "Python 版本檢查" "PASS" "版本 $python_version 符合要求 (>= 3.11)"
        else
            record_test_result "Python 版本檢查" "FAIL" "版本 $python_version 不符合要求 (需要 >= 3.11)"
        fi
    else
        record_test_result "Python 安裝檢查" "FAIL" "Python3 未安裝"
        return
    fi
    
    # 檢查 pip 是否安裝
    if check_command "pip3"; then
        local pip_version=$(pip3 --version)
        record_test_result "pip 安裝檢查" "PASS" "版本: $pip_version"
    else
        record_test_result "pip 安裝檢查" "FAIL" "pip3 未安裝"
        return
    fi
    
    # 檢查後端目錄
    if [ ! -d "$backend_dir" ]; then
        record_test_result "後端目錄檢查" "FAIL" "後端目錄不存在: $backend_dir"
        return
    fi
    
    # 進入後端目錄
    cd "$backend_dir" || {
        record_test_result "進入後端目錄" "FAIL" "無法進入目錄: $backend_dir"
        return
    }
    
    # 檢查虛擬環境
    if [ -d "venv" ]; then
        record_test_result "虛擬環境檢查" "PASS" "venv 目錄存在"
        
        # 檢查虛擬環境中的 Python
        if [ -f "venv/pyvenv.cfg" ]; then
            record_test_result "虛擬環境配置檢查" "PASS" "pyvenv.cfg 存在"
        else
            record_test_result "虛擬環境配置檢查" "FAIL" "pyvenv.cfg 不存在"
        fi
    else
        record_test_result "虛擬環境檢查" "FAIL" "venv 目錄不存在"
        return
    fi
    
    # 檢查 requirements.txt
    if [ -f "requirements.txt" ]; then
        record_test_result "requirements.txt 檢查" "PASS" "檔案存在"
        
        # 檢查必要的依賴包
        local required_deps=(
            "fastapi"
            "uvicorn"
            "python-multipart"
            "pydantic"
            "aiofiles"
        )
        
        for dep in "${required_deps[@]}"; do
            if grep -q "$dep" requirements.txt; then
                record_test_result "依賴包定義檢查: $dep" "PASS" "已在 requirements.txt 中定義"
            else
                record_test_result "依賴包定義檢查: $dep" "FAIL" "未在 requirements.txt 中找到"
            fi
        done
    else
        record_test_result "requirements.txt 檢查" "FAIL" "檔案不存在"
        return
    fi
    
    # 檢查依賴包是否已安裝
    local venv_site_packages="venv/lib/python*/site-packages"
    if ls $venv_site_packages/fastapi* >/dev/null 2>&1; then
        record_test_result "FastAPI 安裝檢查" "PASS" "FastAPI 已安裝在虛擬環境中"
    else
        record_test_result "FastAPI 安裝檢查" "FAIL" "FastAPI 未安裝"
    fi
    
    if ls $venv_site_packages/uvicorn* >/dev/null 2>&1; then
        record_test_result "Uvicorn 安裝檢查" "PASS" "Uvicorn 已安裝在虛擬環境中"
    else
        record_test_result "Uvicorn 安裝檢查" "FAIL" "Uvicorn 未安裝"
    fi
    
    # 檢查主要 Python 檔案
    if [ -f "main.py" ]; then
        record_test_result "main.py 檢查" "PASS" "主程式檔案存在"
        
        # 檢查 Python 語法
        if python3 -m py_compile main.py 2>/dev/null; then
            record_test_result "main.py 語法檢查" "PASS" "語法正確"
        else
            record_test_result "main.py 語法檢查" "FAIL" "語法錯誤"
        fi
    else
        record_test_result "main.py 檢查" "FAIL" "主程式檔案不存在"
    fi
    
    # 檢查 API 模組
    if [ -f "api/routes.py" ]; then
        record_test_result "API 路由檔案檢查" "PASS" "routes.py 存在"
        
        # 檢查語法
        if python3 -m py_compile api/routes.py 2>/dev/null; then
            record_test_result "API 路由語法檢查" "PASS" "語法正確"
        else
            record_test_result "API 路由語法檢查" "FAIL" "語法錯誤"
        fi
    else
        record_test_result "API 路由檔案檢查" "FAIL" "routes.py 不存在"
    fi
    
    # 檢查數據模型
    if [ -f "models/schemas.py" ]; then
        record_test_result "數據模型檔案檢查" "PASS" "schemas.py 存在"
        
        # 檢查語法
        if python3 -m py_compile models/schemas.py 2>/dev/null; then
            record_test_result "數據模型語法檢查" "PASS" "語法正確"
        else
            record_test_result "數據模型語法檢查" "FAIL" "語法錯誤"
        fi
    else
        record_test_result "數據模型檔案檢查" "FAIL" "schemas.py 不存在"
    fi
    
    # 測試後端服務器啟動
    echo "啟動後端服務器進行測試..."
    
    # 設置環境變量
    export PYTHONPATH="$backend_dir:$backend_dir/venv/lib/python*/site-packages"
    
    # 啟動服務器
    python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 > "$TEMP_DIR/backend_server.log" 2>&1 &
    BACKEND_PID=$!
    
    # 等待服務器啟動
    if wait_for_port 8000 30; then
        record_test_result "後端服務器啟動測試" "PASS" "服務器在端口 8000 成功啟動"
        
        # 測試健康檢查端點
        if curl -s -f "http://localhost:8000/health" > "$TEMP_DIR/health_response.json" 2>/dev/null; then
            record_test_result "健康檢查端點測試" "PASS" "/health 端點可以正常訪問"
            
            # 檢查響應內容
            if grep -q "healthy" "$TEMP_DIR/health_response.json"; then
                record_test_result "健康檢查響應內容" "PASS" "響應包含正確的狀態"
            else
                record_test_result "健康檢查響應內容" "FAIL" "響應內容不正確"
            fi
        else
            record_test_result "健康檢查端點測試" "FAIL" "無法訪問 /health 端點"
        fi
        
        # 測試 API 文檔端點
        if curl -s -f "http://localhost:8000/docs" > "$TEMP_DIR/docs_response.html" 2>/dev/null; then
            record_test_result "API 文檔端點測試" "PASS" "/docs 端點可以正常訪問"
        else
            record_test_result "API 文檔端點測試" "FAIL" "無法訪問 /docs 端點"
        fi
        
        # 測試主要 API 端點
        local api_endpoints=(
            "/api/themes"
        )
        
        for endpoint in "${api_endpoints[@]}"; do
            if curl -s -f "http://localhost:8000$endpoint" > "$TEMP_DIR/api_response_$(basename $endpoint).json" 2>/dev/null; then
                record_test_result "API 端點測試: $endpoint" "PASS" "端點可以正常訪問"
            else
                record_test_result "API 端點測試: $endpoint" "FAIL" "無法訪問端點"
            fi
        done
        
    else
        local server_log=$(tail -n 10 "$TEMP_DIR/backend_server.log")
        record_test_result "後端服務器啟動測試" "FAIL" "服務器啟動失敗或超時: $server_log"
    fi
    
    # 返回項目根目錄
    cd "$PROJECT_ROOT" || true
}
