# 第一階段基礎架構驗證測試

這個測試套件用於驗證「教育材料轉換系統」第一階段基礎架構建立的完整性和正確性。

## 📋 測試範圍

### 1. 項目結構驗證
- ✅ 檢查所有必要的目錄和檔案是否存在
- ✅ 驗證項目配置檔案的完整性
- ✅ 檢查檔案權限和目錄結構

### 2. 前端環境測試
- ✅ Node.js 和 npm 版本檢查
- ✅ package.json 依賴包驗證
- ✅ TypeScript 編譯測試
- ✅ Next.js 開發服務器啟動測試
- ✅ 前端頁面訪問測試

### 3. 後端環境測試
- ✅ Python 版本檢查
- ✅ 虛擬環境驗證
- ✅ requirements.txt 依賴包檢查
- ✅ FastAPI 服務器啟動測試
- ✅ API 端點訪問測試

### 4. 整合測試
- ✅ 前後端同時運行測試
- ✅ CORS 配置驗證
- ✅ API 響應格式測試
- ✅ 服務器穩定性測試

## 🚀 使用方法

### 快速執行

```bash
# 進入測試目錄
cd tests/stage1-validation

# 給腳本執行權限
chmod +x *.sh

# 執行測試
./run_tests.sh
```

### 詳細步驟

1. **準備環境**
   ```bash
   # 確保在項目根目錄
   cd /path/to/education-material-converter
   
   # 進入測試目錄
   cd tests/stage1-validation
   ```

2. **設置權限**
   ```bash
   chmod +x run_tests.sh
   chmod +x test_*.sh
   ```

3. **執行測試**
   ```bash
   ./run_tests.sh
   ```

4. **查看結果**
   ```bash
   # 查看測試報告
   cat results/test_report.md
   
   # 查看詳細日誌
   cat results/test.log
   ```

## 📁 檔案結構

```
tests/stage1-validation/
├── run_tests.sh              # 主測試腳本
├── test_project_structure.sh # 項目結構測試
├── test_frontend.sh          # 前端環境測試
├── test_backend.sh           # 後端環境測試
├── test_integration.sh       # 整合測試
├── test_config.sh            # 測試配置
├── test_helpers.sh           # 輔助函數
├── README.md                 # 說明文檔
└── results/                  # 測試結果目錄
    ├── test_report.md        # 測試報告
    ├── test.log              # 詳細日誌
    └── temp/                 # 臨時檔案
```

## ⚙️ 配置選項

測試腳本支援以下環境變量：

```bash
# 超時設置
export FRONTEND_STARTUP_TIMEOUT=60  # 前端啟動超時（秒）
export BACKEND_STARTUP_TIMEOUT=30   # 後端啟動超時（秒）

# 端口配置
export FRONTEND_PORT=3000           # 前端端口
export BACKEND_PORT=8000            # 後端端口

# 記憶體限制
export MAX_BACKEND_MEMORY=500000    # 後端記憶體限制（KB）
export MAX_FRONTEND_MEMORY=1000000  # 前端記憶體限制（KB）
```

## 🔧 故障排除

### 常見問題

1. **Node.js 版本過低**
   ```bash
   # 安裝 Node.js 18+
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```

2. **Python 版本過低**
   ```bash
   # 安裝 Python 3.11+
   sudo apt-get update
   sudo apt-get install -y python3.11 python3.11-venv python3-pip
   ```

3. **依賴包未安裝**
   ```bash
   # 前端依賴
   cd frontend && npm install
   
   # 後端依賴
   cd backend && python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt
   ```

4. **端口被佔用**
   ```bash
   # 檢查端口使用情況
   netstat -tulpn | grep :3000
   netstat -tulpn | grep :8000
   
   # 終止佔用端口的進程
   sudo kill -9 $(lsof -t -i:3000)
   sudo kill -9 $(lsof -t -i:8000)
   ```

5. **權限問題**
   ```bash
   # 設置腳本執行權限
   chmod +x tests/stage1-validation/*.sh
   
   # 檢查目錄權限
   ls -la tests/stage1-validation/
   ```

### 測試失敗處理

如果測試失敗，請按以下步驟處理：

1. **查看詳細錯誤信息**
   ```bash
   cat tests/stage1-validation/results/test.log
   ```

2. **檢查特定模組**
   ```bash
   # 單獨測試項目結構
   source tests/stage1-validation/test_project_structure.sh
   test_project_structure
   
   # 單獨測試前端
   source tests/stage1-validation/test_frontend.sh
   test_frontend_environment
   ```

3. **重新執行測試**
   ```bash
   # 清理之前的測試結果
   rm -rf tests/stage1-validation/results/
   
   # 重新執行
   ./tests/stage1-validation/run_tests.sh
   ```

## 📊 測試報告

測試完成後會生成以下檔案：

- `results/test_report.md` - Markdown 格式的測試報告
- `results/test.log` - 詳細的測試日誌
- `results/temp/` - 臨時檔案和響應數據

### 報告內容

測試報告包含：
- 📊 測試結果摘要
- 📋 詳細測試結果（按模組分類）
- 🎯 結論和建議
- 📁 相關檔案位置

## 🎯 驗收標準

第一階段基礎架構建立的驗收標準：

- ✅ 所有必要的目錄和檔案存在
- ✅ 前端開發服務器可以成功啟動
- ✅ 後端 API 服務器可以成功啟動
- ✅ 所有依賴包正確安裝
- ✅ TypeScript 編譯無錯誤
- ✅ API 端點可以正常訪問
- ✅ CORS 配置正確
- ✅ 前後端可以同時運行

## 🔄 持續整合

這個測試套件可以整合到 CI/CD 流程中：

```yaml
# GitHub Actions 範例
name: Stage 1 Validation
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Stage 1 Tests
        run: |
          chmod +x tests/stage1-validation/run_tests.sh
          ./tests/stage1-validation/run_tests.sh
```

## 📞 支援

如果遇到問題，請：

1. 檢查本文檔的故障排除部分
2. 查看測試日誌檔案
3. 確認系統環境符合要求
4. 聯繫開發團隊

---

**版本**: 1.0  
**最後更新**: 2024-12-02  
**維護者**: 開發團隊
