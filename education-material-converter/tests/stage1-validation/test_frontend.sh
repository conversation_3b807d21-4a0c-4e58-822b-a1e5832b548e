#!/bin/bash

# 前端環境測試模組

test_frontend_environment() {
    echo "檢查前端開發環境..."
    
    local frontend_dir="$PROJECT_ROOT/frontend"
    
    # 檢查 Node.js 是否安裝
    if check_command "node"; then
        local node_version=$(node --version)
        record_test_result "Node.js 安裝檢查" "PASS" "版本: $node_version"
        
        # 檢查 Node.js 版本是否符合要求 (>= 18)
        local node_major_version=$(echo "$node_version" | sed 's/v\([0-9]*\).*/\1/')
        if [ "$node_major_version" -ge 18 ]; then
            record_test_result "Node.js 版本檢查" "PASS" "版本 $node_version 符合要求 (>= 18)"
        else
            record_test_result "Node.js 版本檢查" "FAIL" "版本 $node_version 不符合要求 (需要 >= 18)"
        fi
    else
        record_test_result "Node.js 安裝檢查" "FAIL" "Node.js 未安裝"
        return
    fi
    
    # 檢查 npm 是否安裝
    if check_command "npm"; then
        local npm_version=$(npm --version)
        record_test_result "npm 安裝檢查" "PASS" "版本: $npm_version"
    else
        record_test_result "npm 安裝檢查" "FAIL" "npm 未安裝"
        return
    fi
    
    # 檢查前端目錄
    if [ ! -d "$frontend_dir" ]; then
        record_test_result "前端目錄檢查" "FAIL" "前端目錄不存在: $frontend_dir"
        return
    fi
    
    # 進入前端目錄
    cd "$frontend_dir" || {
        record_test_result "進入前端目錄" "FAIL" "無法進入目錄: $frontend_dir"
        return
    }
    
    # 檢查 package.json
    if [ -f "package.json" ]; then
        record_test_result "package.json 檢查" "PASS" "檔案存在"
        
        # 檢查必要的依賴包
        local required_deps=(
            "next"
            "react"
            "react-dom"
            "typescript"
            "tailwindcss"
            "axios"
            "react-dropzone"
        )
        
        for dep in "${required_deps[@]}"; do
            if grep -q "\"$dep\"" package.json; then
                record_test_result "依賴包檢查: $dep" "PASS" "已在 package.json 中定義"
            else
                record_test_result "依賴包檢查: $dep" "FAIL" "未在 package.json 中找到"
            fi
        done
    else
        record_test_result "package.json 檢查" "FAIL" "檔案不存在"
        return
    fi
    
    # 檢查 node_modules 是否存在
    if [ -d "node_modules" ]; then
        record_test_result "node_modules 檢查" "PASS" "依賴包已安裝"
        
        # 檢查關鍵依賴包是否實際安裝
        local key_modules=(
            "node_modules/next"
            "node_modules/react"
            "node_modules/typescript"
        )
        
        for module in "${key_modules[@]}"; do
            if [ -d "$module" ]; then
                record_test_result "模組安裝檢查: $(basename $module)" "PASS" "模組已安裝"
            else
                record_test_result "模組安裝檢查: $(basename $module)" "FAIL" "模組未安裝"
            fi
        done
    else
        record_test_result "node_modules 檢查" "FAIL" "依賴包未安裝，請執行 npm install"
        return
    fi
    
    # 檢查 TypeScript 配置
    if [ -f "tsconfig.json" ]; then
        record_test_result "TypeScript 配置檢查" "PASS" "tsconfig.json 存在"
        
        # 檢查 TypeScript 編譯
        if npm run build > "$TEMP_DIR/frontend_build.log" 2>&1; then
            record_test_result "TypeScript 編譯檢查" "PASS" "編譯成功"
        else
            local build_error=$(tail -n 10 "$TEMP_DIR/frontend_build.log")
            record_test_result "TypeScript 編譯檢查" "FAIL" "編譯失敗: $build_error"
        fi
    else
        record_test_result "TypeScript 配置檢查" "FAIL" "tsconfig.json 不存在"
    fi
    
    # 檢查 Tailwind CSS 配置
    if [ -f "tailwind.config.js" ] || [ -f "tailwind.config.ts" ]; then
        record_test_result "Tailwind CSS 配置檢查" "PASS" "配置檔案存在"
    else
        record_test_result "Tailwind CSS 配置檢查" "FAIL" "Tailwind 配置檔案不存在"
    fi
    
    # 測試前端服務器啟動
    echo "啟動前端開發服務器進行測試..."
    npm run dev > "$TEMP_DIR/frontend_server.log" 2>&1 &
    FRONTEND_PID=$!
    
    # 等待服務器啟動
    if wait_for_port 3000 30; then
        record_test_result "前端服務器啟動測試" "PASS" "服務器在端口 3000 成功啟動"
        
        # 測試首頁是否可訪問
        if curl -s -f "http://localhost:3000" > "$TEMP_DIR/frontend_response.html" 2>/dev/null; then
            record_test_result "前端頁面訪問測試" "PASS" "首頁可以正常訪問"
            
            # 檢查頁面內容
            if grep -q "教育材料轉換系統" "$TEMP_DIR/frontend_response.html"; then
                record_test_result "前端頁面內容檢查" "PASS" "頁面包含正確的標題"
            else
                record_test_result "前端頁面內容檢查" "FAIL" "頁面內容不正確"
            fi
        else
            record_test_result "前端頁面訪問測試" "FAIL" "無法訪問首頁"
        fi
    else
        local server_log=$(tail -n 10 "$TEMP_DIR/frontend_server.log")
        record_test_result "前端服務器啟動測試" "FAIL" "服務器啟動失敗或超時: $server_log"
    fi
    
    # 返回項目根目錄
    cd "$PROJECT_ROOT" || true
}
