#!/bin/bash

# 測試輔助函數

# 載入配置
source "$(dirname "${BASH_SOURCE[0]}")/test_config.sh"

# 檢查網路連接
check_network_connectivity() {
    if ping -c 1 google.com >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 檢查端口是否被佔用
check_port_available() {
    local port="$1"
    if ! nc -z localhost "$port" 2>/dev/null; then
        return 0  # 端口可用
    else
        return 1  # 端口被佔用
    fi
}

# 獲取進程的記憶體使用量
get_process_memory() {
    local pid="$1"
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        ps -o rss= -p "$pid" 2>/dev/null || echo "0"
    else
        echo "0"
    fi
}

# 檢查檔案是否包含特定內容
file_contains() {
    local file="$1"
    local pattern="$2"
    if [ -f "$file" ]; then
        grep -q "$pattern" "$file"
    else
        return 1
    fi
}

# 等待檔案出現
wait_for_file() {
    local file="$1"
    local timeout="$2"
    local count=0
    
    while [ $count -lt $timeout ]; do
        if [ -f "$file" ]; then
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    return 1
}

# 檢查 JSON 格式是否有效
is_valid_json() {
    local json_string="$1"
    echo "$json_string" | python3 -m json.tool >/dev/null 2>&1
}

# 從 JSON 中提取值
extract_json_value() {
    local json_string="$1"
    local key="$2"
    echo "$json_string" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('$key', ''))" 2>/dev/null
}

# 檢查版本號是否符合要求
version_compare() {
    local version1="$1"
    local operator="$2"
    local version2="$3"
    
    # 移除版本號前的 'v' 字符
    version1=$(echo "$version1" | sed 's/^v//')
    version2=$(echo "$version2" | sed 's/^v//')
    
    # 使用 Python 進行版本比較
    python3 -c "
import sys
from packaging import version
v1 = version.parse('$version1')
v2 = version.parse('$version2')
if '$operator' == '>=':
    sys.exit(0 if v1 >= v2 else 1)
elif '$operator' == '>':
    sys.exit(0 if v1 > v2 else 1)
elif '$operator' == '<=':
    sys.exit(0 if v1 <= v2 else 1)
elif '$operator' == '<':
    sys.exit(0 if v1 < v2 else 1)
elif '$operator' == '==':
    sys.exit(0 if v1 == v2 else 1)
else:
    sys.exit(1)
" 2>/dev/null
}

# 清理測試進程
cleanup_test_processes() {
    local pattern="$1"
    pkill -f "$pattern" 2>/dev/null || true
    sleep 2
}

# 創建測試檔案
create_test_file() {
    local file_path="$1"
    local content="$2"
    local dir_path=$(dirname "$file_path")
    
    mkdir -p "$dir_path"
    echo "$content" > "$file_path"
}

# 備份檔案
backup_file() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        cp "$file_path" "$file_path.backup.$(date +%s)"
    fi
}

# 恢復檔案
restore_file() {
    local file_path="$1"
    local backup_file=$(ls "$file_path.backup."* 2>/dev/null | tail -n 1)
    if [ -f "$backup_file" ]; then
        mv "$backup_file" "$file_path"
    fi
}

# 檢查磁碟空間
check_disk_space() {
    local path="$1"
    local min_space_mb="$2"
    
    local available_space=$(df "$path" | awk 'NR==2 {print $4}')
    local available_space_mb=$((available_space / 1024))
    
    if [ $available_space_mb -ge $min_space_mb ]; then
        return 0
    else
        return 1
    fi
}

# 檢查系統資源
check_system_resources() {
    local min_memory_mb="$1"
    local min_disk_mb="$2"
    
    # 檢查記憶體
    local total_memory=$(free -m | awk 'NR==2{print $2}')
    if [ $total_memory -lt $min_memory_mb ]; then
        echo "記憶體不足: ${total_memory}MB < ${min_memory_mb}MB"
        return 1
    fi
    
    # 檢查磁碟空間
    if ! check_disk_space "$PROJECT_ROOT" $min_disk_mb; then
        echo "磁碟空間不足"
        return 1
    fi
    
    return 0
}

# 生成隨機測試數據
generate_test_data() {
    local type="$1"
    case "$type" in
        "markdown")
            cat << 'EOF'
# 測試文檔

這是一個測試用的 Markdown 文檔。

## 章節 1

這裡是一些內容。

- 列表項目 1
- 列表項目 2
- 列表項目 3

## 章節 2

這裡有一個表格：

| 欄位 1 | 欄位 2 | 欄位 3 |
|--------|--------|--------|
| 值 1   | 值 2   | 值 3   |

EOF
            ;;
        "json")
            cat << 'EOF'
{
    "test": true,
    "message": "這是測試數據",
    "timestamp": "2024-12-02T10:00:00Z"
}
EOF
            ;;
        *)
            echo "測試數據"
            ;;
    esac
}

# 驗證測試環境
validate_test_environment() {
    local errors=()
    
    # 檢查必要的命令
    local required_commands=("curl" "nc" "python3" "node" "npm")
    for cmd in "${required_commands[@]}"; do
        if ! check_command "$cmd"; then
            errors+=("缺少命令: $cmd")
        fi
    done
    
    # 檢查系統資源
    if ! check_system_resources 1024 1024; then  # 1GB RAM, 1GB disk
        errors+=("系統資源不足")
    fi
    
    # 檢查項目目錄
    if [ ! -d "$PROJECT_ROOT" ]; then
        errors+=("項目根目錄不存在: $PROJECT_ROOT")
    fi
    
    # 檢查寫入權限
    if [ ! -w "$PROJECT_ROOT" ]; then
        errors+=("項目目錄沒有寫入權限")
    fi
    
    if [ ${#errors[@]} -gt 0 ]; then
        echo "測試環境驗證失敗："
        for error in "${errors[@]}"; do
            echo "  - $error"
        done
        return 1
    fi
    
    return 0
}

# 獲取錯誤信息
get_error_message() {
    local error_code="$1"
    for msg in "${ERROR_MESSAGES[@]}"; do
        if [[ "$msg" == "$error_code:"* ]]; then
            echo "${msg#*:}"
            return 0
        fi
    done
    echo "未知錯誤: $error_code"
}

# 獲取修復建議
get_fix_suggestion() {
    local fix_code="$1"
    for suggestion in "${FIX_SUGGESTIONS[@]}"; do
        if [[ "$suggestion" == "$fix_code:"* ]]; then
            echo "${suggestion#*:}"
            return 0
        fi
    done
    echo "無可用的修復建議"
}
