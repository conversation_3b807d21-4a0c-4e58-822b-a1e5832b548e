"""
API 路由定義
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from typing import List, Optional
from models.schemas import ConvertRequest, TaskStatus, ConversionResponse, ThemeInfo
import uuid
import os

router = APIRouter()

# 模擬任務存儲（實際應用中應使用數據庫或 Redis）
tasks_storage = {}

@router.post("/upload", response_model=dict)
async def upload_files(files: List[UploadFile] = File(...)):
    """
    檔案上傳端點
    支援單檔案和多檔案上傳
    """
    try:
        uploaded_files = []
        upload_dir = "uploads"
        os.makedirs(upload_dir, exist_ok=True)
        
        for file in files:
            # 驗證檔案類型
            if not file.filename.endswith(('.md', '.markdown')):
                raise HTTPException(
                    status_code=400, 
                    detail=f"不支援的檔案類型: {file.filename}"
                )
            
            # 生成唯一檔案名
            file_id = str(uuid.uuid4())
            file_extension = os.path.splitext(file.filename)[1]
            saved_filename = f"{file_id}{file_extension}"
            file_path = os.path.join(upload_dir, saved_filename)
            
            # 保存檔案
            content = await file.read()
            with open(file_path, "wb") as f:
                f.write(content)
            
            uploaded_files.append({
                "file_id": file_id,
                "original_name": file.filename,
                "saved_path": file_path,
                "size": len(content)
            })
        
        return {
            "message": "檔案上傳成功",
            "files": uploaded_files,
            "total_files": len(uploaded_files)
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"檔案上傳失敗: {str(e)}")

@router.post("/convert", response_model=ConversionResponse)
async def convert_files(
    request: ConvertRequest,
    background_tasks: BackgroundTasks
):
    """
    轉換請求端點
    啟動 Markdown 到 HTML/PDF 的轉換任務
    """
    try:
        # 生成任務 ID
        task_id = str(uuid.uuid4())
        
        # 創建任務狀態
        task_status = TaskStatus(
            task_id=task_id,
            status="pending",
            progress=0,
            result_files=None,
            error_message=None
        )
        
        # 存儲任務狀態
        tasks_storage[task_id] = task_status
        
        # 添加背景任務（實際轉換邏輯）
        background_tasks.add_task(process_conversion, task_id, request)
        
        return ConversionResponse(
            task_id=task_id,
            message="轉換任務已啟動",
            status="pending"
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"轉換任務啟動失敗: {str(e)}")

@router.get("/status/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """
    任務狀態查詢端點
    """
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="任務不存在")
    
    return tasks_storage[task_id]

@router.get("/download/{task_id}/{format}")
async def download_file(task_id: str, format: str):
    """
    檔案下載端點
    支援 HTML 和 PDF 格式下載
    """
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="任務不存在")
    
    task = tasks_storage[task_id]
    
    if task.status != "completed":
        raise HTTPException(status_code=400, detail="任務尚未完成")
    
    if not task.result_files:
        raise HTTPException(status_code=404, detail="結果檔案不存在")
    
    # 根據格式查找對應檔案
    file_path = None
    for file_info in task.result_files:
        if file_info.get("format") == format:
            file_path = file_info.get("path")
            break
    
    if not file_path or not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"{format.upper()} 檔案不存在")
    
    return FileResponse(
        path=file_path,
        filename=f"converted_{task_id}.{format}",
        media_type="application/octet-stream"
    )

@router.get("/themes", response_model=List[ThemeInfo])
async def get_themes():
    """
    獲取可用主題列表
    """
    themes = [
        ThemeInfo(
            id="default",
            name="預設主題",
            description="簡潔的藍色圓形主題",
            preview_url="/themes/default/preview.png"
        ),
        ThemeInfo(
            id="nature",
            name="自然主題",
            description="綠色六角形主題",
            preview_url="/themes/nature/preview.png"
        ),
        ThemeInfo(
            id="space",
            name="太空主題",
            description="紫色星形主題",
            preview_url="/themes/space/preview.png"
        ),
        ThemeInfo(
            id="tech",
            name="科技主題",
            description="青色方形主題",
            preview_url="/themes/tech/preview.png"
        ),
        ThemeInfo(
            id="sport",
            name="運動主題",
            description="紅色徽章主題",
            preview_url="/themes/sport/preview.png"
        )
    ]
    
    return themes

async def process_conversion(task_id: str, request: ConvertRequest):
    """
    背景任務：處理轉換邏輯
    """
    try:
        # 更新任務狀態為處理中
        tasks_storage[task_id].status = "processing"
        tasks_storage[task_id].progress = 10
        
        # 這裡將實現實際的轉換邏輯
        # 目前只是模擬處理過程
        
        # 模擬處理進度
        import asyncio
        await asyncio.sleep(2)
        tasks_storage[task_id].progress = 50
        
        await asyncio.sleep(2)
        tasks_storage[task_id].progress = 80
        
        await asyncio.sleep(1)
        
        # 模擬完成
        tasks_storage[task_id].status = "completed"
        tasks_storage[task_id].progress = 100
        tasks_storage[task_id].result_files = [
            {"format": "html", "path": f"output/{task_id}.html"},
            {"format": "pdf", "path": f"output/{task_id}.pdf"}
        ]
        
    except Exception as e:
        tasks_storage[task_id].status = "failed"
        tasks_storage[task_id].error_message = str(e)
