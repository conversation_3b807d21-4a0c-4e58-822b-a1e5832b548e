"""
錯誤處理機制
"""

from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from typing import Union
from models.schemas import ErrorResponse

# 配置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def http_exception_handler(request: Request, exc: HTTPException):
    """
    HTTP 異常處理器
    """
    logger.error(f"HTTP Exception: {exc.status_code} - {exc.detail}")
    
    error_response = ErrorResponse(
        error="HTTPException",
        message=exc.detail,
        details={
            "status_code": exc.status_code,
            "path": str(request.url)
        }
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.dict()
    )

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    請求驗證異常處理器
    """
    logger.error(f"Validation Error: {exc.errors()}")
    
    error_response = ErrorResponse(
        error="ValidationError",
        message="請求參數驗證失敗",
        details={
            "errors": exc.errors(),
            "path": str(request.url)
        }
    )
    
    return JSONResponse(
        status_code=422,
        content=error_response.dict()
    )

async def general_exception_handler(request: Request, exc: Exception):
    """
    通用異常處理器
    """
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    
    error_response = ErrorResponse(
        error="InternalServerError",
        message="服務器內部錯誤",
        details={
            "path": str(request.url)
        }
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.dict()
    )

class ConversionError(Exception):
    """
    轉換過程中的自定義異常
    """
    def __init__(self, message: str, error_code: str = "CONVERSION_ERROR"):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class FileProcessingError(Exception):
    """
    檔案處理過程中的自定義異常
    """
    def __init__(self, message: str, file_name: str = None):
        self.message = message
        self.file_name = file_name
        super().__init__(self.message)

class ThemeError(Exception):
    """
    主題相關的自定義異常
    """
    def __init__(self, message: str, theme_id: str = None):
        self.message = message
        self.theme_id = theme_id
        super().__init__(self.message)

def setup_error_handlers(app):
    """
    設置錯誤處理器
    """
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
