"""
教育材料轉換系統 - FastAPI 後端主程式
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.routes import router as api_router
from utils.error_handlers import setup_error_handlers

# 創建 FastAPI 應用實例
app = FastAPI(
    title="教育材料轉換系統 API",
    description="將 Markdown 教育材料轉換為 HTML 和 PDF 的後端服務",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置 CORS 中間件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端開發服務器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 設置錯誤處理器
setup_error_handlers(app)

# 註冊 API 路由
app.include_router(api_router, prefix="/api")

# 健康檢查端點
@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {"status": "healthy", "message": "教育材料轉換系統後端服務運行正常"}

# 根端點
@app.get("/")
async def root():
    """根端點"""
    return {
        "message": "歡迎使用教育材料轉換系統 API",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
