"""
簡化的測試主程式
"""

from fastapi import FastAPI

# 創建 FastAPI 應用實例
app = FastAPI(
    title="教育材料轉換系統 API",
    description="將 Markdown 教育材料轉換為 HTML 和 PDF 的後端服務",
    version="0.1.0",
)

# 健康檢查端點
@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {"status": "healthy", "message": "教育材料轉換系統後端服務運行正常"}

# 根端點
@app.get("/")
async def root():
    """根端點"""
    return {
        "message": "歡迎使用教育材料轉換系統 API",
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "test_main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
