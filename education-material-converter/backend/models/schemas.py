"""
API 數據模型和 Schema 定義
"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum

class TaskStatusEnum(str, Enum):
    """任務狀態枚舉"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class PaginationModeEnum(str, Enum):
    """分頁模式枚舉"""
    BALANCED = "balanced"
    COMPACT = "compact"
    INTEGRITY = "integrity"

class ConvertRequest(BaseModel):
    """轉換請求模型"""
    files: List[str] = Field(..., description="要轉換的檔案 ID 列表")
    theme: str = Field(default="default", description="主題 ID")
    options: Dict[str, Any] = Field(default_factory=dict, description="轉換選項")
    
    class Config:
        json_schema_extra = {
            "example": {
                "files": ["file-uuid-1", "file-uuid-2"],
                "theme": "default",
                "options": {
                    "pagination_mode": "balanced",
                    "include_toc": True,
                    "math_rendering": True,
                    "exercise_processing": True
                }
            }
        }

class TaskStatus(BaseModel):
    """任務狀態模型"""
    task_id: str = Field(..., description="任務 ID")
    status: TaskStatusEnum = Field(..., description="任務狀態")
    progress: int = Field(default=0, ge=0, le=100, description="進度百分比")
    result_files: Optional[List[Dict[str, str]]] = Field(None, description="結果檔案列表")
    error_message: Optional[str] = Field(None, description="錯誤信息")
    created_at: Optional[str] = Field(None, description="創建時間")
    updated_at: Optional[str] = Field(None, description="更新時間")
    
    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "task-uuid-123",
                "status": "completed",
                "progress": 100,
                "result_files": [
                    {"format": "html", "path": "output/task-uuid-123.html"},
                    {"format": "pdf", "path": "output/task-uuid-123.pdf"}
                ],
                "error_message": None,
                "created_at": "2024-12-02T10:00:00Z",
                "updated_at": "2024-12-02T10:05:00Z"
            }
        }

class ConversionResponse(BaseModel):
    """轉換響應模型"""
    task_id: str = Field(..., description="任務 ID")
    message: str = Field(..., description="響應信息")
    status: TaskStatusEnum = Field(..., description="初始狀態")
    
    class Config:
        json_schema_extra = {
            "example": {
                "task_id": "task-uuid-123",
                "message": "轉換任務已啟動",
                "status": "pending"
            }
        }

class ThemeInfo(BaseModel):
    """主題信息模型"""
    id: str = Field(..., description="主題 ID")
    name: str = Field(..., description="主題名稱")
    description: str = Field(..., description="主題描述")
    preview_url: str = Field(..., description="預覽圖片 URL")
    
    class Config:
        json_schema_extra = {
            "example": {
                "id": "default",
                "name": "預設主題",
                "description": "簡潔的藍色圓形主題",
                "preview_url": "/themes/default/preview.png"
            }
        }

class FileInfo(BaseModel):
    """檔案信息模型"""
    file_id: str = Field(..., description="檔案 ID")
    original_name: str = Field(..., description="原始檔案名")
    size: int = Field(..., description="檔案大小（字節）")
    upload_time: Optional[str] = Field(None, description="上傳時間")
    
    class Config:
        json_schema_extra = {
            "example": {
                "file_id": "file-uuid-123",
                "original_name": "lesson1.md",
                "size": 2048,
                "upload_time": "2024-12-02T10:00:00Z"
            }
        }

class ConversionOptions(BaseModel):
    """轉換選項模型"""
    pagination_mode: PaginationModeEnum = Field(
        default=PaginationModeEnum.BALANCED,
        description="分頁模式"
    )
    include_toc: bool = Field(default=True, description="是否包含目錄")
    math_rendering: bool = Field(default=True, description="是否渲染數學公式")
    exercise_processing: bool = Field(default=True, description="是否處理練習區塊")
    table_enhancement: bool = Field(default=True, description="是否增強表格")
    image_optimization: bool = Field(default=True, description="是否優化圖片")
    
    class Config:
        json_schema_extra = {
            "example": {
                "pagination_mode": "balanced",
                "include_toc": True,
                "math_rendering": True,
                "exercise_processing": True,
                "table_enhancement": True,
                "image_optimization": True
            }
        }

class ErrorResponse(BaseModel):
    """錯誤響應模型"""
    error: str = Field(..., description="錯誤類型")
    message: str = Field(..., description="錯誤信息")
    details: Optional[Dict[str, Any]] = Field(None, description="錯誤詳情")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": "ValidationError",
                "message": "請求參數驗證失敗",
                "details": {
                    "field": "files",
                    "issue": "檔案列表不能為空"
                }
            }
        }
