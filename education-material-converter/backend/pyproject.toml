[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "education-material-converter-backend"
version = "0.1.0"
description = "Backend service for converting educational materials from Markdown to HTML and PDF"
authors = [
    {name = "Development Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "pydantic>=2.5.0",
    "aiofiles>=23.2.1",
    "markdown-it-py>=3.0.0",
    "beautifulsoup4>=4.12.2",
    "pillow>=10.1.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0"
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88
