# 教育材料轉換系統 - 快速開始指南

## 🚀 5分鐘快速上手

### 第一步：安裝依賴
```bash
pip install markdown-it-py beautifulsoup4
```

### 第二步：基本使用
```bash
# 轉換單個檔案
python main_converter.py input.md

# 使用自然主題
python main_converter.py input.md -t nature

# 批量轉換資料夾
python main_converter.py folder/ -b
```

### 第三步：檢查結果
轉換完成後，會在同一目錄生成對應的 `.html` 檔案。

## 📝 常用命令

### 基本轉換
```bash
# 最簡單的使用方式
python main_converter.py my_document.md
```

### 指定輸出檔案
```bash
python main_converter.py input.md -o custom_output.html
```

### 選擇主題
```bash
# 5種主題可選：default, nature, tech, space, sport
python main_converter.py input.md -t tech
```

### 批量處理
```bash
# 處理整個資料夾
python main_converter.py documents/ -b -t nature
```

### PDF 優化模式
```bash
# 適合列印的樣式
python main_converter.py input.md --pdf-mode
```

### 功能控制
```bash
# 禁用數學公式
python main_converter.py input.md --no-math

# 禁用圖片處理
python main_converter.py input.md --no-images

# 靜默模式
python main_converter.py input.md --quiet
```

## 🎯 支援的 Markdown 語法

### 基本語法
```markdown
# 一級標題
## 二級標題
### 三級標題

**粗體文字**
*斜體文字*
~~刪除線~~

- 無序列表項目1
- 無序列表項目2

1. 有序列表項目1
2. 有序列表項目2

[連結文字](https://example.com)
```

### 數學公式
```markdown
行內公式：$E = mc^2$

顯示公式：
$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$

複雜公式：
$$\begin{pmatrix} a & b \\ c & d \end{pmatrix} \begin{pmatrix} x \\ y \end{pmatrix} = \begin{pmatrix} ax + by \\ cx + dy \end{pmatrix}$$
```

### 圖片處理
```markdown
# 基本圖片
![圖片描述](path/to/image.jpg)

# 指定寬度
![圖片描述](path/to/image.jpg|width=400)

# 居中顯示
![圖片描述](path/to/image.jpg|center)

# 組合參數
![圖片描述](path/to/image.jpg|width=300|center)

# 左浮動
![圖片描述](path/to/image.jpg|width=200|left)

# 右浮動
![圖片描述](path/to/image.jpg|width=200|right)
```

### 教育特殊元素
```markdown
# 填空線（4個或以上下劃線）
姓名：____________________
年齡：____________________

# 水平線（連續的會統一為4條）
---
---
---

# 表格
| 名稱 | 年齡 | 城市 |
|------|------|------|
| 張三 | 25   | 台北 |
| 李四 | 30   | 高雄 |
```

## 🎨 主題預覽

### default - 經典主題
- 主色：專業藍色
- 適用：正式文檔、學術材料

### nature - 自然主題 🌿
- 主色：清新綠色
- 適用：環境科學、生物學

### tech - 科技主題 ⚡
- 主色：現代紫色
- 適用：程式設計、工程學

### space - 太空主題 🚀
- 主色：深邃藍色
- 適用：天文學、物理學

### sport - 運動主題 ⚽
- 主色：活力紅色
- 適用：體育、健康教育

## 🐍 Python API 使用

### 簡單使用
```python
from main_converter import convert_string

markdown_content = """
# 我的文檔

這是一個包含數學公式的文檔：$E = mc^2$

![圖片](image.jpg|width=300|center)
"""

result = convert_string(markdown_content, theme="nature")

if result.success:
    print("轉換成功！")
    with open("output.html", "w", encoding="utf-8") as f:
        f.write(result.html_content)
else:
    print("轉換失敗：", result.errors)
```

### 檔案轉換
```python
from main_converter import convert_file

result = convert_file(
    "input.md", 
    "output.html",
    theme="tech",
    pdf_mode=True,
    enable_math=True
)

print(f"轉換結果: {result.get_summary()}")
```

## 🔧 常見問題

### Q: 數學公式不顯示？
A: 確保：
1. 使用正確的 LaTeX 語法
2. 沒有使用 `--no-math` 參數
3. 沒有使用 `--no-mathjax` 參數

### Q: 圖片不顯示？
A: 檢查：
1. 圖片路徑是否正確
2. 圖片檔案是否存在
3. 使用 `--image-base-path` 設置基礎路徑

### Q: 主題樣式不生效？
A: 確認：
1. 主題名稱拼寫正確
2. 沒有使用 `--no-themes` 參數
3. 沒有使用 `--no-css` 參數

### Q: 批量處理失敗？
A: 檢查：
1. 使用 `-b` 或 `--batch` 參數
2. 輸入路徑是資料夾
3. 資料夾中包含 `.md` 檔案

## 📞 獲取幫助

### 查看完整選項
```bash
python main_converter.py --help
```

### 啟用調試模式
```bash
python main_converter.py input.md --debug
```

### 檢查版本
```bash
python main_converter.py --version
```

## 🎯 下一步

1. **閱讀完整文檔**: `README_unified_converter.md`
2. **了解項目結構**: `PROJECT_STRUCTURE.md`
3. **運行測試**: `python test_unified_converter.py`
4. **探索高級功能**: 自定義配置、Python API
5. **參與開發**: 添加新主題、擴展功能

---

**開始你的教育材料轉換之旅吧！** 🚀
