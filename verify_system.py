#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育材料轉換系統 - 系統驗證腳本

這個腳本用於驗證清理後的系統是否正常運作。
執行基本的功能測試，確保核心功能完整可用。
"""

import os
import sys
import tempfile
from pathlib import Path

def test_import():
    """測試核心模組導入"""
    print("🔍 測試模組導入...")
    try:
        from main_converter import convert_string, convert_file
        from markdown_to_html_unified import UnifiedMarkdownConverter, ConversionConfig
        print("✅ 核心模組導入成功")
        return True
    except ImportError as e:
        print(f"❌ 模組導入失敗: {e}")
        return False

def test_basic_conversion():
    """測試基本轉換功能"""
    print("🔍 測試基本轉換功能...")
    try:
        from main_converter import convert_string
        
        test_content = """# 測試文檔

這是一個簡單的測試文檔。

## 數學公式測試
行內公式：$E = mc^2$

顯示公式：
$$\\int_{0}^{1} x^2 dx = \\frac{1}{3}$$

## 圖片測試
![測試圖片](test.jpg|width=200|center)

## 填空線測試
姓名：____________________

## 列表測試
- 項目 1
- 項目 2
- 項目 3
"""
        
        result = convert_string(test_content, theme="default")
        
        if result.success:
            print("✅ 基本轉換功能正常")
            print(f"   📊 統計: {result.statistics}")
            return True
        else:
            print(f"❌ 轉換失敗: {result.errors}")
            return False
            
    except Exception as e:
        print(f"❌ 轉換測試異常: {e}")
        return False

def test_themes():
    """測試主題系統"""
    print("🔍 測試主題系統...")
    try:
        from main_converter import convert_string
        
        themes = ["default", "nature", "tech", "space", "sport"]
        test_content = "# 主題測試\n\n這是主題測試文檔。\n\n- 列表項目 1\n- 列表項目 2"
        
        success_count = 0
        for theme in themes:
            result = convert_string(test_content, theme=theme)
            if result.success:
                success_count += 1
        
        if success_count == len(themes):
            print(f"✅ 所有 {len(themes)} 個主題測試通過")
            return True
        else:
            print(f"⚠️  {success_count}/{len(themes)} 個主題測試通過")
            return success_count > 0
            
    except Exception as e:
        print(f"❌ 主題測試異常: {e}")
        return False

def test_file_conversion():
    """測試檔案轉換功能"""
    print("🔍 測試檔案轉換功能...")
    try:
        from main_converter import convert_file
        
        # 創建臨時測試檔案
        test_content = """# 檔案轉換測試

這是檔案轉換測試。

## 功能驗證
- [x] Markdown 解析
- [x] HTML 生成
- [x] 檔案保存

測試完成！
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_md_path = f.name
        
        temp_html_path = temp_md_path.replace('.md', '.html')
        
        try:
            result = convert_file(temp_md_path, temp_html_path, theme="nature")
            
            if result.success and os.path.exists(temp_html_path):
                print("✅ 檔案轉換功能正常")
                return True
            else:
                print(f"❌ 檔案轉換失敗: {result.errors}")
                return False
        finally:
            # 清理臨時檔案
            for path in [temp_md_path, temp_html_path]:
                if os.path.exists(path):
                    os.unlink(path)
                    
    except Exception as e:
        print(f"❌ 檔案轉換測試異常: {e}")
        return False

def test_command_line():
    """測試命令行功能"""
    print("🔍 測試命令行功能...")
    try:
        import subprocess
        
        # 測試幫助信息
        result = subprocess.run([sys.executable, "main_converter.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "教育材料轉換系統" in result.stdout:
            print("✅ 命令行功能正常")
            return True
        else:
            print("❌ 命令行功能異常")
            return False
            
    except Exception as e:
        print(f"❌ 命令行測試異常: {e}")
        return False

def check_file_structure():
    """檢查檔案結構"""
    print("🔍 檢查檔案結構...")
    
    required_files = [
        "main_converter.py",
        "markdown_to_html_unified.py",
        "requirements_unified.txt",
        "setup.py",
        "README_unified_converter.md",
        "PROJECT_STRUCTURE.md",
        "QUICK_START.md"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if not missing_files:
        print("✅ 核心檔案結構完整")
        return True
    else:
        print(f"❌ 缺少檔案: {missing_files}")
        return False

def main():
    """主驗證函數"""
    print("🚀 教育材料轉換系統 - 系統驗證")
    print("=" * 50)
    
    tests = [
        ("檔案結構檢查", check_file_structure),
        ("模組導入測試", test_import),
        ("基本轉換測試", test_basic_conversion),
        ("主題系統測試", test_themes),
        ("檔案轉換測試", test_file_conversion),
        ("命令行功能測試", test_command_line)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        if test_func():
            passed += 1
        else:
            print(f"   ⚠️  {test_name} 未通過")
    
    print("\n" + "=" * 50)
    print(f"📊 驗證結果: {passed}/{total} 項測試通過")
    
    if passed == total:
        print("🎉 系統驗證完全通過！所有功能正常運作。")
        return True
    elif passed >= total * 0.8:
        print("⚠️  系統基本正常，但有部分功能需要檢查。")
        return True
    else:
        print("❌ 系統存在重要問題，需要修復。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
