#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主題配置檔案載入器
====================

此模組負責載入和解析主題配置檔案（theme_config.yaml），
並將其轉換為系統可用的主題字典格式。

功能特色：
- 支援 YAML 格式的主題配置檔案
- 自動驗證配置檔案的完整性
- 提供預設值和錯誤處理
- 支援熱重載配置檔案
- 生成與舊版系統兼容的主題字典

作者：教育材料轉換系統
版本：1.0.0
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import os

# 設定日誌
logger = logging.getLogger(__name__)

class ThemeConfigLoader:
    """主題配置檔案載入器"""
    
    def __init__(self, config_file: str = "theme_config.yaml"):
        """
        初始化配置載入器
        
        Args:
            config_file: 配置檔案路徑
        """
        self.config_file = Path(config_file)
        self.config_data = None
        self.themes = {}
        
    def load_config(self) -> Dict[str, Any]:
        """
        載入主題配置檔案
        
        Returns:
            Dict: 配置數據
            
        Raises:
            FileNotFoundError: 配置檔案不存在
            yaml.YAMLError: YAML 格式錯誤
        """
        try:
            if not self.config_file.exists():
                logger.warning(f"配置檔案不存在: {self.config_file}")
                return self._get_default_config()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f)
            
            logger.info(f"成功載入配置檔案: {self.config_file}")
            return self.config_data
            
        except yaml.YAMLError as e:
            logger.error(f"YAML 格式錯誤: {e}")
            return self._get_default_config()
        except Exception as e:
            logger.error(f"載入配置檔案失敗: {e}")
            return self._get_default_config()
    
    def get_themes(self) -> Dict[str, Dict]:
        """
        獲取轉換後的主題字典
        
        Returns:
            Dict: 與舊版系統兼容的主題字典
        """
        if not self.config_data:
            self.load_config()
        
        if not self.config_data or 'themes' not in self.config_data:
            logger.warning("配置檔案中沒有找到主題定義，使用預設主題")
            return self._get_default_themes()
        
        converted_themes = {}
        
        for theme_name, theme_config in self.config_data['themes'].items():
            converted_themes[theme_name] = self._convert_theme_config(theme_config)
        
        logger.info(f"成功載入 {len(converted_themes)} 個主題: {list(converted_themes.keys())}")
        return converted_themes
    
    def _convert_theme_config(self, theme_config: Dict) -> Dict:
        """
        將新格式的主題配置轉換為舊版系統格式
        
        Args:
            theme_config: 新格式的主題配置
            
        Returns:
            Dict: 舊版格式的主題配置
        """
        colors = theme_config.get('colors', {})
        gradients = theme_config.get('gradients', {})
        shadows = theme_config.get('shadows', {})
        list_icons = theme_config.get('list_icons', {})
        educational = theme_config.get('educational_elements', {})
        
        return {
            "name": theme_config.get('name', '未命名主題'),
            "description": theme_config.get('description', ''),
            "category": theme_config.get('category', 'general'),
            
            # 顏色系統
            "primary_color": colors.get('primary', '#2563eb'),
            "secondary_color": colors.get('secondary', '#64748b'),
            "accent_color": colors.get('accent', '#f59e0b'),
            "background_color": colors.get('background', '#ffffff'),
            "text_color": colors.get('text', '#1f2937'),
            "border_color": colors.get('border', '#e5e7eb'),
            
            # 漸變和效果
            "gradient_primary": gradients.get('primary', 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)'),
            "gradient_accent": gradients.get('accent', 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'),
            "shadow_primary": shadows.get('primary', '0 4px 12px rgba(37, 99, 235, 0.15)'),
            "shadow_accent": shadows.get('accent', '0 2px 8px rgba(245, 158, 11, 0.2)'),
            
            # 列表圖標系統
            "list_icons": {
                "primary": list_icons.get('level_1', '🔵'),
                "secondary": list_icons.get('level_2', '🔹'),
                "tertiary": list_icons.get('level_3', '▪️'),
                "quaternary": list_icons.get('level_4', '•'),
                "quinary": list_icons.get('level_5', '‣')
            },
            
            # 教育元素樣式
            "fill_blank_style": educational.get('fill_blank_style', 'solid'),
            "math_bg_color": educational.get('math_bg_color', '#f8fafc'),
            "code_bg_color": educational.get('code_bg_color', '#f1f5f9'),
            "quote_bg_color": educational.get('quote_bg_color', '#eff6ff')
        }
    
    def get_global_settings(self) -> Dict[str, Any]:
        """
        獲取全域設定
        
        Returns:
            Dict: 全域設定
        """
        if not self.config_data:
            self.load_config()
        
        return self.config_data.get('global_settings', {})
    
    def get_company_logo_config(self) -> Dict[str, Any]:
        """
        獲取公司 Logo 配置
        
        Returns:
            Dict: Logo 配置
        """
        global_settings = self.get_global_settings()
        return global_settings.get('company_logo', {
            'enabled': True,
            'path': 'TestFile/Company icon.png',
            'position': 'bottom-right',
            'size': {'width': '3cm', 'max_height': '1.5cm'},
            'opacity': 0.8,
            'margin': {'bottom': '0.5cm', 'right': '1cm'}
        })
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取預設配置"""
        return {
            'themes': self._get_default_themes(),
            'global_settings': {
                'company_logo': {
                    'enabled': True,
                    'path': 'TestFile/Company icon.png',
                    'position': 'bottom-right',
                    'size': {'width': '3cm', 'max_height': '1.5cm'},
                    'opacity': 0.8,
                    'margin': {'bottom': '0.5cm', 'right': '1cm'}
                }
            }
        }
    
    def _get_default_themes(self) -> Dict[str, Dict]:
        """獲取預設主題（作為備用）"""
        return {
            "default": {
                "name": "經典主題",
                "description": "專業的藍色主題，適合正式文檔和商務材料",
                "category": "professional",
                "primary_color": "#1e40af",
                "secondary_color": "#374151",
                "accent_color": "#d97706",
                "background_color": "#ffffff",
                "text_color": "#111827",
                "border_color": "#e5e7eb",
                "gradient_primary": "linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%)",
                "gradient_accent": "linear-gradient(135deg, #d97706 0%, #b45309 100%)",
                "shadow_primary": "0 4px 12px rgba(30, 64, 175, 0.15)",
                "shadow_accent": "0 2px 8px rgba(217, 119, 6, 0.2)",
                "list_icons": {
                    "primary": "🔵",
                    "secondary": "🔹",
                    "tertiary": "▪️",
                    "quaternary": "•",
                    "quinary": "‣"
                },
                "fill_blank_style": "solid",
                "math_bg_color": "#f8fafc",
                "code_bg_color": "#f1f5f9",
                "quote_bg_color": "#eff6ff"
            }
        }
    
    def validate_config(self) -> bool:
        """
        驗證配置檔案的完整性
        
        Returns:
            bool: 配置是否有效
        """
        if not self.config_data:
            return False
        
        required_sections = ['themes']
        for section in required_sections:
            if section not in self.config_data:
                logger.error(f"配置檔案缺少必要區段: {section}")
                return False
        
        # 驗證主題配置
        themes = self.config_data.get('themes', {})
        if not themes:
            logger.error("配置檔案中沒有定義任何主題")
            return False
        
        for theme_name, theme_config in themes.items():
            if not self._validate_theme_config(theme_name, theme_config):
                return False
        
        logger.info("配置檔案驗證通過")
        return True
    
    def _validate_theme_config(self, theme_name: str, theme_config: Dict) -> bool:
        """
        驗證單個主題配置
        
        Args:
            theme_name: 主題名稱
            theme_config: 主題配置
            
        Returns:
            bool: 主題配置是否有效
        """
        required_sections = ['colors', 'list_icons', 'educational_elements']
        
        for section in required_sections:
            if section not in theme_config:
                logger.warning(f"主題 '{theme_name}' 缺少區段: {section}")
                # 不返回 False，允許使用預設值
        
        return True

# 全域配置載入器實例
config_loader = ThemeConfigLoader()

def get_themes_from_config() -> Dict[str, Dict]:
    """
    從配置檔案獲取主題
    
    Returns:
        Dict: 主題字典
    """
    return config_loader.get_themes()

def get_company_logo_config() -> Dict[str, Any]:
    """
    獲取公司 Logo 配置
    
    Returns:
        Dict: Logo 配置
    """
    return config_loader.get_company_logo_config()

if __name__ == "__main__":
    # 測試配置載入器
    loader = ThemeConfigLoader()
    config = loader.load_config()
    themes = loader.get_themes()
    
    print("=== 配置載入測試 ===")
    print(f"載入的主題數量: {len(themes)}")
    print(f"主題列表: {list(themes.keys())}")
    
    # 驗證配置
    is_valid = loader.validate_config()
    print(f"配置檔案有效性: {is_valid}")
    
    # 顯示公司 Logo 配置
    logo_config = loader.get_company_logo_config()
    print(f"Logo 配置: {logo_config}")
