# 教育材料轉換系統 - 統一 Markdown 到 HTML 轉換器

[![版本](https://img.shields.io/badge/版本-3.0.0-blue.svg)](https://github.com/education-converter/unified-converter)
[![Python](https://img.shields.io/badge/Python-3.8+-green.svg)](https://python.org)
[![許可證](https://img.shields.io/badge/許可證-MIT-red.svg)](LICENSE)
[![狀態](https://img.shields.io/badge/狀態-生產就緒-brightgreen.svg)](README.md)

## 📋 概述

這是教育材料轉換系統的核心引擎，提供完整的 Markdown 到 HTML 轉換功能。專為教育工作者設計，支援數學公式、圖片處理、主題系統和教育特殊元素。

### 🎯 核心特性

- ✅ **標準 Markdown 解析**: 完整支援 CommonMark 標準
- ✅ **LaTeX 數學公式**: 支援行內和顯示數學公式，整合 MathJax
- ✅ **智能圖片處理**: 支援尺寸控制、佈局管理和路徑處理
- ✅ **教育特殊元素**: 填空線、練習區塊、水平線統一
- ✅ **多主題系統**: 5種精心設計的教育主題
- ✅ **統一架構**: 單檔案解決方案，易於部署和維護
- ✅ **完整 API**: 命令行工具和 Python API
- ✅ **批量處理**: 支援多檔案和資料夾處理
- ✅ **錯誤處理**: 完整的錯誤報告和日誌系統

### 🏫 適用場景

- **中小學教師**: 製作教學材料和練習題
- **教育機構**: 批量處理教學文檔
- **內容創作者**: 製作包含數學公式的技術文檔
- **開發者**: 整合到教育平台和內容管理系統

## 🚀 快速開始

### 安裝方式

#### 方式一：直接使用（推薦）

```bash
# 1. 下載項目檔案
git clone https://github.com/education-converter/unified-converter.git
cd unified-converter

# 2. 安裝依賴
pip install -r requirements_unified.txt

# 3. 開始使用
python main_converter.py input.md
```

#### 方式二：安裝為 Python 包

```bash
# 安裝到系統
pip install -e .

# 使用命令行工具
edu-converter input.md
markdown-to-html input.md
```

### 基本使用

#### 1. 命令行工具（主要方式）

```bash
# 基本轉換
python main_converter.py input.md

# 指定輸出檔案和主題
python main_converter.py input.md -o output.html -t nature

# 批量處理資料夾
python main_converter.py folder/ -b -t tech

# PDF 優化模式
python main_converter.py input.md --pdf-mode

# 查看所有選項
python main_converter.py --help
```

#### 2. Python API 使用

```python
# 方式一：使用主程式 API
from main_converter import convert_file, convert_string

# 轉換檔案
result = convert_file("input.md", theme="nature", pdf_mode=True)

# 轉換字符串
markdown_content = "# 標題\n\n這是一段文字。"
result = convert_string(markdown_content, theme="tech")

if result.success:
    print("轉換成功！")
    print(f"標題: {result.title}")
    print(f"統計: {result.statistics}")
else:
    print("轉換失敗：", result.errors)
```

```python
# 方式二：直接使用核心轉換器
from markdown_to_html_unified import UnifiedMarkdownConverter, ConversionConfig

# 創建自定義配置
config = ConversionConfig(
    theme_name="nature",        # 主題選擇
    enable_math=True,           # 啟用數學公式
    enable_images=True,         # 啟用圖片處理
    enable_fill_blanks=True,    # 啟用填空線
    pdf_mode=False,             # PDF 模式
    include_mathjax=True,       # 包含 MathJax
    include_css=True            # 包含 CSS 樣式
)

# 創建轉換器並使用
converter = UnifiedMarkdownConverter(config)
result = converter.convert(markdown_content, "output.html")
```

## 🎨 主題系統

支援 5 種內建主題：

- **default**: 經典藍色主題
- **nature**: 自然綠色主題 🌿
- **tech**: 科技紫色主題 ⚡
- **space**: 太空藍色主題 🚀
- **sport**: 運動紅色主題 ⚽

## 🔢 數學公式支援

### 行內公式
```markdown
這是行內公式：$E = mc^2$
```

### 顯示公式
```markdown
$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
```

### 複雜公式
支援所有 LaTeX 數學語法，包括：
- 微積分符號
- 矩陣和行列式
- 希臘字母
- 上下標
- 分數和根號
- 化學公式

## 🖼️ 圖片處理

### 基本語法
```markdown
![圖片描述](path/to/image.jpg)
```

### 尺寸控制
```markdown
![圖片](image.jpg|width=300)
![圖片](image.jpg|height=200)
![圖片](image.jpg|size=50%)
```

### 佈局控制
```markdown
![左浮動](image.jpg|left)
![右浮動](image.jpg|right)
![居中](image.jpg|center)
```

### 組合使用
```markdown
![組合](image.jpg|width=400|center)
![複雜](image.jpg|size=60%|left)
```

## 📝 教育特殊元素

### 填空線
```markdown
姓名：____________________
年齡：____________________
```

會自動轉換為可列印的填空線。

### 水平線統一
連續的水平線會自動統一為 4 條：
```markdown
---
---
---
```

## 🧪 測試

運行測試腳本：

```bash
python test_unified_converter.py
```

測試包括：
- 基本轉換功能
- 所有主題效果
- 複雜數學公式
- 圖片尺寸控制
- 檔案轉換功能

## 📊 API 參考

### ConversionConfig

| 參數 | 類型 | 默認值 | 說明 |
|------|------|--------|------|
| `enable_math` | bool | True | 啟用數學公式處理 |
| `enable_images` | bool | True | 啟用圖片處理 |
| `enable_fill_blanks` | bool | True | 啟用填空線處理 |
| `enable_themes` | bool | True | 啟用主題系統 |
| `theme_name` | str | "default" | 主題名稱 |
| `pdf_mode` | bool | False | PDF 優化模式 |
| `include_mathjax` | bool | True | 包含 MathJax 腳本 |
| `include_css` | bool | True | 包含 CSS 樣式 |

### ConversionResult

| 屬性 | 類型 | 說明 |
|------|------|------|
| `html_content` | str | 生成的 HTML 內容 |
| `title` | str | 文檔標題 |
| `statistics` | dict | 統計信息 |
| `warnings` | list | 警告信息 |
| `errors` | list | 錯誤信息 |
| `success` | bool | 轉換是否成功 |

## 🔧 與現有系統的差異

### 優勢
1. **單檔案解決方案**: 所有功能整合在一個檔案中
2. **簡化的依賴**: 只需要 2 個外部庫
3. **統一的 API**: 一致的接口設計
4. **更好的錯誤處理**: 完整的錯誤和警告系統
5. **易於維護**: 清晰的代碼結構

### 保持的功能
- 所有現有的 Markdown 處理功能
- LaTeX 數學公式支援
- 圖片尺寸和佈局控制
- 教育特殊元素處理
- 多主題系統

## 🚧 未來擴展

這個統一轉換器為未來的功能擴展提供了良好的基礎：

1. **PDF 生成整合**: 可以輕鬆整合 PDF 生成功能
2. **更多主題**: 可以輕鬆添加新主題
3. **插件系統**: 可以添加插件支援
4. **性能優化**: 可以添加緩存和並行處理

## 📄 許可證

此項目遵循原有項目的許可證條款。
