#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
教育材料轉換系統 - 安裝配置

這個檔案定義了教育材料轉換系統的安裝配置，
包括依賴管理、入口點和項目元數據。
"""

from setuptools import setup, find_packages
from pathlib import Path

# 讀取 README 檔案
readme_file = Path(__file__).parent / "README_unified_converter.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 讀取依賴
requirements_file = Path(__file__).parent / "requirements_unified.txt"
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [
            line.strip() 
            for line in f 
            if line.strip() and not line.startswith('#')
        ]
else:
    requirements = [
        "markdown-it-py>=3.0.0,<4.0.0",
        "beautifulsoup4>=4.12.0,<5.0.0"
    ]

setup(
    name="education-material-converter",
    version="3.0.0",
    description="教育材料轉換系統 - 統一 Markdown 到 HTML 轉換器",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="教育材料轉換系統開發團隊",
    author_email="<EMAIL>",
    url="https://github.com/education-converter/unified-converter",
    
    # 包配置
    packages=find_packages(),
    py_modules=["markdown_to_html_unified", "main_converter"],
    
    # Python 版本要求
    python_requires=">=3.8",
    
    # 依賴
    install_requires=requirements,
    
    # 可選依賴
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
        ],
        "image": [
            "Pillow>=10.0.0",
        ],
        "fast": [
            "lxml>=4.9.0",
        ],
    },
    
    # 命令行入口點
    entry_points={
        "console_scripts": [
            "edu-converter=main_converter:main",
            "markdown-to-html=main_converter:main",
        ],
    },
    
    # 分類
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Education",
        "Intended Audience :: Developers",
        "Topic :: Education",
        "Topic :: Text Processing :: Markup :: Markdown",
        "Topic :: Text Processing :: Markup :: HTML",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    
    # 關鍵詞
    keywords="markdown html education converter latex math",
    
    # 項目 URL
    project_urls={
        "Bug Reports": "https://github.com/education-converter/unified-converter/issues",
        "Source": "https://github.com/education-converter/unified-converter",
        "Documentation": "https://education-converter.readthedocs.io/",
    },
    
    # 包含的數據檔案
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml"],
    },
    
    # 排除的檔案
    exclude_package_data={
        "": ["test_*", "*_test.py", "tests/*"],
    },
)
